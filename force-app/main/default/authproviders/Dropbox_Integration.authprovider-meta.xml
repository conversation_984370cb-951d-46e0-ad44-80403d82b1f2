<?xml version="1.0" encoding="UTF-8"?>
<AuthProvider xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorizeUrl>https://www.dropbox.com/oauth2/authorize?token_access_type=offline</authorizeUrl>
    <consumerKey>hhzkwsicka82mhy</consumerKey>
    <consumerSecret>Placeholder_Value</consumerSecret>
    <friendlyName>Dropbox Integration</friendlyName>
    <includeOrgIdInIdentifier>false</includeOrgIdInIdentifier>
    <isPkceEnabled>true</isPkceEnabled>
    <providerType>OpenIdConnect</providerType>
    <requireMfa>false</requireMfa>
    <sendAccessTokenInHeader>true</sendAccessTokenInHeader>
    <sendClientCredentialsInHeader>true</sendClientCredentialsInHeader>
    <sendSecretInApis>false</sendSecretInApis>
    <tokenUrl>https://api.dropboxapi.com/oauth2/token</tokenUrl>
</AuthProvider>
