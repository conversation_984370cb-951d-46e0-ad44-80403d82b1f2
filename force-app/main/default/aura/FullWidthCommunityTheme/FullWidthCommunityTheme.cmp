<aura:component implements="forceCommunity:themeLayout" access="global" description="MF Full Width Layout">
    <aura:attribute name="navBar" type="Aura.Component[]" required="false" />
    <aura:attribute name="headerContent" type="Aura.Component[]"/>
    <aura:attribute name="footerContent" type="Aura.Component[]"/>

    <aura:html tag="header" class="content theme-header-region">
        {!v.headerContent}
    </aura:html>
    <aura:html tag="body" class="content theme-body-region">
        {!v.body}
    </aura:html>
    <aura:html tag="footer" class="content theme-footer-region">
        {!v.footerContent}
    </aura:html>
</aura:component>