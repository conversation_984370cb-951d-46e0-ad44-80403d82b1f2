@isTest
public class CategorizationProcessorTest {
    @isTest
    static void testCategorizationProcessor() {
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Base_Url__c = 'https://api.test.com/',
            Customer_Id__c = '12345',
            Bearer_Token__c = 'testBearerToken'
        );
        insert config;
		
        /*List<Bank_Account__c> bankAccounts = [SELECT Name, Account_Id__c, Available_Balance__c, Is_Active__c FROM Bank_Account__c];
        List<Bank_Transaction__c> transactions = [SELECT Transaction_Id__c, Balance__c, Category__c FROM Bank_Transaction__c];*/
        Bank_Account__c bankAccount = new Bank_Account__c(
            Name = 'Test Bank Account',
            Request_Id__c = '123'
        );
        insert bankAccount;

        Bank_Transaction__c transaction1 = new Bank_Transaction__c(
            Bank_Account__c = bankAccount.Id,
            Transaction_Id__c = 'trans123',
            Category__c = null,
            Sub_Category__c = null
        );
        Bank_Transaction__c transaction2 = new Bank_Transaction__c(
            Bank_Account__c = bankAccount.Id,
            Transaction_Id__c = 'trans456',
            Category__c = null,
            Sub_Category__c = null
        );
        insert new List<Bank_Transaction__c>{transaction1, transaction2};

        Test.setMock(HttpCalloutMock.class, new MockHttpResponseForCategorizationProcessor());

        Test.startTest();
        CategorizationProcessor processor = new CategorizationProcessor('mockLoginId', '123');
        System.enqueueJob(processor);
        Test.stopTest();
    }

    // Mock HTTP response for the CategorizationProcessor
    private class MockHttpResponseForCategorizationProcessor implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setBody('{' +
                '"Transactions": [' +
                    '{"TransactionId": "trans123", "Category": "Category1", "SubCategory": "SubCategory1"},' +
                    '{"TransactionId": "trans456", "Category": "Category2", "SubCategory": "SubCategory2"}' +
                ']' +
            '}');
            return res;
        }
    }
}