@isTest
public class ProjectApiTest {

    @testSetup
    static void setupTestData() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        
        Opportunity opp1 = new Opportunity(Name = 'Test Opportunity 1', StageName = 'Prospecting', CloseDate = System.today().addDays(10), AccountId = testAccount.Id, Amount = 10000);
        Opportunity opp2 = new Opportunity(Name = 'Test Opportunity 2', StageName = 'Closed Won', CloseDate = System.today().addDays(5), AccountId = testAccount.Id, Amount = 20000);
        insert new List<Opportunity> { opp1, opp2 };
            
        Project__c proj = new Project__c(Name = '43546', MF_Loan_Amount__c = 3243, Date_to_Funded__c = System.today(), Loan_Opportunity__c = opp1.Id);
        insert proj;
    }

    @isTest
    static void testWithOpportunityId() {
        Opportunity opp = [SELECT Id FROM Opportunity WHERE Name = 'Test Opportunity 1' LIMIT 1];

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/opportunity/' + opp.Id;
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = new RestResponse();

        Test.startTest();
        ProjectAPI.getProjects();
        Test.stopTest();
    }

    @isTest
    static void testWithProjectId() {
        Project__c proj = [SELECT Id FROM Project__c WHERE Name = '43546' LIMIT 1];

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/opportunity/' + proj.Id;
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = new RestResponse();

        Test.startTest();
        ProjectAPI.getProjects();
        Test.stopTest();
    }
}