@SuppressWarnings('PMD.AvoidDuplicateLiterals, PMD.ExcessiveClassLength')
@IsTest
private class CashflowDataServiceTest {

    // Test Setup: Creates a comprehensive set of records for testing all scenarios.
    @TestSetup
    static void setupTestData() {
        // == Base Records ==
        Account acct = new Account(Name = 'Test Account');
        insert acct;

        Opportunity opp = new Opportunity(Name='Test Opp', StageName='Prospecting', CloseDate=Date.today());
        insert opp;

        Project__c proj = new Project__c(
            Name = 'Test Project',
            Account_Name__c = acct.Id,
            Loan_Opportunity__c = opp.Id,
            Loan_Status__c = 'Good Standing',
            MF_Loan_Amount__c = 500000,
            Loan_Principal__c = 450000,
            Accrued_Interest__c = 10000,
            Date_to_Funded__c = Date.today().addDays(-30),
            Loan_Maturity_Date__c = Date.today().addYears(5)
        );
        insert proj;

        // == Cashflow Versions ==
        // Active Cashflow
        Cashflow__c activeCashflow = new Cashflow__c(
            Name = 'Active Cashflow v2',
            Project__c = proj.Id,
            Is_Active__c = true,
            Version_Number__c = 2
        );
        insert activeCashflow;
        // Old Cashflow Version
        Cashflow__c oldCashflow = new Cashflow__c(
            Name = 'Old Cashflow v1',
            Project__c = proj.Id,
            Is_Active__c = false,
            Version_Number__c = 1
        );
        insert oldCashflow;


        // == Cashflow Line Items (Parents) for Active Cashflow ==
        List<Cashflow_Line_Item__c> lineItems = new List<Cashflow_Line_Item__c>{
            // Item 1: Will have children and related records
            new Cashflow_Line_Item__c(
                Cashflow__c = activeCashflow.Id,
                Week_Start_Date__c = Date.today(),
                Line_Item_Category__c = 'Material',
                Type__c = 'Financing Source',
                Planned_Amount__c = 10000
            ),
            // Item 2: Standalone item
            new Cashflow_Line_Item__c(
                Cashflow__c = activeCashflow.Id,
                Week_Start_Date__c = Date.today().addDays(7),
                Line_Item_Category__c = 'Labor',
                Planned_Amount__c = 5000
            ),
            // Item 3: To be filtered out by save logic
            new Cashflow_Line_Item__c(
                Cashflow__c = activeCashflow.Id,
                Week_Start_Date__c = Date.today().addDays(14),
                Line_Item_Category__c = 'Project Costs Paid That Week',
                Planned_Amount__c = 2500
            )
        };
        insert lineItems;

        // == Child Line Item and Junction ==
        Cashflow_Line_Item_Child__c childItem = new Cashflow_Line_Item_Child__c(
            Description__c = 'Child Item 1',
            Amount__c = 500
        );
        insert childItem;

        Cashflow_Line_Item_Junction__c junction = new Cashflow_Line_Item_Junction__c(
            Cashflow_Line_Item__c = lineItems[0].Id,
            Cashflow_Line_Item_Child__c = childItem.Id
        );
        insert junction;


        // == Disbursements and Junctions ==
        Disbursement__c disbursement = new Disbursement__c(Project__c = proj.Id, Name = 'Disbursement 1', Amount_Approved__c=1200);
        insert disbursement;
        Cashflow_Weekly_Line_Disbursement__c disbJunction = new Cashflow_Weekly_Line_Disbursement__c(
            Cashflow_Line_Item__c = lineItems[0].Id,
            Disbursement__c = disbursement.Id
        );
        insert disbJunction;

        // == Pay Applications and Junctions ==
        Pay_Application__c payApp = new Pay_Application__c(Project__c = proj.Id, Name = 'Pay App 1', Projected_payment__c = 800);
        insert payApp;
        Cashflow_Weekly_Line_Pay_Application__c payAppJunction = new Cashflow_Weekly_Line_Pay_Application__c(
            Cashflow_Line_Item__c = lineItems[0].Id,
            Pay_Application__c = payApp.Id
        );
        insert payAppJunction;

        // == Other Related Records ==
        Transaction__c transaction = new Transaction__c(
            Name = 'Transaction 1',
            Project__c = proj.Id,
            Type__c = 'Payment',
            Amount__c = 5000,
            Transaction_Date__c = Date.today()
        );
        insert transaction;
    }

    // Mocks the HTTP callout to the Excel parsing service.
    private class CashflowHttpCalloutMock implements HttpCalloutMock {
        private Integer statusCode;
        private String responseBody;

        public CashflowHttpCalloutMock(Integer code, String body) {
            this.statusCode = code;
            this.responseBody = body;
        }

        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setBody(this.responseBody);
            res.setStatusCode(this.statusCode);
            return res;
        }
    }

    @IsTest
    static void testGetCashflowData_Success_Full() {
        // Arrange: Get IDs from setup data
        Project__c testProj = [SELECT Id FROM Project__c WHERE Name = 'Test Project' LIMIT 1];
        Cashflow__c activeCashflow = [SELECT Id FROM Cashflow__c WHERE Name = 'Active Cashflow v2' LIMIT 1];

        Test.startTest();
        // Act: Call the main data retrieval method
        CashflowDataService.CashflowPageData result = CashflowDataService.getCashflowData(testProj.Id, activeCashflow.Id);
        Test.stopTest();

        // Assert: Verify all data structures are populated correctly
        System.assertNotEquals(null, result, 'Result wrapper should not be null.');
        System.assertEquals(testProj.Id, result.project.Id, 'Project Id should match.');
        System.assertNotEquals(null, result.account, 'Related Account should be fetched.');
        System.assertNotEquals(null, result.opp, 'Related Opportunity should be fetched.');
        System.assertEquals(2, result.allCashflows.size(), 'Should retrieve all cashflow versions for the project.');

        System.assertEquals(3, result.forecastLines.size(), 'Should return all 3 forecast lines.');
        System.assertEquals(1, result.transactions.size(), 'Should return 1 transaction.');
        System.assertEquals(1, result.disbursements.size(), 'Should return 1 disbursement.');
        System.assertEquals(1, result.payApplications.size(), 'Should return 1 pay application.');

        // Assert Stitching: Verify that child and related objects are correctly nested
        Boolean childFound = false;
        Boolean disbursementFound = false;
        Boolean payAppFound = false;
        for(Cashflow_Line_Item__c cli : result.forecastLines) {
            if(cli.Cashflow_Line_Item_Child__r != null && !cli.Cashflow_Line_Item_Child__r.isEmpty()){
                childFound = true;
                System.assertEquals(1, cli.Cashflow_Line_Item_Child__r.size(), 'Should have one stitched child record.');
                System.assertEquals('Child Item 1', cli.Cashflow_Line_Item_Child__r[0].Description__c);
            }
            if(cli.Cashflow_Weekly_Line_Disbursements__r != null && !cli.Cashflow_Weekly_Line_Disbursements__r.isEmpty()){
                disbursementFound = true;
                System.assertEquals(1, cli.Cashflow_Weekly_Line_Disbursements__r.size(), 'Should have one stitched disbursement junction.');
            }
             if(cli.Cashflow_Weekly_Line_Pay_Applications__r != null && !cli.Cashflow_Weekly_Line_Pay_Applications__r.isEmpty()){
                payAppFound = true;
                System.assertEquals(1, cli.Cashflow_Weekly_Line_Pay_Applications__r.size(), 'Should have one stitched pay application junction.');
            }
        }
        System.assert(childFound, 'Stitched child records were not found.');
        System.assert(disbursementFound, 'Stitched disbursement records were not found.');
        System.assert(payAppFound, 'Stitched pay application records were not found.');
    }

    @IsTest
    static void testGetCashflowData_NullCashflowId_FindsActive() {
        Project__c testProj = [SELECT Id FROM Project__c LIMIT 1];
        Cashflow__c activeCashflow = [SELECT Id FROM Cashflow__c WHERE Is_Active__c = true LIMIT 1];

        Test.startTest();
        // Act: Call with null cashflowId to trigger active lookup
        CashflowDataService.CashflowPageData result = CashflowDataService.getCashflowData(testProj.Id, null);
        Test.stopTest();

        // Assert
        System.assertNotEquals(null, result, 'Result should not be null.');
        System.assertNotEquals(null, result.activeCashflow, 'An active cashflow should have been found.');
        System.assertEquals(activeCashflow.Id, result.activeCashflow.Id, 'The correct active cashflow should be fetched.');
    }
    
    @IsTest
    static void testGetCashflowData_InvalidInputs() {
        // Test with null Project ID
        try {
            CashflowDataService.getCashflowData(null, null);
            System.assert(false, 'Exception should be thrown for null project ID.');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Project ID is required'), 'Correct exception message for null project ID.');
        }

        // Test with a non-existent Project ID
        Id fakeProjectId = Id.valueOf('a00000000000001AAA');
         try {
            CashflowDataService.getCashflowData(fakeProjectId, null);
            System.assert(false, 'Exception should be thrown for invalid project ID.');
        } catch (AuraHandledException e) {
            System.assert(e.getMessage().contains('Project not found'), 'Correct exception message for invalid project ID.');
        }
    }

    @IsTest
    static void testGetAllAccessibleFields_EdgeCases() {
        // Test with blank SObject name
        List<String> fields = CashflowDataService.getAllAccessibleFields('');
        System.assertEquals(0, fields.size(), 'Should return an empty list for blank SObject name.');

        // Test with invalid SObject name (should be handled by try-catch)
        fields = CashflowDataService.getAllAccessibleFields('NonExistentObject__c');
        System.assertEquals(0, fields.size(), 'Should return an empty list for invalid SObject name.');
    }

    @IsTest
    static void testSaveCashflowDetails_AllOperations() {
        // Arrange: Get existing records to update and delete
        Cashflow_Line_Item__c parentToUpdate = [SELECT Id, Planned_Amount__c FROM Cashflow_Line_Item__c WHERE Line_Item_Category__c = 'Labor' LIMIT 1];
        Cashflow_Line_Item__c parentToDelete = [SELECT Id FROM Cashflow_Line_Item__c WHERE Line_Item_Category__c = 'Material' LIMIT 1];
        Cashflow_Line_Item_Child__c childToDelete = [SELECT Id FROM Cashflow_Line_Item_Child__c LIMIT 1];
        
        // Prepare lists for the service method
        parentToUpdate.Planned_Amount__c = 9999;
        List<Cashflow_Line_Item__c> parentsToUpsert = new List<Cashflow_Line_Item__c>{
            parentToUpdate,
            new Cashflow_Line_Item__c(Cashflow__c = parentToUpdate.Cashflow__c, Line_Item_Category__c = 'New Item', Planned_Amount__c = 100),
            new Cashflow_Line_Item__c(Cashflow__c = parentToUpdate.Cashflow__c, Line_Item_Category__c = 'Project Costs Paid That Week', Planned_Amount__c = 50)
        };
        List<Id> parentIdsToDelete = new List<Id>{ parentToDelete.Id };
        List<Cashflow_Line_Item_Child__c> childrenToUpsert = new List<Cashflow_Line_Item_Child__c>{ new Cashflow_Line_Item_Child__c(Amount__c=123) };
        List<Id> childIdsToDelete = new List<Id>{ childToDelete.Id };
        
        Test.startTest();
        // Act: Call the save method
        CashflowDataService.saveCashflowDetails(parentsToUpsert, parentIdsToDelete, childrenToUpsert, childIdsToDelete);
        Test.stopTest();
        
        // Assert: Verify DML operations were successful
        Integer deletedParents = [SELECT COUNT() FROM Cashflow_Line_Item__c WHERE Id = :parentToDelete.Id];
        System.assertEquals(0, deletedParents, 'Parent record should have been deleted.');
        
        Integer deletedChildren = [SELECT COUNT() FROM Cashflow_Line_Item_Child__c WHERE Id = :childToDelete.Id];
        System.assertEquals(0, deletedChildren, 'Child record should have been deleted.');

        Cashflow_Line_Item__c updatedParent = [SELECT Planned_Amount__c FROM Cashflow_Line_Item__c WHERE Id = :parentToUpdate.Id];
        System.assertEquals(9999, updatedParent.Planned_Amount__c, 'Parent record should have been updated.');

        // Verify the filtered category was NOT inserted
        Integer filteredCount = [SELECT COUNT() FROM Cashflow_Line_Item__c WHERE Line_Item_Category__c = 'Project Costs Paid That Week'];
        System.assertEquals(0, filteredCount, 'Record with category "Project Costs Paid That Week" should have been filtered out and not inserted.');

        Integer newParents = [SELECT COUNT() FROM Cashflow_Line_Item__c WHERE Line_Item_Category__c = 'New Item'];
        System.assertEquals(1, newParents, 'A new parent record should have been created.');
        
        Integer newChildren = [SELECT COUNT() FROM Cashflow_Line_Item_Child__c WHERE Amount__c=123];
        System.assertEquals(1, newChildren, 'A new child record should have been created.');
    }
    
    @IsTest
    static void testSaveAsCashflow_WithEditsAndDeletes() {
        // Arrange: Get IDs from setup
        Cashflow__c originalCashflow = [SELECT Id, Project__c FROM Cashflow__c WHERE Is_Active__c = true LIMIT 1];
        Cashflow_Line_Item__c lineItemToEdit = [SELECT Id FROM Cashflow_Line_Item__c WHERE Line_Item_Category__c = 'Labor' LIMIT 1];
        Cashflow_Line_Item__c lineItemToDelete = [SELECT Id FROM Cashflow_Line_Item__c WHERE Line_Item_Category__c = 'Material' LIMIT 1];

        // Prepare changes for the new version
        List<Id> parentItemIdsToDelete = new List<Id>{ lineItemToDelete.Id };
        List<Cashflow_Line_Item__c> parentItemsToUpsert = new List<Cashflow_Line_Item__c>{
            new Cashflow_Line_Item__c(Id = lineItemToEdit.Id, Planned_Amount__c = 7777), // Edit
            new Cashflow_Line_Item__c(Line_Item_Category__c = 'New From SaveAs', Planned_Amount__c = 8888) // New
        };
        
        Test.startTest();
        // Act: Call the clone/saveAs method with all parameters
        Cashflow__c newVersion = CashflowDataService.saveAsCashflow(
            originalCashflow.Id,
            parentItemsToUpsert,
            parentItemIdsToDelete,
            'Version 3.0'
        );
        Test.stopTest();
        
        // Assert: Verify the new cashflow version and its line items
        System.assertNotEquals(null, newVersion, 'A new cashflow version should be created.');
        System.assertEquals('Version 3.0', newVersion.version_Name__c, 'Version name should be set.');
        System.assertEquals(3, newVersion.Version_Number__c, 'Version number should be incremented to 3.');

        List<Cashflow_Line_Item__c> newVersionItems = [SELECT Planned_Amount__c, Line_Item_Category__c FROM Cashflow_Line_Item__c WHERE Cashflow__c = :newVersion.Id];
        // Original: 3 items. Delete 1, Edit 1 of the remaining 2, Add 1 new. Should be 3 total.
        System.assertEquals(3, newVersionItems.size(), 'New version should have the correct number of line items.');

        Boolean editFound = false;
        Boolean newFound = false;
        for(Cashflow_Line_Item__c item : newVersionItems){
            if(item.Line_Item_Category__c == 'Labor') {
                System.assertEquals(7777, item.Planned_Amount__c, 'The edited item should have the new amount.');
                editFound = true;
            }
             if(item.Line_Item_Category__c == 'New From SaveAs') {
                System.assertEquals(8888, item.Planned_Amount__c, 'The new item should exist with its amount.');
                newFound = true;
            }
        }
        System.assert(editFound, 'The edited line item was not found in the new version.');
        System.assert(newFound, 'The new line item was not found in the new version.');

        // Verify snapshot logic also ran. Amount = 1200 (disb) + 800 (payapp) = 2000
        Cashflow_Line_Item__c snapshottedItem = [SELECT Planned_Amount__c FROM Cashflow_Line_Item__c WHERE Id = :lineItemToDelete.Id];
        System.assertEquals(2000, snapshottedItem.Planned_Amount__c, 'Original Financing Source item should be snapshotted.');
    }

    @IsTest
    static void testUploadAndParseExcel_Success() {
        // Arrange: Mock a successful response
        Test.setMock(HttpCalloutMock.class, new CashflowHttpCalloutMock(200, '{"status":"success", "data":[]}'));
        
        Test.startTest();
        // Act
        String response = CashflowDataService.uploadAndParseExcel('dGVzdA==');
        Test.stopTest();
        
        // Assert
        System.assert(response.contains('"status":"success"'), 'Should return the success response from the mock.');
    }

    @IsTest
    static void testUploadAndParseExcel_Error() {
        // Arrange: Mock an error response
        Test.setMock(HttpCalloutMock.class, new CashflowHttpCalloutMock(500, '{"error":"Internal Server Error"}'));
        
        Test.startTest();
        try {
            // Act
            CashflowDataService.uploadAndParseExcel('dGVzdA==');
            System.assert(false, 'An exception should have been thrown for a non-2xx response.');
        } catch (AuraHandledException e) {
            // Assert
            System.assert(e.getMessage().contains('Error from parse-xls (500)'), 'The correct error message should be thrown.');
        }
        Test.stopTest();
    }
}