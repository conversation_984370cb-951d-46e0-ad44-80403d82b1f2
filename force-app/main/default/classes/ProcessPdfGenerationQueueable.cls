public class ProcessPdfGenerationQueueable implements Queueable, Database.AllowsCallouts {

    // Member variables to hold data passed from the invocable method
    private Id recordId;
    private String agreementDateStr;
    private String borrowerName;
    private String advanceDateStr;
    private String advanceAmountStr;
    private String purpose;
    private String borrowerEntityName;
    private String signatoryName;
    private String signatoryTitle;
    private String contentDownloadUrl;
    // private Id originalUserId; // Keep if needed for complex permission scenarios

    // Constructor to receive data
    public ProcessPdfGenerationQueueable(Id recordId, String agreementDateStr, String borrowerName,
                                         String advanceDateStr, String advanceAmountStr, String purpose,
                                         String borrowerEntityName, String signatoryName, String signatoryTitle,
                                         String contentDownloadUrl /*, Id originalUserId */ ) {

        this.recordId = recordId;
        this.agreementDateStr = agreementDateStr;
        this.borrowerName = borrowerName;
        this.advanceDateStr = advanceDateStr;
        this.advanceAmountStr = advanceAmountStr;
        this.purpose = purpose;
        this.borrowerEntityName = borrowerEntityName;
        this.signatoryName = signatoryName;
        this.signatoryTitle = signatoryTitle;
        this.contentDownloadUrl = contentDownloadUrl;
		
                                             // --- Nebula Logging ---
        String logTag = 'Advanced Request PDF Generation'; // Define tag for consistency
        Nebula.Logger.info('Queueable Constructor - recordId -> ' + recordId).addTag(logTag);
        Nebula.Logger.info('Queueable Constructor - agreementDateStr -> ' + agreementDateStr).addTag(logTag);
        Nebula.Logger.info('Queueable Constructor - borrowerName -> ' + borrowerName).addTag(logTag);
        Nebula.Logger.info('Queueable Constructor - advanceDateStr -> ' + advanceDateStr).addTag(logTag);
        Nebula.Logger.info('Queueable Constructor - advanceAmountStr -> ' + advanceAmountStr).addTag(logTag);
        Nebula.Logger.info('Queueable Constructor - purpose -> ' + purpose).addTag(logTag);
        Nebula.Logger.info('Queueable Constructor - borrowerEntityName -> ' + borrowerEntityName).addTag(logTag);
        Nebula.Logger.info('Queueable Constructor - signatoryName -> ' + signatoryName).addTag(logTag);
        Nebula.Logger.info('Queueable Constructor - signatoryTitle -> ' + signatoryTitle).addTag(logTag);
        Nebula.Logger.info('Queueable Constructor - contentDownloadUrl -> ' + contentDownloadUrl).addTag(logTag);
        // --- End Nebula Logging ---

    }

    // The execute method contains the core processing logic
    public void execute(QueueableContext context) {
        System.debug('Queueable job execute started for Record ID: ' + this.recordId);

        Id targetNetworkId = null;

        try {
            List<Network> networks = [SELECT Id FROM Network WHERE Name = 'Mobilization Funding' LIMIT 1];
            if (!networks.isEmpty()) {
                targetNetworkId = networks[0].Id;
                System.debug('Queueable: Found Target Network ID: ' + targetNetworkId);
            }
        } catch (Exception e) {
            System.debug('Queueable: Error querying Network: ' + e.getMessage());
        }
        // --- End Determine NetworkId ---


        PageReference pdfPage = Page.DisbursementPerjuryDoc; 

        // Pass parameters needed by the CONTROLLER CONSTRUCTOR
        pdfPage.getParameters().put('agreementDate', this.agreementDateStr);
        pdfPage.getParameters().put('borrowerName', this.borrowerName);
        pdfPage.getParameters().put('advanceDate', this.advanceDateStr);
        pdfPage.getParameters().put('advanceAmount', this.advanceAmountStr);
        pdfPage.getParameters().put('purpose', this.purpose);
        pdfPage.getParameters().put('borrowerEntityName', this.borrowerEntityName);
        pdfPage.getParameters().put('signatoryName', this.signatoryName);
        pdfPage.getParameters().put('signatoryTitle', this.signatoryTitle);
        if (String.isNotBlank(this.contentDownloadUrl)) {
             // Pass the *decoded* URL as the controller expects it this way
            pdfPage.getParameters().put('contentDownloadUrlParam', EncodingUtil.urlDecode(this.contentDownloadUrl, 'UTF-8'));
        } else {
            pdfPage.getParameters().put('contentDownloadUrlParam', null);
        }

        Blob pdfBlob;
        try {
            // Render VF page as PDF. This runs the controller constructor.
            if (Test.isRunningTest()) {
                pdfBlob = Blob.valueOf('Test PDF Content from Queueable');
            } else {
                pdfBlob = pdfPage.getContentAsPDF();
            }
            System.debug('Queueable: Successfully generated PDF blob for Record ID: ' + this.recordId + '. Blob size: ' + (pdfBlob != null ? pdfBlob.size() : 0));

        } catch (Exception e) {
            System.debug('FATAL ERROR: Queueable: getContentAsPDF failed for Record ID ' + this.recordId + ': ' + e.getMessage() + ' Stack: ' + e.getStackTraceString());
            Nebula.Logger.error('FATAL ERROR: Queueable: getContentAsPDF failed for Record ID ' + this.recordId + ': ' + e.getMessage() + ' Stack: ' + e.getStackTraceString()).addTag('Advanced Request PDF Generation');
            Nebula.Logger.saveLog();
            return; // Exit if PDF generation failed
        }

        // --- Generate Ideal Name ---
        Date advanceDt;
        String formattedAdvanceDate = Date.today().format(); // Default format if parsing fails
        if (String.isNotBlank(this.advanceDateStr)) {
            try {
                DateTime dt = DateTime.parse(this.advanceDateStr + ' 00:00:00');
                advanceDt = dt.date();
                formattedAdvanceDate = advanceDt.year() + '-' + String.valueOf(advanceDt.month()).leftPad(2, '0') + '-' + String.valueOf(advanceDt.day()).leftPad(2, '0');
            } catch (Exception e) {
                System.debug('Queueable: Could not parse advanceDate (' + this.advanceDateStr + '). Using fallback. Error: ' + e.getMessage());
                advanceDt = Date.today();
                formattedAdvanceDate = advanceDt.year() + '-' + String.valueOf(advanceDt.month()).leftPad(2, '0') + '-' + String.valueOf(advanceDt.day()).leftPad(2, '0');
            }
        } else {
            advanceDt = Date.today();
            formattedAdvanceDate = advanceDt.year() + '-' + String.valueOf(advanceDt.month()).leftPad(2, '0') + '-' + String.valueOf(advanceDt.day()).leftPad(2, '0');
        }
        String borrower = String.isBlank(this.borrowerName) ? 'Unknown Borrower' : this.borrowerName;
        String idealTitle = 'Advance Request - ' + borrower + ' - ' + formattedAdvanceDate;
        String sanitizedForPath = idealTitle.replaceAll('[^a-zA-Z0-9\\s\\-_\\.]', '');
        String idealPath = sanitizedForPath.replaceAll('\\s+', '-') + '.pdf';
        System.debug('Queueable: Generated Ideal Title: ' + idealTitle);
        System.debug('Queueable: Generated Ideal PathOnClient: ' + idealPath);

        // --- Create ContentVersion ---
        // Using the separate insert CV then insert CDL approach which worked last time
        ContentVersion cv = new ContentVersion();
        cv.Title = idealTitle;
        cv.PathOnClient = idealPath;
        cv.VersionData = pdfBlob;
        cv.Origin = 'C';
        if (targetNetworkId != null) {
             cv.NetworkId = targetNetworkId; // Set NetworkId if found
             System.debug('Queueable: Setting NetworkId to ' + targetNetworkId);
        } else {
             System.debug('Queueable: Proceeding without setting NetworkId.');
        }
        // ** REMOVED ** cv.FirstPublishLocationId = this.recordId; // Link later with CDL

        try {
            // Insert ContentVersion first
            Database.SaveResult cvResult = Database.insert(cv, false); // Use partial success

            if (!cvResult.isSuccess()) {
                 System.debug('FATAL ERROR: Queueable: Failed to insert ContentVersion. Errors: ' + cvResult.getErrors());
                 for(Database.Error err : cvResult.getErrors()) {
                     System.debug('CV Error Field: ' + err.getFields() + ' | Msg: ' + err.getMessage() + ' | Code: ' + err.getStatusCode());
                 }
                 return; // Stop if CV insert fails
            } else {
                 System.debug('Queueable: Inserted ContentVersion Id: ' + cvResult.getId() + ' for Record ID: ' + this.recordId);

                 // Get ContentDocumentId
                 Id contentDocumentId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cvResult.getId() LIMIT 1].ContentDocumentId;

                 // Create ContentDocumentLink
                 ContentDocumentLink cdl = new ContentDocumentLink();
                 cdl.ContentDocumentId = contentDocumentId;
                 cdl.LinkedEntityId = this.recordId;
                 cdl.ShareType = 'V';
                 cdl.Visibility = 'AllUsers'; // Adjust as needed

                 Database.SaveResult cdlResult = Database.insert(cdl, false); // Use partial success

                 if (!cdlResult.isSuccess()) {
                     System.debug('FATAL ERROR: Queueable: Failed to insert ContentDocumentLink. Errors: ' + cdlResult.getErrors());
                     for(Database.Error err : cdlResult.getErrors()) {
                         System.debug('CDL Error Field: ' + err.getFields() + ' | Msg: ' + err.getMessage() + ' | Code: ' + err.getStatusCode());
                     }
                 } else {
                     System.debug('Queueable: Successfully created ContentDocumentLink for Record ID: ' + this.recordId + ', ContentDocumentId: ' + contentDocumentId);
                 }
            }
        } catch (Exception e) { // Catch unexpected exceptions during DML or Query
            System.debug('FATAL ERROR: Queueable: Unexpected exception during file processing for Record ID ' + this.recordId + ': ' + e.getMessage() + ' Stack: ' + e.getStackTraceString());
            Nebula.Logger.error('FATAL ERROR: Queueable: Unexpected exception during file processing for Record ID ' + this.recordId + ': ' + e.getMessage() + ' Stack: ' + e.getStackTraceString()).addTag('Advanced Request PDF Generation');
            Nebula.Logger.saveLog();
            
        }
        System.debug('Queueable job execute finished for Record ID: ' + this.recordId);
    }
}