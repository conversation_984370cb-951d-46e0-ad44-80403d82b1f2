@SuppressWarnings('PMD')  
@RestResource(urlMapping='/SubmitDisbursementRequest/')
global without sharing class SubmitDisbursementRequestService {
    
    @HttpPost
    global static ResponseWrapper handleDisbursementRequest() {
        ResponseWrapper response = new ResponseWrapper();

        Savepoint sp = Database.setSavepoint();
        
        try {
            RestRequest req = RestContext.request;
            String jsonInput = req.requestBody.toString();
            Nebula.Logger.info('input -> ' + jsonInput).addTag('Disbursement Request API');

            DisbursementRequestWrapper wrapper = (DisbursementRequestWrapper) JSON.deserialize(jsonInput, DisbursementRequestWrapper.class);
            
            DisbursementRequestWrapper.DisbursementRequest dr = wrapper.disbursementRequest;
            Payee_Information__c payeeInfo;
            
            if (dr.payeeId != null) {
                // load existing
                Id payeeInfoId = Id.valueOf(dr.payeeId);
                payeeInfo = [
                    SELECT Id,
                           Account_Name__c, Bank_Account_Number__c, Bank_Name__c,
                           Bank_Routing_Number__c, Street__c, City__c,
                           State_Province__c, Country__c, Zip_Postal_Code__c,
                           Payee_Contact_Email__c, Payee_Contact_Name__c,
                           Payee_Name__c, Payee_Phone__c,
                           Payment_Method__c, Expense_Type__c, Project__c
                    FROM Payee_Information__c
                    WHERE Id = :payeeInfoId
                    LIMIT 1
                ];
                
                // only overwrite when incoming value is present
                if (String.isNotBlank(dr.Account_Name)) {
                    payeeInfo.Account_Name__c = dr.Account_Name;
                }
                if (String.isNotBlank(dr.Bank_Account_Number)) {
                    payeeInfo.Bank_Account_Number__c = dr.Bank_Account_Number;
                }
                if (String.isNotBlank(dr.Bank_Name)) {
                    payeeInfo.Bank_Name__c = dr.Bank_Name;
                }
                if (String.isNotBlank(dr.Bank_Routing_Number)) {
                    payeeInfo.Bank_Routing_Number__c = dr.Bank_Routing_Number;
                }
                
                DisbursementRequestWrapper.PayeeAddress addr = dr.payeeAddress;
                if (addr != null) {
                    if (String.isNotBlank(addr.street)) {
                        payeeInfo.Street__c = addr.street;
                    }
                    if (String.isNotBlank(addr.city)) {
                        payeeInfo.City__c = addr.city;
                    }
                    if (String.isNotBlank(addr.state)) {
                        payeeInfo.State_Province__c = addr.state;
                    }
                    if (String.isNotBlank(addr.country)) {
                        payeeInfo.Country__c = addr.country;
                    }
                    if (String.isNotBlank(addr.postalCode)) {
                        payeeInfo.Zip_Postal_Code__c = addr.postalCode;
                    }
                }
                
                if (String.isNotBlank(dr.payeeContactEmail)) {
                    payeeInfo.Payee_Contact_Email__c = dr.payeeContactEmail;
                }    
                if (dr.payeeContactName != null &&
                    (String.isNotBlank(dr.payeeContactName.firstName) || String.isNotBlank(dr.payeeContactName.lastName))) {
                	payeeInfo.Payee_Contact_Name__c =
                        (String.isNotBlank(dr.payeeContactName.firstName) ? dr.payeeContactName.firstName : '') +
                        ((String.isNotBlank(dr.payeeContactName.firstName) && String.isNotBlank(dr.payeeContactName.lastName)) ? ' ' : '') +
                        (String.isNotBlank(dr.payeeContactName.lastName)  ? dr.payeeContactName.lastName  : '');
                }
                if (dr.payeeContactName != null && String.isNotBlank(dr.payeeContactName.firstName)) {
                    payeeInfo.Payee_First_Name__c = dr.payeeContactName.firstName;
                }
                if (dr.payeeContactName != null && String.isNotBlank(dr.payeeContactName.lastName)) {
                    payeeInfo.Payee_Last_Name__c = dr.payeeContactName.lastName;
                }
                if (String.isNotBlank(dr.payeeName)) {
                    payeeInfo.Payee_Name__c = dr.payeeName;
                }
                if (String.isNotBlank(dr.payeePhone)) {
                    payeeInfo.Payee_Phone__c = dr.payeePhone;
                }
                if (String.isNotBlank(dr.paymentMethod)) {
                    payeeInfo.Payment_Method__c = dr.paymentMethod;
                }
                if (String.isNotBlank(dr.expenseType)) {
                    payeeInfo.Expense_Type__c = dr.expenseType;
                }
                if (String.isNotBlank(dr.projectLookup)) {
                    payeeInfo.Project__c = dr.projectLookup;
                }
                
                if (String.isNotBlank(dr.Mail_Check_To)) {
                    payeeInfo.Mail_Check_To__c = dr.Mail_Check_To;
                }
                
                update payeeInfo;
                Nebula.Logger.info('Updated Payee_Information__c Id: ' + payeeInfo.Id)
                             .addTag('Disbursement Request API');
                
            } else {
                // insert new—set whatever was passed
                payeeInfo = new Payee_Information__c(
                    Account_Name__c            = dr.Account_Name,
                    Bank_Account_Number__c     = dr.Bank_Account_Number,
                    Bank_Name__c               = dr.Bank_Name,
                    Bank_Routing_Number__c     = dr.Bank_Routing_Number,
                    Street__c                  = dr.payeeAddress.street,
                    City__c                    = dr.payeeAddress.city,
                    State_Province__c          = dr.payeeAddress.state,
                    Country__c                 = dr.payeeAddress.country,
                    Zip_Postal_Code__c         = dr.payeeAddress.postalCode,
                    Payee_Contact_Email__c     = dr.payeeContactEmail,
                    Payee_Contact_Name__c      = dr.payeeContactName.firstName + ' ' + dr.payeeContactName.lastName,
                    Payee_First_Name__c        = dr.payeeContactName.firstName,
                    Payee_Last_Name__c         = dr.payeeContactName.lastName,
                    Payee_Name__c              = dr.payeeName,
                    Payee_Phone__c             = dr.payeePhone,
                    Payment_Method__c          = dr.paymentMethod,
                    Expense_Type__c            = dr.expenseType,
                    Project__c                 = dr.projectLookup,
                    Mail_Check_To__c           = dr.Mail_Check_To
                );
                insert payeeInfo;
                Nebula.Logger.info('Inserted Payee_Information__c Id: ' + payeeInfo.Id)
                             .addTag('Disbursement Request API');
            }
            
            Disbursement_Request__c disReq = processDisbursementRequest(wrapper, payeeInfo);
            insert disReq;
            
            List<Requested_Item__c> requestedItems = processRequestedItems(wrapper.itemDescriptions, disReq.Id);
                        
            response.requestedItemIds = new List<Id>(); 
            List<Requested_Item__c> reqItemList = new List<Requested_Item__c>();
            for (Requested_Item__c item : requestedItems){
                response.requestedItemIds.add(item.Id);
                reqItemList.add(item);
            }
            
            sendEmailOfDisbursementRequestFormDetails(disReq.Id, reqItemList);
            
            response.status = 'success';
            response.message = 'Disbursement request processed successfully.';
            response.disbursementRequestId = disReq.Id;
        
    	} catch (Exception e) {

            Database.rollback(sp);
            
            response.status = 'error';
            response.status = 'An error occurred: ' + e.getMessage();
            response.stackTrace = e.getStackTraceString();
            Nebula.Logger.error('Error - ' + e.getMessage() + '--' + e.getStackTraceString()).addTag('Disbursement Request API');
            Nebula.Logger.saveLog();
        }
        
        return response;
    }
    
    public static Disbursement_Request__c processDisbursementRequest(DisbursementRequestWrapper wrapper, Payee_Information__c payeeInfo) {
        Disbursement_Request__c dr = new Disbursement_Request__c();
        
        dr.Account_Name__c            = payeeInfo.Account_Name__c;
        dr.Bank_Account_Number__c     = payeeInfo.Bank_Account_Number__c;
        dr.Bank_Name__c               = payeeInfo.Bank_Name__c;
        dr.Bank_Routing_Number__c     = payeeInfo.Bank_Routing_Number__c;
        dr.Street_Address__c          = payeeInfo.Street__c;
        dr.City__c                    = payeeInfo.City__c;
        dr.State__c                   = payeeInfo.State_Province__c;
        dr.Country__c                 = payeeInfo.Country__c;
        dr.Zip_Code__c                = payeeInfo.Zip_Postal_Code__c;
        dr.Payee_Contact_Email__c     = payeeInfo.Payee_Contact_Email__c;
        dr.Payee_Name__c              = payeeInfo.Payee_Name__c;
        dr.Phone__c                   = payeeInfo.Payee_Phone__c;
        dr.Payment_Method__c          = payeeInfo.Payment_Method__c;
        dr.Payee_Contact_Name__c 	  = payeeInfo.Payee_Contact_Name__c;
        dr.Payee_Information__c		  = payeeInfo.Id;
        
        dr.Comments__c                         = wrapper.verifyAndSubmit.additionalCommentsSpecialInstructions;
        dr.Disbursement__c                     = wrapper.disbursementRequest.Disbursement_Number;
        dr.Issue_Check_To__c                   = wrapper.disbursementRequest.Mail_Check_To;
        dr.Requester_Email__c                  = wrapper.verifyAndSubmit.requesterEmail;
        dr.General_Contractor_Contract_Owner__c = wrapper.verifyAndSubmit.PROJECTOWNER_Lookup;
        dr.Requester_Name__c                   = wrapper.verifyAndSubmit.signature;
        dr.Status__c                           = 'New';
        
        dr.Disbursement_Type__c = wrapper.disbursementRequest.expenseType;
        dr.Project_Lookup__c    = wrapper.disbursementRequest.projectLookup;
        
        if (wrapper.disbursementRequest.loanNumberLookup != null) {
            Opportunity opp = [
                SELECT Name 
                FROM Opportunity 
                WHERE Id = :wrapper.disbursementRequest.loanNumberLookup 
                LIMIT 1
            ];
            dr.Loan_Number__c = opp.Name;
        }
        
        
        return dr;
    }
    
    public static List<Requested_Item__c> processRequestedItems(List<DisbursementRequestWrapper.ItemDescription> items, Id disbursementRequestId) {
        List<Requested_Item__c> requestedItems = new List<Requested_Item__c>();
        
        for (DisbursementRequestWrapper.ItemDescription item : items) {
            Requested_Item__c requestedItem = new Requested_Item__c();
            requestedItem.Name = item.Item;
            requestedItem.Description_Work__c = item.Description_Work;
            requestedItem.Invoice_Date__c = Date.valueOf(item.invoiceDate);
            requestedItem.Invoice_Amount__c = Decimal.valueOf(item.invoiceAmount.replace('$', '').replace(',', ''));
            requestedItem.Invoice__c = item.invoice;
            requestedItem.Invoice_Due_Date__c = Date.valueOf(item.invoiceDueDate);
            requestedItem.Disbursement_Request__c = disbursementRequestId; 
            
            requestedItems.add(requestedItem);
        }
        
        if (!requestedItems.isEmpty()) {
            insert requestedItems;
        }
        
        return requestedItems;
    }
    
    public static void sendEmailOfDisbursementRequestFormDetails(Id disReqId, List<Requested_Item__c> ReqItemId){
        sendEmailOfDisbursementReqForm.inputVariables inputVar = new sendEmailOfDisbursementReqForm.inputVariables();
        inputVar.disbursementReqId = disReqId;
        inputVar.requestedItemIds = ReqItemId;
        
        List<sendEmailOfDisbursementReqForm.inputVariables> inputList = new List<sendEmailOfDisbursementReqForm.inputVariables>();
        inputList.add(inputVar);

        List<sendEmailOfDisbursementReqForm.outputVariables> outputList = sendEmailOfDisbursementReqForm.sendEmailOfDisbursementRequestForm(inputList);

        for (sendEmailOfDisbursementReqForm.outputVariables outputVar : outputList) {
            System.debug('success: ' + outputVar.result);
        }
    }
    
    global class ResponseWrapper {
        public String status;
        public String message;
        public String stackTrace;
        public Id disbursementRequestId;
        public List<Id> requestedItemIds;
    }
}