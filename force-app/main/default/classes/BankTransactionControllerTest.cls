@SuppressWarnings('PMD')
@isTest
    private class BankTransactionControllerTest {

        @TestSetup
        static void makeData(){
            Account acc = new Account(Name='Test Account');
            insert acc;

            Bank_Account__c bankAcc = new Bank_Account__c(
                Name='Test Bank Account',
                Account__c = acc.Id
            );
            insert bankAcc;

            List<Bank_Transaction__c> transactions = new List<Bank_Transaction__c>();
            for(Integer i = 0; i < 5; i++) {
                transactions.add(new Bank_Transaction__c(
                    Bank_Account__c = bankAcc.Id,
                    Transaction_Date__c = Date.today().addDays(-i),
                    Description__c = 'Test Description ' + i,
                    Credit__c = 5,
                    Debit__c = 0,
                    Category__c = 'Test Category ' + i,
                    MF_Category__c = 'Travel',
                    MF_Category_Manual_Override__c = 'Payroll',
					Transaction_Id__c = i+'_id'
                ));
            }
            insert transactions;
        }

        @isTest
        static void testGetTransactionFields() {
            Test.startTest();
            List<BankTransactionController.FieldOption> fieldOptions = BankTransactionController.getTransactionFields();
            Test.stopTest();

            System.assertNotEquals(null, fieldOptions, 'Field options should not be null.');
            System.assert(fieldOptions.size() > 0, 'Should return at least one field option.');

            if (fieldOptions.size() > 1) {
                System.assert(fieldOptions[0].label.compareTo(fieldOptions[1].label) <= 0, 'Fields should be sorted by label');
            }
        }

        @isTest
        static void testGetTransactions_ByBankAccount() {
            Bank_Account__c bankAcc = [SELECT Id FROM Bank_Account__c WHERE Name='Test Bank Account' LIMIT 1];

            Test.startTest();
            List<Bank_Transaction__c> transactions = BankTransactionController.getTransactions(bankAcc.Id, 'Bank_Account__c');
            Test.stopTest();

            System.assertNotEquals(null, transactions, 'Transactions list should not be null.');
            System.assertEquals(5, transactions.size(), 'Should return 5 transactions for the bank account.');
        }

        @isTest
        static void testGetTransactions_ByAccount() {
            Account acc = [SELECT Id FROM Account WHERE Name='Test Account' LIMIT 1];

            Test.startTest();
            List<Bank_Transaction__c> transactions = BankTransactionController.getTransactions(acc.Id, 'Account');
            Test.stopTest();

            System.assertNotEquals(null, transactions, 'Transactions list should not be null.');
            System.assertEquals(5, transactions.size(), 'Should return 5 transactions for the account.');
        }

         @isTest
        static void testGetTransactions_ByAccount_NoBankAccounts() {
            Account accWithoutBanks = new Account(Name='No Banks Account');
            insert accWithoutBanks;

            Test.startTest();
            List<Bank_Transaction__c> transactions = BankTransactionController.getTransactions(accWithoutBanks.Id, 'Account');
            Test.stopTest();

            System.assertNotEquals(null, transactions, 'Transactions list should not be null.');
            System.assertEquals(0, transactions.size(), 'Should return 0 transactions for the account with no bank accounts.');
        }


        @isTest
        static void testGetTransactions_UnsupportedObject() {
            Account acc = [SELECT Id FROM Account WHERE Name='Test Account' LIMIT 1];

            Test.startTest();
            List<Bank_Transaction__c> transactions = BankTransactionController.getTransactions(acc.Id, 'Contact');
            Test.stopTest();

            System.assertNotEquals(null, transactions, 'Transactions list should not be null.');
            System.assertEquals(0, transactions.size(), 'Should return 0 transactions for an unsupported object type.');
        }


         @isTest
        static void testGetTransactions_InferObjectType() {
            Bank_Account__c bankAcc = [SELECT Id FROM Bank_Account__c WHERE Name='Test Bank Account' LIMIT 1];

            Test.startTest();
            List<Bank_Transaction__c> transactions = BankTransactionController.getTransactions(bankAcc.Id, null);
            Test.stopTest();

            System.assertNotEquals(null, transactions, 'Transactions list should not be null when inferring type.');
            System.assertEquals(5, transactions.size(), 'Should return 5 transactions when inferring type from Bank Account Id.');
        }

        @isTest
        static void testSaveTransactions_Success() {
            List<Bank_Transaction__c> transactionsToUpdate = [SELECT Id, Description__c, Credit__c FROM Bank_Transaction__c LIMIT 2];
            List<Object> draftValues = new List<Object>();

            Map<String, Object> draft1 = new Map<String, Object>();
            draft1.put('Id', transactionsToUpdate[0].Id);
            draft1.put('Description__c', 'Updated Description 1');
            draft1.put('Credit__c', 999.99);
            draftValues.add(draft1);

            Map<String, Object> draft2 = new Map<String, Object>();
            draft2.put('Id', transactionsToUpdate[1].Id);
            draft2.put('Description__c', 'Updated Description 2');
            draftValues.add(draft2);


            Test.startTest();
            String resultMessage = BankTransactionController.saveTransactions(draftValues);
            Test.stopTest();

            System.assert(resultMessage.contains('2 record(s) saved successfully.'), 'Success message not as expected. Received: ' + resultMessage);
            
        }

        @isTest
        static void testSaveTransactions_PartialSuccess() {
            // Assumes a validation rule exists or tries to update a non-updateable field implicitly handled by the controller
            List<Bank_Transaction__c> transactionsToUpdate = [SELECT Id, Name FROM Bank_Transaction__c LIMIT 2];
            List<Object> draftValues = new List<Object>();

            // Valid update
            Map<String, Object> draft1 = new Map<String, Object>();
            draft1.put('Id', transactionsToUpdate[0].Id);
            draft1.put('Description__c', 'Partially Successful Update');
            draftValues.add(draft1);

            // Invalid update (trying to update Name which is excluded)
            Map<String, Object> draft2 = new Map<String, Object>();
            draft2.put('Id', transactionsToUpdate[1].Id);
            draft2.put('Name', 'TryingToUpdateName'); // This field is in nonUpdateableFields set
            draft2.put('Description__c', 'This should not save if Name is the only change'); // Add a valid field too
            draftValues.add(draft2);

            Test.startTest();
            String resultMessage = BankTransactionController.saveTransactions(draftValues);
            Test.stopTest();

            System.assert(resultMessage.contains('2 record(s) saved successfully.'), 'Expected 2 successful saves as invalid field is ignored. Received: ' + resultMessage);
            System.assert(!resultMessage.contains('failed'), 'Result message should not indicate failures if invalid fields are just ignored. Received: ' + resultMessage);


            
        }

		@isTest
        static void testSaveTransactions_NoValidChanges() {
            List<Bank_Transaction__c> transactionsToUpdate = [SELECT Id, Name FROM Bank_Transaction__c LIMIT 1];
            List<Object> draftValues = new List<Object>();

            // Invalid update (trying to update only Name which is excluded)
            Map<String, Object> draft1 = new Map<String, Object>();
            draft1.put('Id', transactionsToUpdate[0].Id);
            draft1.put('Name', 'TryingToUpdateName'); // This field is in nonUpdateableFields set
            draftValues.add(draft1);

            Test.startTest();
            String resultMessage = BankTransactionController.saveTransactions(draftValues);
            Test.stopTest();

            System.assertEquals('No valid changes detected to save.', resultMessage, 'Message for only invalid fields not correct.');
        }


        @isTest
        static void testSaveTransactions_MissingId() {
            List<Object> draftValues = new List<Object>();
            Map<String, Object> draft1 = new Map<String, Object>();
            // draft1.put('Id', 'some_id'); // Intentionally missing Id
            draft1.put('Description__c', 'Update With Missing Id');
            draftValues.add(draft1);

            Test.startTest();
            String resultMessage = BankTransactionController.saveTransactions(draftValues);
            Test.stopTest();

            // The current controller code adds to an error list but returns 'No valid changes' if that's the only issue.
            System.assert(resultMessage.contains('No valid changes detected to save.'), 'Message should indicate no valid changes. Received: ' + resultMessage);
            System.assert(resultMessage.contains('Cannot update a record missing an ID'), 'Message should contain the specific error. Received: ' + resultMessage);

        }

        @isTest
        static void testFieldOptionComparable() {
            BankTransactionController.FieldOption optionA = new BankTransactionController.FieldOption('Apple', 'Fruit__c', 'text');
            BankTransactionController.FieldOption optionB = new BankTransactionController.FieldOption('Banana', 'Fruit__c', 'text');
            BankTransactionController.FieldOption optionA2 = new BankTransactionController.FieldOption('Apple', 'Veg__c', 'text');
            BankTransactionController.FieldOption optionNull = new BankTransactionController.FieldOption(null, 'Unknown__c', 'text');

            System.assert(optionA.compareTo(optionB) < 0, 'Apple should come before Banana');
            System.assert(optionB.compareTo(optionA) > 0, 'Banana should come after Apple');
            System.assertEquals(0, optionA.compareTo(optionA2), 'Apples should be equal regardless of API name');
            System.assert(optionNull.compareTo(optionA) < 0, 'Null label should come before Apple');
             System.assert(optionA.compareTo(optionNull) > 0, 'Apple should come after Null label');
        }
    }