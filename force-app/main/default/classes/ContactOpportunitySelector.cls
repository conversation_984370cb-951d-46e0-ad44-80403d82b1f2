@SuppressWarnings('PMD')
public without sharing class ContactOpportunitySelector {
  @AuraEnabled(cacheable = true)
  public static Map<String, String> getAccountDetails() {
      Map<String, String> resultMap = new Map<String, String>();
      String userId = UserInfo.getUserId();
      System.debug('userId: ' + userId);
      
      if (userId != null) {
          User currentUser = [SELECT Id, ContactId, Contact.AccountId FROM User WHERE Id = :userId LIMIT 1];
          Id contactId = currentUser.ContactId;
          
          if (contactId != null) {
              Id accountId = currentUser.Contact.AccountId;
              resultMap.put('accountId', accountId);
              
              List<Opportunity> opportunities = [
                  SELECT Id, Name, StageName, Date_to_Funded__c, CreatedDate, 
                        Assigned_MF_CRM__c, Assigned_MF_CRM__r.FirstName, Assigned_MF_CRM__r.Email, Assigned_MF_Servicer__c, Assigned_MF_UW__c, Assigned_MF_Servicer__r.FirstName,Assigned_MF_Servicer__r.Email
                  FROM Opportunity
                  WHERE AccountId = :accountId
                  AND StageName IN ('Ready to fund', 'Holding Pattern', 'Closed Won','Funded')
                  ORDER BY Date_to_Funded__c NULLS LAST, CreatedDate ASC
                  LIMIT 1
              ];

              if(!opportunities.isEmpty() && opportunities[0].Assigned_MF_Servicer__c != null){
                  Opportunity matchedOpportunity = opportunities[0];
                  resultMap.put('firstOppId', matchedOpportunity.Id);
                  //resultMap.put('Assigned_MF_CRM', String.valueOf(matchedOpportunity.Assigned_MF_CRM__c));
                  resultMap.put('Assigned_MF_CRM', String.valueOf(matchedOpportunity.Assigned_MF_CRM__r.FirstName));
                  resultMap.put('Assigned_MF_CRM_Email', String.valueOf(matchedOpportunity.Assigned_MF_CRM__r.Email));
                  //resultMap.put('MF_Assigned_UW', String.valueOf(matchedOpportunity.Assigned_MF_UW__c));
                  resultMap.put('status', 'Matching Opportunity found');
              }
              else{
                  // No matching opportunities, fetch details from Account
                  System.debug('No matching opportunities found. Fetching from Account.');
                  Account account = [
                      SELECT Assigned_MF_CRM__c, MF_Assigned_Servicer__c, MF_Assigned_UW__c,MF_Assigned_UW__r.FirstName,MF_Assigned_Servicer__r.FirstName,Assigned_MF_CRM__r.FirstName,  MF_Assigned_UW__r.Email,MF_Assigned_Servicer__r.Email,Assigned_MF_CRM__r.Email
                      FROM Account
                      WHERE Id = :accountId
                      LIMIT 1
                  ];

                  //resultMap.put('Assigned_MF_CRM', String.valueOf(account.Assigned_MF_CRM__r.FirstName));
                  resultMap.put('MF_Assigned_Servicer', String.valueOf(account.MF_Assigned_Servicer__r.FirstName));
                  //resultMap.put('MF_Assigned_UW', String.valueOf(account.MF_Assigned_UW__r.FirstName));

                  //resultMap.put('MF_Assigned_UW_Email', String.valueOf(account.MF_Assigned_UW__r.Email));
                  resultMap.put('MF_Assigned_Servicer_Email', String.valueOf(account.MF_Assigned_Servicer__r.Email));
                  //resultMap.put('Assigned_MF_CRM_Email', String.valueOf(account.Assigned_MF_CRM__r.Email));

                  resultMap.put('status', 'No matching Opportunities, using Account values');
              }
          }
      } else {
          resultMap.put('status', 'Non-Community User');
      }

      System.debug('resultMap ' + resultMap);
      return resultMap;
  }

  @AuraEnabled(cacheable = true)
  public static Map<String, String> selectOpportunity() {
    //System.debug('contactId: ' + contactId);
    Map<String, String> resultMap = new Map<String, String>();
    String userId = UserInfo.getUserId();
    System.debug('userId: ' + userId);
    if (userId != null) {
      // Query User to get ContactId
      User currentUser = [ SELECT Id, ContactId, Contact.AccountId FROM User WHERE Id = : userId LIMIT 1 ];
      Id contactId = currentUser.ContactId;
      System.debug('contactId: ' + contactId);

      if (contactId != null) {
        Id accountId = currentUser.Contact.AccountId;
        resultMap.put('accountId', accountId);
        
        System.debug('accountId ' + accountId);

        if (accountId != null) {

          List<Opportunity> opportunities = [SELECT Id, Name, StageName, Status_Update_for_Client__c FROM Opportunity WHERE AccountId = :accountId ];
          System.debug('opportunities ' + opportunities);

          if (opportunities.isEmpty()) {
            System.debug('No opportunities found for the community user.');
            resultMap.put('status', 'No Opportunities');
          } else {
            Id firstOpp = opportunities[0].Id;
            System.debug('Opportunities found for the community user:');
            resultMap.put('firstOppId', firstOpp); 

            Boolean hasAwardedOpportunity = false;
            Boolean allDeclined = true; 
            for (Opportunity opp : opportunities) {
              if (opp.Status_Update_for_Client__c != 'Declined' ) {
                allDeclined = false;
              }
              if (opp.StageName == 'Funded') {
                hasAwardedOpportunity = true;
                break;
              }
            } 
            if (allDeclined) {
                System.debug('All opportunities have Status_Update_for_Client__c = Declined.');
                resultMap.put('status', 'No Opportunities');
            } else if (hasAwardedOpportunity) {
              System.debug(
                  'At least one awarded opportunity found for the non-community user.');
              resultMap.put('status', 'Awarded Opportunities Found');
            } else {
              System.debug(
                  'No awarded opportunities found for the non-community user.');
              resultMap.put('status', 'No Awarded Opportunities');
            }
          }
        }
      }
    } else {
      System.debug('This is a non-community user.');
      resultMap.put('status', 'Non-Community User');
    }

    System.debug('resultMap ' + resultMap);
    return resultMap;
  }
}