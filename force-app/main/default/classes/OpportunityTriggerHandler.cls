@SuppressWarnings('PMD')
public class OpportunityTriggerHandler {
    
    public static void sendEmailOnStatusChange(List<Opportunity> newOppList, Map<Id,Opportunity> oldOppMap) {
        if(!newOppList.isEmpty()) {
            Map<String,Set<String>> statusToEmailMap = new Map<String,Set<String>>();
            Set<Id> accountIds = new Set<Id>();

            //get account ids
            for (Opportunity opp : newOppList) {
                if (opp.AccountId != null && opp.Status_Update_for_Client__c != oldOppMap.get(opp.Id).Status_Update_for_Client__c) {
                    accountIds.add(opp.AccountId);
                }
            }

            // fetch related users of Accounts
            Map<Id, Set<String>> accountUserEmailsMap = new Map<Id, Set<String>>();
            if (!accountIds.isEmpty()) {
                for (User u : [SELECT Id, Email, AccountId FROM User WHERE AccountId IN :accountIds AND isActive = true]) {
                    if (u.Email != null) {
                        if (!accountUserEmailsMap.containsKey(u.AccountId)) {
                            accountUserEmailsMap.put(u.AccountId, new Set<String>());
                        }
                        accountUserEmailsMap.get(u.AccountId).add(u.Email);
                    }
                }
            }

            // Build the status-to-email mapping
            for (Opportunity opp : newOppList) {
                if (opp.Status_Update_for_Client__c != oldOppMap.get(opp.Id).Status_Update_for_Client__c) {
                    String status = opp.Status_Update_for_Client__c;
                    Set<String> emails = accountUserEmailsMap.get(opp.AccountId);
                    
                    if (emails != null && !emails.isEmpty()) {
                        if (!statusToEmailMap.containsKey(status)) {
                            statusToEmailMap.put(status, new Set<String>());
                        }
                        statusToEmailMap.get(status).addAll(emails);
                    }
                }
            }

            //Send emails....
            List<String> pickListValues = getOpportunityStatusPicklistValues();
            Set<String> oppStatusSet = statusToEmailMap.keySet();
            String subject = 'Update on Your Loan Status';
            for(String status : oppStatusSet) {
                
                String htmlBody = GenerateHTML.createPaths(pickListValues, status);
                String body = [SELECT HtmlValue FROM EmailTemplate WHERE name = 'Alert Client For Status Update Template'].HtmlValue;
                body = body.replace('<p>&lt;&lt;Paths&gt;&gt;</p>',htmlBody);
                body = body.replace('&lt;&lt;Status&gt;&gt;',status);
                System.debug('body- >' + body);
				SYstem.debug('subject -> ' + subject);
                sendMail(statusToEmailMap.get(status),subject,body);
            }
        }
    }
    
    public static void mapStageToStatusUpdate(List<Opportunity> oppList, Map<Id,Opportunity> oldOppMap) {
        
		Map<String, String> stageToStatusMap = new Map<String, String>();            
        for (StageName_Mapping_with_Client_Status__mdt mapping : [SELECT StageName__c, Status_Update_for_Client__c FROM StageName_Mapping_with_Client_Status__mdt]) {
            stageToStatusMap.put(mapping.StageName__c, mapping.Status_Update_for_Client__c);
        }
      
        for (Opportunity opp : oppList) {
            if (oldOppMap != null && oldOppMap.containsKey(opp.Id)) {
                Opportunity oldOpp = oldOppMap.get(opp.Id);
                if (oldOpp.StageName != opp.StageName) { 
                    if (stageToStatusMap.containsKey(opp.StageName)) {
                        opp.Status_Update_for_Client__c = stageToStatusMap.get(opp.StageName);
                    }
                }
            } else { 
                if (stageToStatusMap.containsKey(opp.StageName)) {
                    opp.Status_Update_for_Client__c = stageToStatusMap.get(opp.StageName);
                }
            }
        }
    }
    
    public static void createActivity(List<opportunity> opps,Map<Id,Opportunity> oldMap){
        
        User usr = [SELECT Id,contactId,AccountId FROM User WHERE Id =: userinfo.getUserId()];
        
        
        List<Activity_Logger__c> activities = new List<Activity_Logger__c>();
        
        for(opportunity opp : opps){
            if(oldMap == null){
                activities.add(new Activity_Logger__c(Item__c=opp.name,Related_Record__c = opp.Id,Account__c = opp.AccountId,Activity_Time__c = system.now(),Activity_Type__c = 'Loan Submitted',Contact__c=usr.contactId,User__c=usr.Id));
            }else if(opp.Status_Update_for_Client__c != oldMap.get(opp.Id).Status_Update_for_Client__c){
                activities.add(new Activity_Logger__c(Item__c=opp.name,Related_Record__c = opp.Id,Account__c = opp.AccountId,Activity_Time__c = system.now(),Activity_Type__c = 'Loan Updated',Contact__c=usr.contactId,User__c=usr.Id));
            }
        }
        
        if(!activities.isEmpty()){
            insert activities;
        }
        
        
    }
    
    public static void sendMail(Set<String> address, String subject, String body) {
        List<Messaging.SingleEmailMessage> emailList = new List<Messaging.SingleEmailMessage>();
        List<String> addresses = new List<String>(address);
        SYstem.debug('addresses- -> ' +addresses);
            
       	Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setSubject(subject);
        mail.setHtmlBody(body);
        mail.setToAddresses(addresses);
        emailList.add(mail);
        
        if(!emailList.isEmpty()){
            List<Messaging.SendEmailResult> smr = Messaging.sendEmail(emailList);
            System.debug(smr[0].isSuccess());
            System.debug(smr[0].getErrors());
            System.debug('Email Sent Successfully');
        }	   
    }
    
    public static List<String> getOpportunityStatusPicklistValues() {
        // Describe the Status field on the Opportunity object
        Schema.DescribeFieldResult fieldResult = Opportunity.Status_Update_for_Client__c.getDescribe();

        // Retrieve the picklist values
        List<Schema.PicklistEntry> picklistValues = fieldResult.getPicklistValues();

        // Store the active picklist values in a list
        List<String> statusValues = new List<String>();
        for (Schema.PicklistEntry picklistValue : picklistValues) {
            if (picklistValue.isActive()) {
                statusValues.add(picklistValue.getLabel());
            }
        }
        return statusValues;
    }
    
}