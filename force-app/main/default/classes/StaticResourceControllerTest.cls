@IsTest
private class StaticResourceControllerTest {

    @IsTest
    static void testGetStaticContentByNameWithExpectedName() {
        // Given the understanding there's a StaticResource named "Styles"
        Test.startTest();
        List<String> names = new List<String>{'Styles'};
        List<String> results = StaticResourceController.getStaticContentByName(names);
        Test.stopTest();
        
        // Verify the method returns a result, content verification is out of scope
        System.assertNotEquals(null, results, 'Results should not be null when querying an expected static resource.');
        System.assertNotEquals(new List<String>(), results, 'Results should not be empty for an existing resource.');
    }

    @IsTest
    static void testGetStaticContentByNameWithInvalidName() {
        // Attempt to retrieve a non-existing static resource
        Test.startTest();
        List<String> names = new List<String>{'NonexistentResource'};
        List<String> results = StaticResourceController.getStaticContentByName(names);
        Test.stopTest();
        
        // Verify
        System.assertEquals(0, results.size(), 'Results should be null for a nonexistent resource.');
    }

    @IsTest
    static void testGetStaticContentByNameWithNullInput() {
        // Passing null as the method argument
        Test.startTest();
        List<String> results = StaticResourceController.getStaticContentByName(null);
        Test.stopTest();
        
        // Verify
        System.assertEquals(null, results, 'Results should be null when input is null.');
    }

    @IsTest
    static void testGetStaticContentByNameWithEmptyList() {
        // Passing an empty list as the method argument
        Test.startTest();
        List<String> results = StaticResourceController.getStaticContentByName(new List<String>());
        Test.stopTest();
        
        // Verify
        System.assertEquals(null, results, 'Results should be null when input list is empty.');
    }
}