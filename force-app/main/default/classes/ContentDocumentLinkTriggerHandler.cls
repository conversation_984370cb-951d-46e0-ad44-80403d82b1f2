@SuppressWarnings('PMD')
public without sharing class ContentDocumentLinkTriggerHandler {
    public static void handleAfterInsert(List<ContentDocumentLink> newRecords) {
        Set<Id> contentDocumentIds = new Set<Id>();
        for (ContentDocumentLink cdObj : newRecords) {
            contentDocumentIds.add(cdObj.ContentDocumentId);
        }

        Map<Id, ContentDocument> contentDocumentMap = new Map<Id, ContentDocument>(
            [SELECT Id, Title, FileType, CreatedById, ContentAssetId  FROM ContentDocument WHERE Id IN :contentDocumentIds]
        );

        ContentDocumentLink selectedCdObj = null;

        for (ContentDocumentLink cdObjTemp : newRecords) {
            ContentDocument cdDocu = contentDocumentMap.get(cdObjTemp.ContentDocumentId);
            if (cdDocu != null && cdDocu.FileType != 'SNote' && cdDocu.ContentAssetId == null) {
                String sobj = cdObjTemp.LinkedEntityId.getSObjectType().getDescribe().getName();
                if (sobj != 'User') {
                    selectedCdObj = cdObjTemp;
                    break;
                }
            }
        }

        if (selectedCdObj != null) {
            handleFileUpload(selectedCdObj, contentDocumentMap);
        }
    }

    private static void handleFileUpload(ContentDocumentLink selectedCdObj, Map<Id, ContentDocument> contentDocumentMap) {
        String folderPath = '';
        ContentDocument cdDocu = contentDocumentMap.get(selectedCdObj.ContentDocumentId);
        String fileName = cdDocu != null ? cdDocu.Title : 'Unknown File';
        String sObjName = selectedCdObj.LinkedEntityId.getSObjectType().getDescribe().getName();
        String fileUrl = '';

        User currentUser = [SELECT Id, Name, AccountId, Account.Name, ContactId FROM User WHERE Id = :cdDocu.CreatedById LIMIT 1];
        String accName = '';
        String userName = currentUser.AccountId != null ? 'Client' : 'User';
        String sObjNameForPath = '';
        String parentRecordName = '';

        Activity_Logger__c al = new Activity_Logger__c(Related_Record__c = selectedCdObj.LinkedEntityId, Activity_Time__c = System.now(), Activity_Type__c = 'File Uploaded', Contact__c = currentUser.ContactId, User__c = currentUser.Id);

        switch on sObjName {
            when 'Opportunity' {
                sObjNameForPath = '/MF Team Folder/Salesforce Back-Ups/Applications/';
                Opportunity opp = [SELECT Id, Name, Account.Name, AccountId FROM Opportunity WHERE Id = :selectedCdObj.LinkedEntityId];
                al.Account__c = opp.AccountId;
                al.Item__c = opp.Name;
                parentRecordName = opp.Name;
                accName = opp.Account.Name;
            }
            when 'Disbursement_Request__c' {
                sObjNameForPath = '/MF Team Folder/Salesforce Back-Ups/Disbursements/';
                Disbursement_Request__c dr = [SELECT Id, Name, Project_lookup__r.Account_Name__r.Name, Project_lookup__r.Loan_Opportunity__r.AccountId FROM Disbursement_Request__c WHERE Id = :selectedCdObj.LinkedEntityId];
                al.Item__c = dr.Name;
                al.Account__c = dr.Project_lookup__r.Loan_Opportunity__r.AccountId;
                parentRecordName = dr.Name;
                accName = dr.Project_lookup__r.Account_Name__r.Name;
            }
            when 'Project__c' {
                sObjNameForPath = '/MF Team Folder/Salesforce Back-Ups/Projects/';
                Project__c proj = [SELECT Id, Name, Account_Name__r.Name, Loan_Opportunity__r.AccountId FROM Project__c WHERE Id = :selectedCdObj.LinkedEntityId];
                al.Item__c = proj.Name;
                al.Account__c = proj.Loan_Opportunity__r.AccountId;
                parentRecordName = proj.Name;
                accName = proj.Account_Name__r.Name;
            }
        }

        if (al.Related_Record__c != null) {
            if(!FeedItemTriggerHandler.hasInsertedChatterPost){
                insert al;
            }
        } else {
            System.debug('Insufficient permissions to insert Activity_Logger__c.');
        }

        fileUrl = sObjNameForPath + userName + '.' + accName + '.' + parentRecordName + '.';

        List<ContentVersion> contentVersions = [
            SELECT Id, ContentDocumentId, Dropbox_Url__c
            FROM ContentVersion
            WHERE ContentDocumentId = :selectedCdObj.ContentDocumentId
        ];

        for (ContentVersion cv : contentVersions) {
            cv.Dropbox_Url__c = fileUrl;
        }

        if (!contentVersions.isEmpty()) {
            update contentVersions;
        } else {
            System.debug('Insufficient permissions to update ContentVersion or no ContentVersions found.');
        }

        //DropboxUploadBatch batch = new DropboxUploadBatch(selectedCdObj.LinkedEntityId, fileUrl);
        //Database.executeBatch(batch);

        //DropboxUploadQueueable job = new DropboxUploadQueueable(selectedCdObj.LinkedEntityId, fileUrl);
		//System.enqueueJob(job);
		
		if (!Test.isRunningTest()) {
            DropboxUploadFuture.uploadToDropbox(selectedCdObj.LinkedEntityId, fileUrl);
        }
        
    }
}