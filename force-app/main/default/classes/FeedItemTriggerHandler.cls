@SuppressWarnings('PMD')
public without sharing class FeedItemTriggerHandler {
    public static Boolean hasInsertedActivities = false;
     public static Boolean hasInsertedChatterPost = false;

    public static void handleAfterInsert(List<FeedItem> newFeedItems) {
        System.debug('FeedItemTriggerHandler handleAfterInsert -> ');
        
        
        if (newFeedItems == null || newFeedItems.isEmpty()) {
            return;
        }
        
        List<Activity_Logger__c> activities = new List<Activity_Logger__c>();
        
        List<Chatter_Post_Tracker__c> postTrackers = new List<Chatter_Post_Tracker__c>();
        List<Chatter_Post_Tracker__c> updatedTrackers = new List<Chatter_Post_Tracker__c>();
        Set<Id> communityUserIds = new Set<Id>();
        
        Set<Id> userIds = new Set<Id>();
        for (FeedItem feed : newFeedItems) {
            userIds.add(feed.CreatedById);
        }
        
        Map<Id, User> userMap = new Map<Id, User>([SELECT Id, ContactId, AccountId FROM User WHERE Id IN :userIds]);
        
        Set<Id> relatedOpportunityIds = new Set<Id>();
        Set<Id> relatedProjectIds = new Set<Id>();
        Set<Id> relatedDisbursementRequestIds = new Set<Id>();
        
        for (FeedItem feed : newFeedItems) {
            /*if (userMap.containsKey(feed.CreatedById)) {
                User user = userMap.get(feed.CreatedById);
                
                if (user.ContactId != null) {
                    communityUserIds.add(feed.CreatedById);*/
                    
                    //Set<String> projectPrefixes = new Set<String>{'a09', 'a00'};
                    if (feed.ParentId != null && String.valueOf(feed.ParentId).startsWith('006')) {
                        relatedOpportunityIds.add(feed.ParentId);
                    }
                    else if (feed.ParentId != null && ((feed.ParentId).getSObjectType().getDescribe().getName()) == 'Project__c') {
                        relatedProjectIds.add(feed.ParentId);
                    }
                    /*else if (feed.ParentId != null && projectPrefixes.contains(String.valueOf(feed.ParentId).substring(0, 3))) {
                    	relatedProjectIds.add(feed.ParentId);
                    }*/
                    else if (feed.ParentId != null && ((feed.ParentId).getSObjectType().getDescribe().getName()) == 'Disbursement_Request__c'){
                        relatedDisbursementRequestIds.add(feed.ParentId);
                    }
                /*}
            }*/
        }
        
        @SuppressWarnings('PMD.ApexCRUDViolation')
        Map<Id, Opportunity> opportunityMap = relatedOpportunityIds.isEmpty() ? new Map<Id, Opportunity>() : 
        new Map<Id, Opportunity>([SELECT Id, OwnerId,name,AccountId, Account.OwnerId FROM Opportunity WHERE Id IN :relatedOpportunityIds]);
        
        Map<Id, Project__c> projectMap = relatedProjectIds.isEmpty() ? new Map<Id, Project__c>() : 
        new Map<Id, Project__c>([SELECT Id, OwnerId,name,Loan_Opportunity__r.AccountId,Loan_Opportunity__r.Account.OwnerId FROM Project__c WHERE Id IN :relatedProjectIds]);
        
        Map<Id, Disbursement_Request__c> disbursementRequestMap = relatedDisbursementRequestIds.isEmpty() ? new Map<Id, Disbursement_Request__c>() : 
        new Map<Id, Disbursement_Request__c>([SELECT Id, OwnerId,name,Project_lookup__r.Loan_Opportunity__r.AccountId,Project_lookup__r.Loan_Opportunity__r.Account.OwnerId FROM Disbursement_Request__c WHERE Id IN :relatedDisbursementRequestIds]);
        
        Set<Id> accountIds = new Set<Id>();
        for (User u : userMap.values()) {
            if (u.AccountId != null) {
                accountIds.add(u.AccountId);
            }
        }
        Map<Id, Account> accountMap = accountIds.isEmpty() ? new Map<Id, Account>() : 
        new Map<Id, Account>([SELECT Id, OwnerId FROM Account WHERE Id IN :accountIds]);
        
        for (FeedItem feed : newFeedItems) {
            if(feed.Visibility == 'AllUsers') {
                User user = userMap.get(feed.CreatedById);
                
                String  feedCmmt = ChatterPostTrackerController.removeTags(feed.Body);

                Pattern pattern = Pattern.compile('@([A-Za-z]+(?: [A-Za-z]+)*)');
				Matcher matcher = pattern.matcher(feedCmmt);
				System.debug('matcher: ' + matcher);
				List<String> mentionUsers = new List<String>();
                while (matcher.find()) {
                    mentionUsers.add(matcher.group(1));
                }
                
                System.debug('mentionUsers: ' + mentionUsers);
                
                Boolean hasExternalUser = false;
                if (!mentionUsers.isEmpty()) {
                
                	List<User> mentionedUsersFetch = [SELECT Id, Name, AccountId FROM User WHERE Name IN :mentionUsers];
            
                    for (User usr : mentionedUsersFetch) {
                        if (usr.AccountId != null) {
                            hasExternalUser = true;
                            break; 
                        }
                    }
                }

                // Id accountOwner;
                // String relatedAccount;

                //Internal User
            // else 
                //if (user.ContactId == null) { 
                    
                List<Chatter_Post_Tracker__c> trackers = [
                    SELECT Id, Status__c FROM Chatter_Post_Tracker__c
                    WHERE (Related_Opportunity__c = :feed.ParentId OR
                        Related_Project__c = :feed.ParentId OR
                        Related_Disbursement_Request__c = :feed.ParentId)
                    AND Status__c = 'New'
                    ORDER BY CreatedDate DESC LIMIT 1
                ];

                Activity_Logger__c al = new Activity_Logger__c(
                    Related_Record__c = feed.ParentId,
                    Activity_Time__c = System.now(),
                    Contact__c = user.ContactId,
                    User__c = user.Id,
                    Comment_Body__c = feedCmmt
                );

                Id accountOwner;
                String relatedAccount;
                    
                if (!trackers.isEmpty()) {
                    Chatter_Post_Tracker__c tracker = trackers[0];
                    tracker.Status__c = 'Responded';
                    tracker.Answered_By__c = feed.CreatedById;
                    tracker.Answered_Time__c = System.now();
                    updatedTrackers.add(tracker);
                    al.Activity_Type__c = 'Reply Added';
                    //break;
                }
                else if(hasExternalUser || user.ContactId != null){
                //if (!isDuplicateTracker(feed)) {
                    if (user.ContactId != null) {
                        System.debug('after if');
                        accountOwner = user.AccountId != null && accountMap.containsKey(user.AccountId) 
                            ? accountMap.get(user.AccountId).OwnerId 
                            : UserInfo.getUserId();
                        relatedAccount = user.AccountId;
                    } else {
                        System.debug('after else '+feed);
                        if (opportunityMap.containsKey(feed.ParentId)) {
                            Opportunity relatedOpportunity = opportunityMap.get(feed.ParentId);
                            accountOwner = relatedOpportunity.Account.OwnerId;
                            relatedAccount = relatedOpportunity.AccountId;
                        }
                        else if (projectMap.containsKey(feed.ParentId)) {
                            Project__c relatedProject = projectMap.get(feed.ParentId);
                            accountOwner = relatedProject.Loan_Opportunity__r.Account.OwnerId;
                            relatedAccount = relatedProject.Loan_Opportunity__r.AccountId;
                        }
                        else if (disbursementRequestMap.containsKey(feed.ParentId)) {
                            Disbursement_Request__c relatedDisReq = disbursementRequestMap.get(feed.ParentId);
                            accountOwner = relatedDisReq.Project_lookup__r.Loan_Opportunity__r.Account.OwnerId;
                            relatedAccount = relatedDisReq.Project_lookup__r.Loan_Opportunity__r.AccountId;
                        }
                    }

                    if (feedCmmt != null && feedCmmt.length() > 255) {
                        feedCmmt = feedCmmt.substring(0, 255);
                    }

                    // Create the tracker record
                    Chatter_Post_Tracker__c tracker = new Chatter_Post_Tracker__c(
                        Post_Link__c = '/' + feed.Id,
                        Status__c = 'New',
                        Client__c = feed.CreatedById,
                        Assigned_To__c = accountOwner,
                        Related_Account__c = relatedAccount,
                        Comments__c = feedCmmt
                    );
                
                    // Set related object based on parent ID
                    if (relatedOpportunityIds.contains(feed.ParentId)) {
                        tracker.Related_Opportunity__c = feed.ParentId;
                    } else if (relatedProjectIds.contains(feed.ParentId)) {
                        tracker.Related_Project__c = feed.ParentId;
                    } else if (relatedDisbursementRequestIds.contains(feed.ParentId)) {
                        tracker.Related_Disbursement_Request__c = feed.ParentId;
                    }

                    postTrackers.add(tracker);
                    al.Activity_Type__c = 'Comment Added';
                //} 
                } 
                else{
                    al.Activity_Type__c = 'Reply Added';
                }
                
                if (relatedOpportunityIds.contains(feed.ParentId)) {
                
                    al.Item__c = opportunityMap.get(feed.ParentId).Name;
                    al.Account__c = opportunityMap.get(feed.ParentId).AccountId;
                } else if (relatedProjectIds.contains(feed.ParentId)) {
                    al.Item__c = projectMap.get(feed.ParentId).Name;
                
                    al.Account__c = projectMap.get(feed.ParentId).Loan_Opportunity__r.AccountId;
                } else if (relatedDisbursementRequestIds.contains(feed.ParentId)) {
                    al.Item__c = disbursementRequestMap.get(feed.ParentId).Name;
                
                    al.Account__c = disbursementRequestMap.get(feed.ParentId).Project_lookup__r.Loan_Opportunity__r.AccountId;
                }
                
                activities.add(al);
            }
        }
        
        if (!postTrackers.isEmpty()) {
            if (Schema.sObjectType.Chatter_Post_Tracker__c.isCreateable()) {
                insert postTrackers;
				System.debug('postTrackers '+postTrackers);
            } else {
                System.debug('User does not have permission to create Chatter_Post_Tracker__c records.');
            }
            System.debug('before createTrackerOwners');
            ChatterPostTrackerController.createTrackerOwners(postTrackers);
            System.debug('after createTrackerOwners');
            ChatterPostTrackerController.notifyAccountOwners(postTrackers);
            System.debug('after notifyAccountOwners');
        }
        
        if (!updatedTrackers.isEmpty()) {
            update updatedTrackers;
        }
        
        if(!activities.isEmpty()){
            //if (Schema.sObjectType.Activity_Logger__c.isCreateable()) {
                insert activities;
                hasInsertedActivities = false;
            /*} else {
                System.debug('User does not have permission to create Chatter_Post_Tracker__c records.');
            }*/
            //insert activities;
        }
    }
    
    public static Boolean isDuplicateTracker(FeedItem feed) {
        List<Chatter_Post_Tracker__c> existingTrackers = [
            SELECT Id FROM Chatter_Post_Tracker__c
            WHERE (Related_Opportunity__c = :feed.ParentId OR
                   Related_Project__c = :feed.ParentId OR
                   Related_Disbursement_Request__c = :feed.ParentId)
            //AND Status__c = 'New'
        ]; 
        
        if(existingTrackers.isEmpty()){
            return false;
        }
        else {
            return true;
        }
    }
    
    
    public static void preventUnauthorizedDelete(List<FeedItem> feedItems) {
        User currentUser = [SELECT Id, Profile.Name FROM User WHERE Id = :UserInfo.getUserId()];
        String currentProfileName = currentUser.Profile.Name;

        Set<String> communityProfileNames = new Set<String>{
            'Customer Community User',
            'Customer Community Login User'
        };

        for (FeedItem item : feedItems) {
            System.debug('Checking FeedItem: ' + item);

            Boolean isCommunityUser = communityProfileNames.contains(currentProfileName);
            Boolean isPostOwner = item.CreatedById == UserInfo.getUserId();

            if (isCommunityUser && !isPostOwner) {
                item.addError('Delete prevented: User does not have permission to delete this Internal post');
            }
        }
    }
    

	public static void processMentionsAndSendEmails(List<FeedItem> newFeedItems) {
        List<User> currentUserList = [SELECT Account.Name FROM User WHERE Id = :UserInfo.getUserId() AND ContactId = null LIMIT 1];
        if (currentUserList.isEmpty()) {
            System.debug('Current user is not an internal user. Exiting trigger.');
            return;
        }
        
        for (FeedItem post : newFeedItems) {

            List<String> allowedObjNameFromNotification = new List<String>{'Opportunity', 'Project__c', 'Disbursement_Request__c'};
            String parentObjName = String.valueOf(post.ParentId.getSObjectType());
            
            if(post.Visibility == 'AllUsers' && allowedObjNameFromNotification.contains(parentObjName)) {
                System.debug('Processing FeedItem: ' + post.Id + ', Body: ' + post.Body);

                Set<String> userIds = new Set<String>();
                if (!Test.isRunningTest()){
                    ConnectApi.FeedElement fi = ConnectApi.ChatterFeeds.getFeedElement('internal', post.Id);
                    List<ConnectApi.MessageSegment> messageSegments = fi.body.messageSegments;
                    for (ConnectApi.MessageSegment segment : messageSegments) {
                        if (segment instanceof ConnectApi.MentionSegment) {
                            ConnectApi.MentionSegment mention = (ConnectApi.MentionSegment) segment;
                            userIds.add(mention.record.id);
                        }
                    }
                }
                        
                String cleanedBody = removeTags(post.Body);
                
                List<User> taggedUsers = [SELECT Id, Email, Name FROM User WHERE Id IN :userIds AND ContactId != null];
                
                Organization orgInfo = [SELECT Id, IsSandbox FROM Organization LIMIT 1];
                String orgWideDisplayName;
                String replyToAddress;
                if (orgInfo.IsSandbox) {
                    orgWideDisplayName = System.Label.Form_Details;
                    replyToAddress = System.Label.MF_Chatter_Reply_Email_Service_Sandbox;
                } else {
                    orgWideDisplayName = System.Label.Form_Details_Prod;
                    replyToAddress = System.Label.MF_Chatter_Reply_Email_Service;
                }
    
                List<OrgWideEmailAddress> oweaList = [SELECT Id, Address, DisplayName FROM OrgWideEmailAddress WHERE Address = :orgWideDisplayName];
                
                String baseUrl = URL.getOrgDomainUrl().toExternalForm();
                String cmmtUrl = baseUrl.replace('salesforce','site'); 
    
                for (User taggedUser : taggedUsers) {
                    try {
                        String dynamicUrl = createDynamicUrl(cmmtUrl, post.Id, orgInfo.Id, taggedUser.Id);
                        System.debug('dynamicUrl: ' + dynamicUrl);
                        String emailBody = constructHtmlBody(post.Id, taggedUser.Name, cleanedBody, dynamicUrl,taggedUser.Id);
                        System.debug('emailBody: ' + emailBody);
    
                        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
                        mail.setToAddresses(new String[] { taggedUser.Email });
                        if (!oweaList.isEmpty()) {
                            mail.setOrgWideEmailAddressId(oweaList[0].Id);
                        }
                        mail.setSubject('You are mentioned in a Chatter post');
                        mail.setHtmlBody(emailBody);
                        mail.setCharset('UTF-8');
                        mail.setReplyTo(replyToAddress);
    
                        List<Messaging.SendEmailResult > sendEmailResults = Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
                        processSendEmailResults(sendEmailResults, taggedUser.Email);
    
                    } catch (Exception e) {
                        System.debug('Error occurred while processing user mention: ' + e.getMessage());
                        System.debug('Error occurred at line: ' + e.getStackTraceString());
                    }
                }
            }
        }
    }
    
    public static void processSendEmailResults(Messaging.SendEmailResult[] results, String recipientEmail) {
        for (Messaging.SendEmailResult result : results) {
            if (result.isSuccess()) {
                System.debug('Email sent successfully to: ' + recipientEmail);
            } else {
                for (Messaging.SendEmailError error : result.getErrors()) {
                    System.debug('Error sending email to ' + recipientEmail + ': ' + error.getMessage());
                }
            }
        }
    }
    
    public static List<String> extractUsersFromPostBody(String postBody) {
        List<String> users = new List<String>();
        Pattern p = Pattern.compile('@([a-zA-Z0-9._-]+)');
        Matcher m = p.matcher(postBody);
        while (m.find()) {
            users.add(m.group(1));
        }
        return users;
    }
    
    public static String removeTags(String input) {
        if (input == null) {
            return null;
        }
        return input.replaceAll('<[^>]*>', '');
    }
    
    public static String createDynamicUrl(String baseUrl, Id postId, Id orgId, Id userId) {
        System.debug('createDynamicUrl ');
        Boolean isSandbox = mf123Opp.isSandbox();
        System.debug('isSandbox '+isSandbox);
 		String addMFOrNot = isSandbox ? '/mf/s/' : '/s';

        String orgBaseUrlForEmail= '';
        if(!isSandbox) {
            orgBaseUrlForEmail = 'https://portal.mobilizationfunding.com/s/';
        } else {
            orgBaseUrlForEmail = baseUrl + addMFOrNot;
        }
        String dynamicUrl = orgBaseUrlForEmail + 'feed/' + postId +
        //String dynamicUrl = baseUrl + addMFOrNot + 'feed/' + postId +
        '?s1oid=' + orgId +
        '&OpenCommentForEdit=1' +
        '&s1nid=000000000000000' +
        '&emkind=chatterPostNotification' +
        '&s1uid=' + userId +
        '&emtm=' + DateTime.now().getTime() +
        '&fromEmail=1' +
        '&s1ext=0';
    
        System.debug('Generated Dynamic URL: ' + dynamicUrl);
        return dynamicUrl;
    }
    
    public static String constructHtmlBody(Id postId, String userName, String postBody, String dynamicUrl, String UserId) {
        EmailTemplate emailTemplate = [SELECT Id, Name, Body, Subject, HtmlValue FROM EmailTemplate WHERE Name = 'Community Chatter Mention Email Updated' LIMIT 1];
        
        String htmlBody = emailTemplate.HtmlValue;
        htmlBody = htmlBody.replace('{{{mentioned_user}}}', userName);
        htmlBody = htmlBody.replace('{{{mention_text}}}', postBody);
        htmlBody = htmlBody.replace('{{{comment_url}}}', dynamicUrl);
        htmlBody = htmlBody.replace('{{{UserId}}}', UserId);
        htmlBody = htmlBody.replace('{{{postId}}}', postId);
        htmlBody = htmlBody.replace('{{{current_year}}}', String.valueOf(System.today().Year()));
       
        return htmlBody;
    }
}