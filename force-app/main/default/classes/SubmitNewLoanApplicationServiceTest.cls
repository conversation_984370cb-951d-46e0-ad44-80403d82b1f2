@isTest
public class SubmitNewLoanApplicationServiceTest {
    
    @testSetup
    static void testSetup(){
        Account acc = new Account(name = 'kg parry');
        insert acc;
        Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Email = '<EMAIL>', AccountId = acc.Id);
        insert testContact;

        User testUser = new User(
            Username = '<EMAIL>',
            Email = '<EMAIL>',
            LastName = 'User',
            Alias = 'testuser',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Customer Community Login User' LIMIT 1].Id,
            LanguageLocaleKey = 'en_US',
            ContactId = testContact.Id
        );
        insert testUser;
       
        
       
    }
    
    @isTest
    static void testUpdateAccountAndCreateOpportunity_Success() {
       
        User testUser = [SELECT Id, name, ContactId, Profile.Name FROM User Where Email = '<EMAIL>' Limit 1];
        
		String jsonInput = '{  "BusinessInformation": { "Business_Name": "Example Business LLC", "Project_Name": "test proj"}, "AdditionalInformation": {"Are_there_any_new_UCC_Filings_against_the_company_or_any_of_its_majority_owners": "yes",  "Are_you_delinquent_or_in_default_of_any_debt_or_other_loans_including_Federal_or": "No", "Are_there_any_new_bankruptcy_fillings_by_you_or_your_majority_owners": "Yes", "Are_you_or_any_of_the_majority_owners_party_to_any_current_lawsuits_not_previous": "Yes"}, "VerifyAndSubmit": { "Date": "2024-09-11", "Email_for_Confirmation": "<EMAIL>", "Signature": "test sgn" }}';
        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/SubmitApplicationForm/';
        RestResponse res = new RestResponse();
        req.requestBody = Blob.valueOf(jsonInput);
        req.httpMethod = 'POST';
        RestContext.request = req;
        //RestContext.response = res;
        
        
        
        Test.startTest();
        
        PermissionSet mfPermissionSet = [SELECT Id FROM PermissionSet WHERE Name = 'Mobilization_Funding_Experience_Member' LIMIT 1];
        
        PermissionSetAssignment psa = new PermissionSetAssignment(
            AssigneeId = testUser.Id,
            PermissionSetId = mfPermissionSet.Id
        );
        insert psa;   
        
        System.runAs(testUser){
            SubmitNewLoanApplicationService.ResponseWrapper response = SubmitNewLoanApplicationService.processNewApplication();
        }
        Test.stopTest();
    
            // Verify the response
            /*System.assertEquals('success', response.status, 'The status should be success.');
            System.assertNotEquals(null, response.accountId, 'Account ID should not be null.');
            System.assertNotEquals(null, response.contact1Id, 'Contact1 ID should not be null.');
            System.assertNotEquals(null, response.opportunityId, 'Opportunity ID should not be null.');*/
        
    }

    
}