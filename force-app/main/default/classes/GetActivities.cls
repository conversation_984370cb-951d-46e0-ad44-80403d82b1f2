@RestResource(urlMapping='/GetActivities/*')
global without sharing class GetActivities {
    
    // Wrapper class to handle both success and error responses
    global class ResponseWrapper {
        public List<Activity_Logger__c> activities;
        public String error;
        
        public ResponseWrapper(List<Activity_Logger__c> activities, String error) {
            this.activities = activities;
            this.error = error;
        }
    }

    @HttpGet
    global static ResponseWrapper doGet() {
        
        RestRequest req = RestContext.request;
        Map<String,Object> restData = (Map<String,Object>)JSON.deserializeUntyped(JSON.serialize(RestContext.request.params));
        
        User currentUser = [SELECT Id, contactId, AccountId FROM User WHERE Id = :UserInfo.getUserId()];
        
        if(!restData.containsKey('relatedId') && currentUser.AccountId == null) {
            return new ResponseWrapper(null, 'Account must be associated with user');
        }         

        List<Activity_Logger__c> activities = new List<Activity_Logger__c>();
        
        if(restData.containsKey('relatedId')) {
            String relatedId = String.valueOf(restData.get('relatedId'));

            activities = [SELECT Id, Activity_Type__c, Item__c, Account__c, Name, Contact__c, Activity_Time__c, Related_Record__c, User__c, User__r.FirstName, User__r.LastName  
                          FROM Activity_Logger__c WHERE Related_Record__c =: relatedId];
                          
        } else {
            activities = [SELECT Id, Activity_Type__c, Item__c, Account__c, Name, Contact__c, Activity_Time__c, Related_Record__c, User__c, User__r.FirstName, User__r.LastName
                          FROM Activity_Logger__c WHERE Account__c =: currentUser.AccountId];
        }
        
        return new ResponseWrapper(activities, null);
    }
}