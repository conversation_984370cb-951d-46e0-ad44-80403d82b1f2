@SuppressWarnings('PMD')
public class PayApplicationTriggerHandler {
    public static void handleAfterInsert(List<Pay_Application__c> newPayApps) {
        Set<Id> projectIds = new Set<Id>();
        Map<Id, Pay_Application__c> payAppMap = new Map<Id, Pay_Application__c>();
        Map<Id, Date> payAppToFridayMap = new Map<Id, Date>();

        for (Pay_Application__c pa : newPayApps) {
            if (pa.Project__c != null) {
                projectIds.add(pa.Project__c);
                payAppMap.put(pa.Id, pa);
            }
        }

        Map<Id, Cashflow__c> projectToCashflowMap = new Map<Id, Cashflow__c>();
        List<Cashflow__c> cashflows = [
            SELECT Id, Project__c
            FROM Cashflow__c
            WHERE Is_Active__c = TRUE AND Project__c IN :projectIds
        ];

        Map<Id, List<Cashflow__c>> cfGroup = new Map<Id, List<Cashflow__c>>();
        for (Cashflow__c cf : cashflows) {
            if (!cfGroup.containsKey(cf.Project__c)) {
                cfGroup.put(cf.Project__c, new List<Cashflow__c>());
            }
            cfGroup.get(cf.Project__c).add(cf);
        }

        List<Cashflow_Weekly_Line_Pay_Application__c> junctions = new List<Cashflow_Weekly_Line_Pay_Application__c>();
        List<Cashflow_Line_Item__c> newLineItems = new List<Cashflow_Line_Item__c>();
        Map<Id, Date> payAppToLineItemDate = new Map<Id, Date>();

        for (Pay_Application__c pa : newPayApps) {
            List<Cashflow__c> activeCFs = cfGroup.get(pa.Project__c);

            if (activeCFs == null || activeCFs.isEmpty()) {
                System.debug('⚠ No active Cashflow for Project: ' + pa.Project__c);
                continue;
            }
            if (activeCFs.size() > 1) {
                System.debug('⚠ Multiple active Cashflows for Project: ' + pa.Project__c);
                continue;
            }

            Cashflow__c cashflow = activeCFs[0];

            if (pa.Planned_Loan_Payment_Date__c == null) {
                System.debug('⚠ Planned_Loan_Payment_Date__c is null for Pay Application: ' + pa.Id);
                continue;
            }

            // Calculate next Friday
            Date payDate = pa.Planned_Loan_Payment_Date__c;
            Integer dayOfWeek = Integer.valueOf(DateTime.newInstance(payDate, Time.newInstance(0, 0, 0, 0)).format('u'));
            Integer daysToAdd = 5 - dayOfWeek;
            if (daysToAdd < 0) {
                daysToAdd += 7;
            }
            Date nextFriday = payDate.addDays(daysToAdd);
            payAppToLineItemDate.put(pa.Id, nextFriday);

            // Get cashflow line item matching next Friday
            List<Cashflow_Line_Item__c> lineItems = [
                SELECT Id, Week_Start_Date__c
                FROM Cashflow_Line_Item__c
                WHERE Cashflow__c = :cashflow.Id AND Week_Start_Date__c = :nextFriday AND Type__c = 'Financing Source' AND
                    Financing_Source__c = 'Pay Application'
                LIMIT 1
            ];

            Cashflow_Line_Item__c lineItem;
            if (lineItems.isEmpty()) {
                System.debug('⚠ No Cashflow Line Item found, creating one for Cashflow: ' + cashflow.Id + ' and date: ' + nextFriday);
                lineItem = new Cashflow_Line_Item__c(
                    Cashflow__c = cashflow.Id,
                    Week_Start_Date__c = nextFriday,
                    Type__c = 'Financing Source',
                    Financing_Source__c = 'Pay Application'
                );
                newLineItems.add(lineItem);
                // Temporarily add null and update after insert
                junctions.add(new Cashflow_Weekly_Line_Pay_Application__c(
                    Pay_Application__c = pa.Id,
                    Cashflow_Line_Item__c = null
                ));
            } else {
                lineItem = lineItems[0];
                junctions.add(new Cashflow_Weekly_Line_Pay_Application__c(
                    Pay_Application__c = pa.Id,
                    Cashflow_Line_Item__c = lineItem.Id
                ));
            }
        }

        if (!newLineItems.isEmpty()) {
            try {
                insert newLineItems;
                // Map inserted line items by Week_Start_Date
                Map<Date, Id> weekDateToIdMap = new Map<Date, Id>();
                for (Cashflow_Line_Item__c cli : newLineItems) {
                    weekDateToIdMap.put(cli.Week_Start_Date__c, cli.Id);
                }
                // Update junctions with correct line item IDs
                for (Cashflow_Weekly_Line_Pay_Application__c j : junctions) {
                    if (j.Cashflow_Line_Item__c == null && payAppToLineItemDate.containsKey(j.Pay_Application__c)) {
                        Date keyDate = payAppToLineItemDate.get(j.Pay_Application__c);
                        if (weekDateToIdMap.containsKey(keyDate)) {
                            j.Cashflow_Line_Item__c = weekDateToIdMap.get(keyDate);
                        }
                    }
                }
            } catch (DmlException e) {
                System.debug('⚠ Error inserting new line items: ' + e.getMessage());
            }
        }

        if (!junctions.isEmpty()) {
            try {
                insert junctions;
            } catch (DmlException e) {
                System.debug('⚠ Error inserting junctions: ' + e.getMessage());
            }
        }
    }
}