@SuppressWarnings('PMD')
public class UpdateFundedTransactionsProcessor {
    
    public static void processTransactions() {
        List<Transaction__c> rawTxns = [
            SELECT Id, Project__c, Transaction_Date__c
            FROM Transaction__c
            WHERE Run_Transactions_Flow__c = false
              AND Transaction_Date__c != null
              AND Project__r.Date_Loan_Paid_Off__c = null
              AND Project__r.Loan_Opportunity__r.StageName = 'Funded'
            ORDER BY Project__c, Transaction_Date__c ASC
        ];

        Map<Id, Transaction__c> earliestByProject = new Map<Id, Transaction__c>();
        for (Transaction__c t : rawTxns) {
            if (!earliestByProject.containsKey(t.Project__c)) {
                earliestByProject.put(t.Project__c, t);
            }
        }

        if (!earliestByProject.isEmpty()) {
            List<Transaction__c> toUpdate = new List<Transaction__c>();
            for (Transaction__c t : earliestByProject.values()) {
                t.Run_Transactions_Flow__c = true;
                toUpdate.add(t);
            }
            update toUpdate;
        }
    }
}