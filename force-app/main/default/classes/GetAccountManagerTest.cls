@isTest
public class GetAccountManagerTest {

    @isTest
    static void testGetAccountDetailsAPI_ValidOpportunity() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        // Create test Opportunity linked to the Account
        User testUser = new User(
            FirstName = 'John',
            LastName = 'Doe',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'jdoe',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
            LanguageLocaleKey = 'en_US'
        );
        insert testUser;

        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity',
            StageName = 'Closed Won',
            CloseDate = Date.today().addDays(30),
            AccountId = testAccount.Id,
            Assigned_MF_Servicer__c = testUser.Id
        );
        insert testOpportunity;

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/GetAccountManager/' + testAccount.Id;
        req.httpMethod = 'GET';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        Test.startTest();
        GetAccountManager.getAccountDetailsAPI();
        Test.stopTest();
    }

    @isTest
    static void testGetAccountDetailsAPI_NoOpportunities() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        User testUser = new User(
            FirstName = 'Jane',
            LastName = 'Doe',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'jdoe',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
            LanguageLocaleKey = 'en_US'
        );
        insert testUser;

        // Update the Account with Assigned MF CRM
        testAccount.Assigned_MF_CRM__c = testUser.Id;
        update testAccount;

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/GetAccountManager/' + testAccount.Id;
        req.httpMethod = 'GET';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        Test.startTest();
        GetAccountManager.getAccountDetailsAPI();
        Test.stopTest();
    }

    @isTest
    static void testGetAccountDetailsAPI_InvalidAccountId() {
        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/GetAccountManager/';
        req.httpMethod = 'GET';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        Test.startTest();
        GetAccountManager.getAccountDetailsAPI();
        Test.stopTest();
    }

    @isTest
    static void testGetAccountDetailsAPI_NoUserFound() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/GetAccountManager/' + testAccount.Id;
        req.httpMethod = 'GET';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        Test.startTest();
        GetAccountManager.getAccountDetailsAPI();
        Test.stopTest();
    }
}