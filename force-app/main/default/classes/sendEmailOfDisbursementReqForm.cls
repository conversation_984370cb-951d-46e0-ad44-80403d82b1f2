@SuppressWarnings('PMD')
public without sharing class sendEmailOfDisbursementReqForm {
     @InvocableMethod(label='Send Email Of Disbursement Request Form')
    public static List<outputVariables> sendEmailOfDisbursementRequestForm(List<inputVariables> inputVariables) {

        List<outputVariables> outputVariablesList = new List<outputVariables>();

        for(inputVariables inputVariable : inputVariables) {
            // List<Disbursement_Request__c> disburseRequests = new List<Disbursement_Request__c>();
            List<Requested_Item__c> reqItems = new List<Requested_Item__c>();
            List<Note> notes = new List<Note>();
            
            Disbursement_Request__c acc = new Disbursement_Request__c();
            
            String disburseDetails = '';
            String verifyDetails = '';
            String reqItemDetails = '';
            String noteDetails = '';
            
            outputVariables outputVar = new outputVariables(); 
            
            try {
                if (inputVariable.disbursementReqId != null) {
                    acc = [SELECT Id, Account_Name__c,Zip_Code__c,
                    Street_Address__c,Status__c,State__c,Requester_Name__c,Requester_Email__c,Project__c,Project_lookup__c,
                    Project_lookup__r.Name,Project_lookup__r.Loan_Opportunity__r.Name,Project_Name__c,Phone__c,Payment_Method__c,
                    Payee_Name__c,Payee_Contact_Name__c,Payee_Contact_Email__c,Loan_Number__c,Issue_Check_To__c,
                    General_Contractor_Contract_Owner__c,Disbursement__c,Disbursement_Type__c,Comments__c,City__c,
                    Bank_Routing_Number__c,Bank_Name__c,Bank_Account_Number__c
                    FROM Disbursement_Request__c WHERE Id = :inputVariable.disbursementReqId LIMIT 1];

                    //for (Disbursement_Request__c acc : disburseRequests) {
                        // disburseDetails += '<h2>Disbursement Request</h2>';
                        // disburseDetails += '<p>Loan Number: ' + acc.Loan_Number__c + '</p>';
                        // disburseDetails += '<p>Expense Type: ' + acc.Disbursement_Type__c + '</p>';
                        // disburseDetails += '<p>Project Code: ' + acc.Project_Name__c + '</p>';
                        // disburseDetails += '<p>Payee Name: ' + acc.Payee_Name__c + '</p>';
                        // disburseDetails += '<p>Payee Contact Name: ' + acc.Payee_Contact_Name__c + '</p>';
                        // disburseDetails += '<p>Payee Contact Email: ' + acc.Payee_Contact_Email__c + '</p>';
                        // disburseDetails += '<p>Disbursement Number: ' + acc.Disbursement__c + '</p>';
                        // disburseDetails += '<p>Payment Method: ' + acc.Payment_Method__c + '</p>';
                        // disburseDetails += '<p>Payee Phone: ' + acc.Phone__c + '</p>';
                        // disburseDetails += '<p>Bank Name: ' + acc.Bank_Name__c + '</p>';
                        // disburseDetails += '<p>Account Name: ' + acc.Account_Name__c + '</p>';
                        // disburseDetails += '<p>Bank Routing Number: ' + acc.Bank_Routing_Number__c + '</p>';
                        // disburseDetails += '<p>Bank Account Number: ' + acc.Bank_Account_Number__c + '</p>';
                        // disburseDetails += '<p>Street: ' + acc.Street_Address__c + '</p>';
                        // disburseDetails += '<p>City: ' + acc.City__c + '</p>';
                        // disburseDetails += '<p>State/Province: ' + acc.State__c + '</p>';
                        // disburseDetails += '<p>Zip/Postal Code: ' + acc.Zip_Code__c + '</p>';
                        // disburseDetails += '<p>Mail Check To: ' + acc.Issue_Check_To__c + '</p>';

                        // disburseDetails += '<h2>Verify and Submit</h2>';
                        // disburseDetails += '<p>GC/Project Owner: ' + acc.General_Contractor_Contract_Owner__c + '</p>';
                        // disburseDetails += '<p>Requester Email: ' + acc.Requester_Name__c + '</p>';
                        // disburseDetails += '<p>Requester Email: ' + acc.Requester_Email__c + '</p>';
                        // disburseDetails += '<p>Additional Comments/Special Instructions: ' + acc.Comments__c + '</p>';


                        disburseDetails += '<h2>Disbursement Request</h2><br/>';
                        if (acc.Loan_Number__c != null) {
                            disburseDetails += '<p>Loan Number: ' + acc.Project_lookup__r.Loan_Opportunity__r.Name + '</p>';
                            System.debug('disburseDetails 61:'+disburseDetails );
                        } else {
                            disburseDetails += '<p>Loan Number: </p>';
                        }
                        if (acc.Disbursement_Type__c != null) {
                            disburseDetails += '<p>Expense Type: ' + acc.Disbursement_Type__c + '</p>';
                            System.debug('disburseDetails 67:'+disburseDetails );
                        } else {
                            disburseDetails += '<p>Expense Type: </p>';
                        }
                        if (acc.Project_lookup__c != null) {
                            disburseDetails += '<p>Project Code: ' + acc.Project_lookup__r.Name + '</p>';
                        } else {
                            disburseDetails += '<p>Project Code: </p>';
                        }
                        if (acc.Payee_Name__c != null) {
                            disburseDetails += '<p>Payee Name: ' + acc.Payee_Name__c + '</p>';
                        } else {
                            disburseDetails += '<p>Payee Name: </p>';
                        }
                        if (acc.Payee_Contact_Name__c != null) {
                            disburseDetails += '<p>Payee Contact Name: ' + acc.Payee_Contact_Name__c + '</p>';
                        } else {
                            disburseDetails += '<p>Payee Contact Name: </p>';
                        }
                        if (acc.Payee_Contact_Email__c != null) {
                            disburseDetails += '<p>Payee Contact Email: ' + acc.Payee_Contact_Email__c + '</p>';
                        } else {
                            disburseDetails += '<p>Payee Contact Email: </p>';
                        }
                        if (acc.Disbursement__c != null) {
                            disburseDetails += '<p>Disbursement Number: ' + acc.Disbursement__c + '</p>';
                        } else {
                            disburseDetails += '<p>Disbursement Number: </p>';
                        }
                        if (acc.Payment_Method__c != null) {
                            disburseDetails += '<p>Payment Method: ' + acc.Payment_Method__c + '</p>';
                        } else {
                            disburseDetails += '<p>Payment Method: </p>';
                        }
                        if (acc.Phone__c != null) {
                            disburseDetails += '<p>Payee Phone: ' + acc.Phone__c + '</p>';
                        } else {
                            disburseDetails += '<p>Payee Phone: </p>';
                        }
                        if (acc.Bank_Name__c != null) {
                            disburseDetails += '<p>Bank Name: ' + acc.Bank_Name__c + '</p>';
                        } 
                        // else {
                        //     disburseDetails += '<p>Bank Name: </p>';
                        // }
                        if (acc.Account_Name__c != null) {
                            disburseDetails += '<p>Account Name: ' + acc.Account_Name__c + '</p>';
                        } 
                        // else {
                        //     disburseDetails += '<p>Account Name: </p>';
                        // }
                        if (acc.Bank_Routing_Number__c != null) {
                            disburseDetails += '<p>Bank Routing Number: ' + acc.Bank_Routing_Number__c + '</p>';
                        } 
                        // else {
                        //     disburseDetails += '<p>Bank Routing Number: </p>';
                        // }
                        if (acc.Bank_Account_Number__c != null) {
                            disburseDetails += '<p>Bank Account Number: ' + acc.Bank_Account_Number__c + '</p>';
                        } 
                        // else {
                        //     disburseDetails += '<p>Bank Account Number: </p>';
                        // }
                        if (acc.Street_Address__c != null) {
                            disburseDetails += '<p>Street: ' + acc.Street_Address__c + '</p>';
                        } 
                        // else {
                        //     disburseDetails += '<p>Street: </p>';
                        // }
                        if (acc.City__c != null) {
                            disburseDetails += '<p>City: ' + acc.City__c + '</p>';
                        } 
                        // else {
                        //     disburseDetails += '<p>City: </p>';
                        // }
                        if (acc.State__c != null) {
                            disburseDetails += '<p>State/Province: ' + acc.State__c + '</p>';
                        } 
                        // else {
                        //     disburseDetails += '<p>State/Province: </p>';
                        // }
                        if (acc.Zip_Code__c != null) {
                            disburseDetails += '<p>Zip/Postal Code: ' + acc.Zip_Code__c + '</p>';
                        } 
                        // else {
                        //     disburseDetails += '<p>Zip/Postal Code: </p>';
                        // }
                        if (acc.Issue_Check_To__c != null) {
                            disburseDetails += '<p>Mail Check To: ' + acc.Issue_Check_To__c + '</p>';
                        } else {
                            disburseDetails += '<p>Mail Check To: </p>';
                        }

                        verifyDetails += '<br/><h2>Verify and Submit</h2><br/>';
                        if (acc.General_Contractor_Contract_Owner__c != null) {
                            verifyDetails += '<p>GC/Project Owner: ' + acc.General_Contractor_Contract_Owner__c + '</p>';
                        } else {
                            verifyDetails += '<p>GC/Project Owner: </p>';
                        }
                        if (acc.Project_Name__c != null) {
                            verifyDetails += '<p>Project Code: ' + acc.Project_Name__c + '</p>';
                        } else {
                            verifyDetails += '<p>Project Code: </p>';
                        }
                        if (acc.Requester_Name__c != null) {
                            verifyDetails += '<p>Requester Name: ' + acc.Requester_Name__c + '</p>';
                        } else {
                            verifyDetails += '<p>Requester Name: </p>';
                        }
                        if (acc.Requester_Email__c != null) {
                            verifyDetails += '<p>Requester Email: ' + acc.Requester_Email__c + '</p>';
                        } else {
                            verifyDetails += '<p>Requester Email: </p>';
                        }
                        if (acc.Comments__c != null) {
                            verifyDetails += '<p>Additional Comments/Special Instructions: ' + acc.Comments__c + '</p>';
                        } else {
                            verifyDetails += '<p>Additional Comments/Special Instructions: </p>';
                        }

                    //}
                }
                
                if (inputVariable.noteId != null ) {
                    notes = [SELECT Id, Title, ParentId, Body FROM Note WHERE Id = :inputVariable.noteId];

                    noteDetails += '<br/><h2>Upload Documents</h2><br/>';
                    for (Note con : notes) {
                        
                        // noteDetails += '<p>Item: ' + con.Body + '</p>';
                        // noteDetails += '<p>Title: ' + con.Title + '</p>';
                        if (con.Body != null) {
                            noteDetails += '<p>Item: ' + con.Body + '</p>';
                        } else {
                            noteDetails += '<p>Item: </p>';
                        }

                        // if (con.Title != null) {
                        //     noteDetails += '<p>Title: ' + con.Title + '</p>';
                        // } else {
                        //     noteDetails += '<p>Title: </p>';
                        // }

                    }
                }

                if (inputVariable.requestedItemIds != null ) {
                    reqItems = [SELECT Id, Name, Description_Work__c, Invoice_Date__c, 
                    Invoice_Amount__c, Invoice_Due_Date__c, Invoice__c, Disbursement_Request__c
                                      FROM Requested_Item__c WHERE Id = :inputVariable.requestedItemIds];

                    for (Requested_Item__c con : reqItems) {
                        reqItemDetails += '<br/><h2>Item Description</h2><br/>';
                        // reqItemDetails += '<p>Item: ' + con.Name + '</p>';
                        // reqItemDetails += '<p>Description/Work: ' + con.Description_Work__c + '</p>';
                        // reqItemDetails += '<p>Invoice Date: ' + con.Invoice_Date__c + '</p>';
                        // reqItemDetails += '<p>Invoice Amount: ' + con.Invoice_Amount__c + '</p>';
                        // reqItemDetails += '<p>Invoice #: ' + con.Invoice__c + '</p>';
                        // reqItemDetails += '<p>Invoice Due Date: ' + con.Invoice_Due_Date__c + '</p>';

                        if (con.Name != null) {
                            reqItemDetails += '<p>Item: ' + con.Name + '</p>';
                        } else {
                            reqItemDetails += '<p>Item: </p>';
                        }

                        if (con.Description_Work__c != null) {
                            reqItemDetails += '<p>Description/Work: ' + con.Description_Work__c + '</p>';
                        } else {
                            reqItemDetails += '<p>Description/Work: </p>';
                        }

                        if (con.Invoice_Date__c != null) {
                            reqItemDetails += '<p>Invoice Date: ' + con.Invoice_Date__c.format() + '</p>';
                        } else {
                            reqItemDetails += '<p>Invoice Date: </p>';
                        }

                        if (con.Invoice_Amount__c != null) {
                            reqItemDetails += '<p>Invoice Amount: $' + con.Invoice_Amount__c.format() + '</p>';
                        } else {
                            reqItemDetails += '<p>Invoice Amount: </p>';
                        }

                        if (con.Invoice__c != null) {
                            reqItemDetails += '<p>Invoice #: ' + con.Invoice__c + '</p>';
                        } else {
                            reqItemDetails += '<p>Invoice #: </p>';
                        }

                        if (con.Invoice_Due_Date__c != null) {
                            reqItemDetails += '<p>Invoice Due Date: ' + con.Invoice_Due_Date__c.format() + '</p>';
                        } else {
                            reqItemDetails += '<p>Invoice Due Date: </p>';
                        }

                    }
                }

                String emailBody = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title></title></head><body>';
                //Business Loan Application Form
                emailBody += '<h1>Disbursement Request Form</h1><br/>';
                emailBody += disburseDetails;
                emailBody += reqItemDetails;
                emailBody += noteDetails; 
                emailBody += verifyDetails;
                
                emailBody += '</body></html>';
                
                String loggerTransactionId;
                loggerTransactionId = Nebula.Logger.getTransactionId();

                Nebula.Logger.info('inputVariable ->' + inputVariable);
                Nebula.Logger.info('Starting BatchableLogger');
                Nebula.Logger.saveLog();

                System.enqueueJob(new PDFGeneratorQueueable(inputVariable.disbursementReqId, emailBody, inputVariable.contentDownloadUrl, 'Disbursement Form Details',loggerTransactionId));
                //createAndLinkPdfDocument(emailBody, inputVariable.disbursementReqId);

                Organization orgInfo = [SELECT Id, Name, OrganizationType, IsSandbox FROM Organization LIMIT 1];
                String orgWideDisplayName;
                String senderAddress;
                if (orgInfo.IsSandbox) {
                    orgWideDisplayName = System.Label.Form_Details;
                    senderAddress = System.Label.Sender_Address_Disbursement_Req_Form_Sandbox;
                } else {
                    orgWideDisplayName = System.Label.Form_Details_Prod;
                    senderAddress = System.Label.Sender_Address_Disbursement_Req_Form;
                }

                List<String> emailAddressList = senderAddress.split(',');

                for (Integer i = 0; i < emailAddressList.size(); i++) {
                    emailAddressList[i] = emailAddressList[i].trim();
                }

                List<OrgWideEmailAddress> owealist = [SELECT Id, Address, DisplayName FROM OrgWideEmailAddress WHERE Address =: orgWideDisplayName];

                List<Messaging.SingleEmailMessage> emailMessages = new List<Messaging.SingleEmailMessage>();
                Messaging.SingleEmailMessage formDetails = new Messaging.SingleEmailMessage();
                //formDetails.setToAddresses(new String[]{senderAddress});
                formDetails.setToAddresses(emailAddressList);
                
                if (!owealist.isEmpty()) formDetails.setOrgWideEmailAddressId(owealist[0].Id);
                formDetails.setSubject('Disbursement Request Form Details');

                System.debug('Content Download URL:172 ' + inputVariable.contentDownloadUrl);
                
                if (emailBody.contains('</body>')) {
                    emailBody = emailBody.replace('</body>', 
                                                    '<p>Signature Picture: <br/><img src="' + inputVariable.contentDownloadUrl + '" alt="Signature" width="250" height=auto;/></p></body></html>');
                } 

                EmailTemplate emailTemplate = [SELECT Id, Name, Body, Subject, HtmlValue FROM EmailTemplate WHERE Name = 'Form Submission Data Email' LIMIT 1];
                String emailBody2 = emailTemplate.Body;
                String htmlBody = emailTemplate.HtmlValue;
                List<String> bodies = new List<String>();
                String body = (htmlBody != null) ? htmlBody : emailBody2;
                body = body.replace('&lt;&lt;&lt;Form Data&gt;&gt;&gt;', emailBody );

                formDetails.setHtmlBody(body);
                emailMessages.add(formDetails);   

                if (!emailMessages.isEmpty() || Test.isRunningTest()) {
                    try {
                        if (!Test.isRunningTest()) {

                            Map<String, System.orgLimit> limitsMap = orgLimits.getMap();
                            System.orgLimit objSingleEMailLimit = limitsMap.get('SingleEmail');

                            if(objSingleEMailLimit.getValue() < objSingleEMailLimit.getLimit()) {
                        	    Messaging.sendEmail(emailMessages);
                            }
                        }
                    } catch (Exception e) {
                        System.debug('An exception occurred while sending the email: ' + e.getMessage() + ' --> ' + e.getStackTraceString());
                        throw new AuraHandledException(e.getMessage());
                    }
                }   

                outputVar.result = true;
            } catch (Exception e) {
                System.debug('An exception occurred: ' + e.getMessage() + ' --> ' + e.getStackTraceString());
                outputVar.result = false;
                throw new AuraHandledException(e.getMessage()+ ' ' + e.getStackTraceString());
            }

            outputVariablesList.add(outputVar);
        }
       
        return outputVariablesList;
    }

    /*public static void createAndLinkPdfDocument(String emailBody, Id linkedEntityId) {
        Blob pdfBlob = Blob.toPdf(emailBody);
        
        ContentVersion contentVersion = new ContentVersion(
            Title = 'Disbursement Form Details',
            PathOnClient = 'DisbursementFormDetails.pdf',
            VersionData = pdfBlob,
            IsMajorVersion = true,
            ContentLocation = 'S'
        );
        if (Test.isRunningTest()) { 
            try {
			Id netId = [SELECT Id FROM Network LIMIT 1].Id;
                contentVersion.NetworkId = netId;
            } catch (Exception e) {
                System.debug('Not in a network context: ' + e.getMessage());
            }
        }
        insert contentVersion;

        contentVersion = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id];
        
        ContentDocumentLink contentDocLink = new ContentDocumentLink(
            ContentDocumentId = contentVersion.ContentDocumentId,
            LinkedEntityId = linkedEntityId, 
            Visibility = 'AllUsers'
        );
        insert contentDocLink;
    }*/

    public class inputVariables {
        @InvocableVariable 
        public Id disbursementReqId;
        @InvocableVariable 
        public List<Requested_Item__c> requestedItemIds;
        @InvocableVariable 
        public List<Note> noteId;
        @InvocableVariable(label='Content Download URL')
        public String contentDownloadUrl;
    }

    public class outputVariables {
        @InvocableVariable 
        public Boolean result;
    }
}