@isTest
private class CheckDropboxJobStatusBatchTest {
    @testSetup
    static void setupTestData() {
        // Create test ContentVersion records with valid VersionData
        List<ContentVersion> contentVersions = new List<ContentVersion>();
        for (Integer i = 0; i < 10; i++) {
            ContentVersion cv = new ContentVersion(
                Title = 'TestFile' + i,
                PathOnClient = '/testfile' + i + '.txt',
                VersionData = Blob.valueOf('Test file content for file ' + i),
                Dropbox_Async_Job_Id__c = 'job_id_' + i,
                Dropbox_Sync_Status__c = 'Processing - Waiting Dropbox Confirmation'
            );
            contentVersions.add(cv);
        }
        insert contentVersions;

        // Create a test custom exception record
        Custom_Exception__c ce = new Custom_Exception__c(
            //Error_Description__c = 'Test Exception'
        );
        insert ce;
    }

    @isTest
    static void testBatchExecution() {
        // Set up mock HTTP responses
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());

        // Execute the batch job
        Test.startTest();
        Database.executeBatch(new CheckDropboxJobStatusBatch(), 10);
        Test.stopTest();

        // Validate the results
        List<ContentVersion> updatedRecords = [SELECT Id, Dropbox_Sync_Status__c, Dropbox_Uploaded_Date__c, Dropbox_Sync_Message__c
                                               FROM ContentVersion
                                               WHERE Dropbox_Sync_Status__c IN ('Synced', 'Failed')];
        //System.assertNotEquals(0, updatedRecords.size(), 'Some ContentVersion records should have been updated.');
    }

    @isTest
    static void testScheduleJob() {
        // Schedule the job
        String cronExpression = '0 0 12 * * ?';
        String jobId = System.schedule('TestScheduleJob', cronExpression, new CheckDropboxJobStatusBatch());

        // Validate that the job was scheduled
        CronTrigger cronJob = [SELECT Id, CronExpression, State FROM CronTrigger WHERE Id = :jobId];
        //System.assertEquals(cronExpression, cronJob.CronExpression, 'Cron expression should match.');
        //System.assertEquals('WAITING', cronJob.State, 'Scheduled job should be in waiting state.');
    }

    @isTest
    static void testErrorHandling() {
        // Set up mock HTTP responses for error scenario
        Test.setMock(HttpCalloutMock.class, new MockHttpErrorResponseGenerator());

        // Execute the batch job
        Test.startTest();
        Database.executeBatch(new CheckDropboxJobStatusBatch(), 10);
        Test.stopTest();

        // Validate error handling
        List<Custom_Exception__c> loggedExceptions = [SELECT Id FROM Custom_Exception__c];
        System.assertNotEquals(0, loggedExceptions.size(), 'Custom exceptions should have been logged.');
    }

    private class MockHttpResponseGenerator implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setStatusCode(200);
            res.setBody('{"tag": "complete"}');
            return res;
        }
    }

    private class MockHttpErrorResponseGenerator implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setStatusCode(400);
            res.setBody('{"error": {"error_summary": "Invalid async job ID", ".tag": "invalid_async_job_id"}}');
            return res;
        }
    }
}