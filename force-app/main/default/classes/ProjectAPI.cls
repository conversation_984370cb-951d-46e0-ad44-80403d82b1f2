@RestResource(urlMapping='/project/*')
global without sharing class ProjectAPI {

    @HttpGet
    global static void getProjects() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        String opportunityId = req.requestURI.substring(req.requestURI.lastIndexOf('/') + 1);

        ProjectResponseWrapper responseWrapper = new ProjectResponseWrapper();

        try {
            List<Project__c> projects;

            if (!String.isBlank(opportunityId) && opportunityId.startsWith('006')) {
                projects = [
                    SELECT Id, Name, Date_to_Funded__c, Date_Funded__c, MF_Loan_Amount__c, Project_Number__c, Assigned_MF_Servicer__c, Assigned_MF_Servicer__r.Name, Loan_Status__c, Loan_Maturity_Date__c, Account_Name__c, Account_Name__r.Name, Current_Pay_Off_Balance__c, Money_Available_to_Draw_on_the_Loan__c, Enter_name_of_GC__c,
                    (
                        SELECT id, name, Amount_Approved__c, Amount_Approved1__c, Disbursement_Type__c, LastModifiedDate,
                        (
                            SELECT Id, Name, Disbursement_Request__c, Description_Work__c, 
                                Invoice_Amount__c, Invoice_Date__c, Invoice_Due_Date__c, Invoice__c,
                                CreatedDate, CreatedById, LastActivityDate
                            FROM Requested_Items__r
                        )
                        FROM Disbursement_Requests1_del__r
                    )
                    FROM Project__c 
                    WHERE Loan_Opportunity__c = :opportunityId
                ];
                
                responseWrapper.totalSize = projects.size();
                responseWrapper.done = true;
                responseWrapper.records = projects;
    
                res.responseBody = Blob.valueOf(JSON.serialize(responseWrapper));
            } else {
                projects = [
                    SELECT Id, Name, MF_Loan_Amount__c, Assigned_MF_Servicer__c, Assigned_MF_Servicer__r.Name, Loan_Status__c, Loan_Maturity_Date__c, Account_Name__c, Account_Name__r.Name, Date_to_Funded__c, Date_Funded__c, Project_Number__c, Enter_name_of_GC__c,
                    (
                        SELECT id, name, Amount_Approved__c, Amount_Approved1__c, Disbursement_Type__c, LastModifiedDate,
                        (
                            SELECT Id, Name, Disbursement_Request__c, Description_Work__c, 
                                Invoice_Amount__c, Invoice_Date__c, Invoice_Due_Date__c, Invoice__c,
                                CreatedDate, CreatedById, LastActivityDate
                            FROM Requested_Items__r
                        )
                        FROM Disbursement_Requests1_del__r
                    ) FROM Project__c Where Id = :opportunityId
                ];
                 
                res.responseBody = Blob.valueOf(JSON.serialize(projects));
            }

            
        } catch (Exception ex) {
            res.statusCode = 500;
            res.responseBody = Blob.valueOf('Error retrieving projects: ' + ex.getMessage());
        }
    }

    public class ProjectResponseWrapper {
        public Integer totalSize;
        public Boolean done;
        public List<Project__c> records;
        public List<Disbursement_Request__c> disbrecords;
        public List<Requested_Item__c> itemrecords;

        public ProjectResponseWrapper() {
            this.records = new List<Project__c>();
            this.disbrecords = new List<Disbursement_Request__c>();
            this.itemrecords = new List<Requested_Item__c>();
        }
    }
}