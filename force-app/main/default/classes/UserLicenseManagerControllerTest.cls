@isTest
public class UserLicenseManagerControllerTest {  
    @testSetup
    static void setupTestData() {
        Account acc1 = new Account(Name = 'Account 1');
        insert acc1;
        
        Contact con = new Contact(AccountId = acc1.Id, Phone = '34356', lastName = 'con34');
        insert con;

        Account acc2 = new Account(Name = 'Account 2');
        insert acc2;

        Profile communityProfile = [SELECT Id FROM Profile WHERE Name LIKE '%Community%' LIMIT 1];
        
        User user1 = new User(
            FirstName = 'Test',
            LastName = 'User1',
            Email = '<EMAIL>',
            Username = '<EMAIL>' + System.currentTimeMillis(),
            Alias = 'tuser1',
            CommunityNickname = 'TestUser1',
            ProfileId = communityProfile.Id,
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            IsActive = true,
            ContactId = con.Id
        );
        insert user1;

        Opportunity opp1 = new Opportunity(
            Name = 'Opportunity 1',
            StageName = 'Closed Won',
            CloseDate = System.today(),
            AccountId = acc1.Id,
            Status_Update_for_Client__c = 'Approved'
        );
        insert opp1;

        User_Profile_Change_Log__c log1 = new User_Profile_Change_Log__c(
            UserId__c = user1.Id,
            OldProfileName__c = 'Old Profile',
            NewProfileName__c = 'New Profile',
            ChangeDate__c = System.today(),
            isActive__c = true
        );
        insert log1;
    }

    @isTest
    static void testGetUserLicenses() {
        Test.startTest();
        List<UserLicenseManagerController.UserLicenseWrapper> userLicenses = UserLicenseManagerController.getUserLicenses();
        Test.stopTest();
    }

    @isTest
    static void testGetUserProfileChangeLogs() {
        Test.startTest();
        List<UserLicenseManagerController.UserProfileChangeLogWrapper> changeLogs = UserLicenseManagerController.getUserProfileChangeLogs();
        Test.stopTest();
    }
}