@IsTest
private class FileAPITest {

    @IsTest
    static void testGetFilesWithValidRecordId() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        ContentVersion contentVersion = new ContentVersion(
            Title = 'Test File',
            PathOnClient = 'TestFile.pdf',
            VersionData = Blob.valueOf('Test content'),
            IsMajorVersion = true
        );
        insert contentVersion;

        Id contentDocumentId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id LIMIT 1].ContentDocumentId;

        ContentDocumentLink contentDocumentLink = new ContentDocumentLink(
            LinkedEntityId = testAccount.Id,
            ContentDocumentId = contentDocumentId,
            ShareType = 'I'
        );
        insert contentDocumentLink;

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/files/' + testAccount.Id;
        req.httpMethod = 'GET';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        Test.startTest();
        FileAPI.getFiles();
        Test.stopTest();
    }

    @IsTest
    static void testGetFilesWithoutRecordId() {
        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/files/';
        req.httpMethod = 'GET';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        Test.startTest();
        FileAPI.getFiles();
        Test.stopTest();
    }

}