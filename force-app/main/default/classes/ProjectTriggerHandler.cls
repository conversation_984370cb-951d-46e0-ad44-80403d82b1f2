public class ProjectTriggerHandler {

    public static void createActivity(List<Project__c> prjs){

        List<Project__c> projects = new List<Project__c>();

        projects = [SELECT Id,name,Loan_Opportunity__r.AccountId FROM Project__c WHERE Id IN: prjs];

        User usr = [SELECT Id,contactId,AccountId FROM User WHERE Id =: userinfo.getUserId()];        

        List<Activity_Logger__c> activities = new List<Activity_Logger__c>();

        for(Project__c project : projects){

            activities.add(new Activity_Logger__c(Item__c=project.name,Related_Record__c = project.Id,Account__c = project.Loan_Opportunity__r.AccountId,Activity_Time__c = system.now(),Activity_Type__c = 'Project Submitted',Contact__c=usr.contactId,User__c=usr.Id));

        }

        if(!activities.isEmpty()){
            insert activities;
        }       

    }

     public static void updateAccountName(List<Project__c> projects, Map<Id, Project__c> oldProjectsMap) {
        Set<Id> opportunityIds = new Set<Id>();

        for (Project__c project : projects) {           
            //Checking if insert or update
            Project__c oldProdject = oldProjectsMap != null ? oldProjectsMap.get(project.Id) : null;
            if(
                (
                    oldProdject != null && project.Loan_Opportunity__c != oldProdject.Loan_Opportunity__c
                )    
                ||
                (
                    oldProdject == null && project.Loan_Opportunity__c != null)
                ) {
                opportunityIds.add(project.Loan_Opportunity__c);
            }
        }

        if(!opportunityIds.isEmpty()) {
            Map<Id, Opportunity> oppMap = new Map<Id, Opportunity>(
                [SELECT Id, AccountId FROM Opportunity WHERE Id IN :opportunityIds]
            );

            for (Project__c project : projects) {
                if (project.Loan_Opportunity__c != null) {
                    Opportunity relatedOpp = oppMap.get(project.Loan_Opportunity__c);

                    if (relatedOpp != null && relatedOpp.AccountId != null) {
                        project.Account_Name__c = relatedOpp.AccountId;
                        System.debug('Updated Account_Name__c with Account Id: ' + relatedOpp.AccountId);
                    }
                }
            }
        }
    }
}