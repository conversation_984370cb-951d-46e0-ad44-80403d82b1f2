@RestResource(urlMapping='/feedItem/*')
global without sharing class FeedItemRestApi {

    //Create feedItem record
    @HttpPost
    global static String createFeedItem(String parentId, String body, String title, String type) {
        FeedItem feedItem = new FeedItem();
        feedItem.ParentId = parentId;  
        feedItem.Body = body;         
        feedItem.Title = title;   
        feedItem.Type = type;
        feedItem.Visibility = 'AllUsers';

        Nebula.Logger.info('parentId -> ' + parentId).addTag('Feed Item API');
        Nebula.Logger.info('body -> ' + body).addTag('Feed Item API');
        Nebula.Logger.info('title -> ' + title).addTag('Feed Item API');
        Nebula.Logger.info('type -> ' + type).addTag('Feed Item API');
        
        try {
            insert feedItem;
            return 'FeedItem created successfully with Id: ' + feedItem.Id;
        } catch (Exception ex) {
            Nebula.Logger.error('Error ' + ex.getMessage() + '--' + ex.getStackTraceString()).addTag('Feed Item API');
            Nebula.Logger.saveLog();
            return 'Error: ' + ex.getMessage();
        }
    }
    
    

    // GET method to retrieve a FeedItem by Id and its associated comments
    @HttpGet
    global static List<FeedItem> getFeedItemAndComments() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;

        // Get the FeedItem Id from the URL
        String parentItemId = req.requestURI.substring(req.requestURI.lastIndexOf('/') + 1);

        if (parentItemId == null || parentItemId == '') {
            res.statusCode = 400;
            res.responseBody = Blob.valueOf('Missing FeedItemId');
            return null;
        }

        try {
            return [SELECT Id, ParentId, Body, Title, CreatedById, CreatedDate, Type,
                                        (SELECT Id, FeedItemId, CommentBody, CreatedById, CreatedDate FROM FeedComments)
                                     FROM FeedItem 
                                     WHERE ParentId = :parentItemId AND Visibility = 'AllUsers'];

        } catch (Exception ex) {
            res.statusCode = 500;
            res.responseBody = Blob.valueOf('Error retrieving FeedItem and comments: ' + ex.getMessage());
            return null;
        }
    }
}