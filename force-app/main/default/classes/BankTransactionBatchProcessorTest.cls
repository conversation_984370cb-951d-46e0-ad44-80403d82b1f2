@isTest
private class BankTransactionBatchProcessorTest {
    
    /** 
     * Sets up test data: a bank account and two transactions.
     */
    @testSetup
    static void setupTestData() {
        // Create a test Bank Account record
        Bank_Account__c acct = new Bank_Account__c(
            Name = 'Test Bank Account',
            Holder_Name__c = '<PERSON>',
            Current_Balance__c = 1000.00
        );
        insert acct;
        
        // Create two Bank Transaction records with null Category__c and MF_Category__c
        List<Bank_Transaction__c> transactions = new List<Bank_Transaction__c>{
            new Bank_Transaction__c(
                Transaction_Id__c = 'txn1',
                Bank_Account__c = acct.Id,
                Description__c = 'Transaction 1',
                Debit__c = 100.00
            ),
            new Bank_Transaction__c(
                Transaction_Id__c = 'txn2',
                Bank_Account__c = acct.Id,
                Description__c = 'Transaction 2',
                Credit__c = 50.00
            )
        };
        insert transactions;
    }
    
    /** 
     * Tests the processor with 'All' filter, ensuring all transactions are processed.
     */
    @isTest
    static void testBatchExecution_AllFilter() {
        // Set the mock callout to simulate OpenAI categorization
        Bank_Transaction__c txn2 = [SELECT Id, Description__c, Debit__c, Credit__c 
                                  FROM Bank_Transaction__c 
                                  LIMIT 1];
        
        Test.setMock(HttpCalloutMock.class, new MockOpenAICalloutSuccess(txn2.Id));

        
        // Retrieve the test Bank Account
        Bank_Account__c acct = [SELECT Id FROM Bank_Account__c LIMIT 1];
        
        // Instantiate the processor with 'All' filter
        BankTransactionBatchProcessor processor = new BankTransactionBatchProcessor(acct.Id, 'All', UserInfo.getUserId());
        
        Test.startTest();
        processor.execute(null); // Execute as a schedulable job
        Test.stopTest();
        
      
    }
    
    /** 
     * Tests the processor with 'NoFlinksCategory' filter, processing transactions with null Category__c.
     */
    @isTest
    static void testBatchExecution_NoFlinksCategory() {
        // Set the mock callout
        Bank_Transaction__c txn2 = [SELECT Id, Description__c, Debit__c, Credit__c 
                                   FROM Bank_Transaction__c 
                                   LIMIT 1];
        
        Test.setMock(HttpCalloutMock.class, new MockOpenAICalloutSuccess(txn2.Id));
        
        // Retrieve the test Bank Account
        Bank_Account__c acct = [SELECT Id FROM Bank_Account__c LIMIT 1];
        
        // Instantiate the processor with 'NoFlinksCategory' filter
        BankTransactionBatchProcessor processor = new BankTransactionBatchProcessor(acct.Id, 'NoFlinksCategory', UserInfo.getUserId());
        
        Test.startTest();
        processor.execute(null);
        Test.stopTest();
        
    }
    
    /** 
     * Tests the processor with 'NoMFCategory' filter, processing only transactions with null MF_Category__c.
     */
    @isTest
    static void testBatchExecution_NoMFCategory() {
        // Set the mock callout
        Bank_Transaction__c txn2 = [SELECT Id, Description__c, Debit__c, Credit__c 
                                   FROM Bank_Transaction__c 
                                   LIMIT 1];
        
        Test.setMock(HttpCalloutMock.class, new MockOpenAICalloutSuccess(txn2.Id));
        
        // Retrieve the test Bank Account
        Bank_Account__c acct = [SELECT Id FROM Bank_Account__c LIMIT 1];
        
        // Update one transaction to have a non-null MF_Category__c
        List<Bank_Transaction__c> txnsToUpdate = [SELECT Id, MF_Category__c FROM Bank_Transaction__c WHERE Bank_Account__c = :acct.Id LIMIT 2];
        if (txnsToUpdate.size() == 2) {
            txnsToUpdate[0].MF_Category__c = 'Manual';
            update txnsToUpdate[0];
        }
        
        // Instantiate the processor with 'NoMFCategory' filter
        BankTransactionBatchProcessor processor = new BankTransactionBatchProcessor(acct.Id, 'NoMFCategory', UserInfo.getUserId());
        
        Test.startTest();
        processor.execute(null);
        Test.stopTest();
        
        
    }
    
    /** 
     * Tests the processor with an empty account, ensuring no processing occurs.
     */
    @isTest
    static void testBatchExecution_NoRecords() {
        // Create a new bank account with no transactions
        Bank_Account__c newAcct = new Bank_Account__c(
            Name = 'Empty Account',
            Holder_Name__c = 'No Transactions',
            Current_Balance__c = 500.00
        );
        insert newAcct;
        
        // Instantiate the processor with 'Uncategorized' filter
        BankTransactionBatchProcessor processor = new BankTransactionBatchProcessor(newAcct.Id, 'Uncategorized', UserInfo.getUserId());
        
        Test.startTest();
        processor.execute(null);
        Test.stopTest();
        
        // Verify no transactions exist
        List<Bank_Transaction__c> txns = [SELECT Id FROM Bank_Transaction__c WHERE Bank_Account__c = :newAcct.Id];
        System.assertEquals(0, txns.size(), 'There should be no transactions for this bank account.');
        
        // Verify no email was sent
        System.assertEquals(0, Limits.getEmailInvocations(), 'No email should be sent when no transactions are processed.');
    }
    
    /** 
     * Mock class to simulate a successful OpenAI callout, returning categories for up to two transactions.
     */
       private class MockOpenAICalloutSuccess implements HttpCalloutMock {
        private String txnId;

        public MockOpenAICalloutSuccess(String txnId) {
            this.txnId = txnId;
        }

        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse response = new HttpResponse();
            response.setStatusCode(200);

            String contentString = '{"classified_transactions": [{"ai_category": "Overhead", "transaction_type": "debit", "salesforce_id": "' + txnId + '", "confidence_score": 9, "ai_reasoning": "Matched vendor UPWORK for Overhead (business services)."}]}';

            Map<String, Object> message = new Map<String, Object>{
                'content' => contentString,
                'role' => 'assistant'
            };
            Map<String, Object> choice = new Map<String, Object>{
                'message' => message,
                'finish_reason' => 'stop',
                'index' => 0
            };
            Map<String, Object> responseMap = new Map<String, Object>{
                'choices' => new List<Object>{ choice },
                'created' => 1743692516,
                'id' => 'chatcmpl-BIGMm1gNyIZauFqItr78624mooPbd',
                'model' => 'gpt-4o-2024-11-20',
                'object' => 'chat.completion'
            };
            String responseJson = JSON.serialize(responseMap);
            response.setBody(responseJson);
            return response;
        }
    }
    

}