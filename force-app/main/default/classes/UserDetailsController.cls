@SuppressWarnings('PMD')
public without sharing class UserDetailsController {
    @AuraEnabled(cacheable=true)
    public static UserDetails getUserDetails() {
        Id userId = UserInfo.getUserId();
        User u = [SELECT Id, Email, Phone,Contact.Account.Name, Contact.Name, Contact.Phone, Contact.Email 
                  FROM User 
                  WHERE Id = :userId LIMIT 1];
        
        UserDetails userDetails = new UserDetails();
        userDetails.accountName = u.Contact.Account.Name;
        userDetails.contactName = u.Contact.Name;
        userDetails.contactPhone = u.Contact.Phone;
        userDetails.contactEmail = u.Contact.Email;
        userDetails.userEmail = u.Email;
        userDetails.userPhone = u.Phone;
        
        return userDetails;
    }

    @AuraEnabled(cacheable=true)
public static User getAccountId() {
    return [
        SELECT Name, AccountId, Account.Name 
        FROM User 
        WHERE Id = :UserInfo.getUserId()
    ];
}


    @AuraEnabled
    public static Boolean isSandbox() {
        Organization orgInfo = [SELECT Id, Name, OrganizationType, IsSandbox FROM Organization LIMIT 1];
        if (orgInfo.IsSandbox) {
           return true;
        }
        return false;
    }

    public class UserDetails {
        @AuraEnabled
        public String accountName;
        @AuraEnabled
        public String contactName;
        @AuraEnabled
        public String contactPhone;
        @AuraEnabled
        public String contactEmail;
        @AuraEnabled
        public String userEmail;
        @AuraEnabled
        public String userPhone;
    }
}