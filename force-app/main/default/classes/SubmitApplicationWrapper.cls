public class SubmitApplicationWrapper {
    
    public BusinessInformation BusinessInformation;
    public AdditionalInformation AdditionalInformation;
    public OwnerInformation OwnerInformation;
    public OwnerInformation2 OwnerInformation2;
    public DebtSchedule DebtSchedule;
    public UploadingYourDocuments UploadingYourDocuments;
    public VerifyAndSubmit VerifyAndSubmit;

    public class BusinessInformation {
        public String Business_Name;
        public BusinessAddress BusinessAddress;
        public String Website;
        public String Year_Business_Was_Founded;
        public String Type_of_Work;
        public String businessPhone;
        public String Number_of_Employees;
        public Decimal Federal_Tax_ID_Number;
        public Decimal Loan_Amount_Requested;
        public String purchaseOrder;
        public String Number_of_Owners_Above_10;
    }

    public class BusinessAddress {
        public String STREET_ADDRESS;
        public String CITY;
        public String STATE;
        public String ZIP_CODE;
    }

    public class AdditionalInformation {
        public String Are_there_any_UCC_Filings_against_the_company_or_any_of_its_majority_owners;
        public String Are_you_delinquent_or_in_default_of_any_debt_or_other_loans_including_Federal_or;
        public String Have_you_or_any_of_the_majority_owners_ever_filed_for_bankruptcy;
        public String Are_you_or_any_of_the_majority_owners_party_to_any_current_lawsuits;
    }

    public class OwnerInformation {
        public Name Name;
        //public String homeAddress;
        public HomeAddress HomeAddress;
        public String cellPhone;
        public String Email;
        public String Social_Security_Number;
        public String Married;
        public String Date_Of_Birth;
        public String Title;
        public String Percent_Ownership;
        public String Do_you_have_a_life_insurance_policy;
        public String If_yes_what_is_the_policy_limit;
    }
    
    public class HomeAddress {
        public String street;
        public String city;
        public String state;
        public String postalCode;
        public String country;
    }

    public class Name {
        public String First;
        public String Last;
    }

    public class OwnerInformation2 {
        public Name2 Name;
        //public String homeAddress2;
        public HomeAddress2 HomeAddress2;
        public String cellPhone2;
        public String Email2;
        public String Social_Security_Number2;
        public String Married2;
        public String Date_Of_Birth2;
        public String Title2;
        public String Percent_Ownership2;
        public String Do_you_have_a_life_insurance_policy2;
        public String If_yes_what_is_the_policy_limit2;
    }
    
    public class HomeAddress2 {
        public String street2;
        public String city2;
        public String state2;
        public String postalCode2;
        public String country2;
    }

    public class Name2 {
        public String First2;
        public String Last2;
    }

    public class DebtSchedule {
        public String OverheadAndDebtSchedule;
    }

    public class UploadingYourDocuments {
        public String uploadDocuments;
    }

    public class VerifyAndSubmit {
        public Date Date1;
        public String Email_for_Confirmation;
        public String Signature;
    }
}