/**
 * @description This class is responsible for determining the UI component configurations
 * for the Cashflow Tracker based on the running user's permissions. It helps in conditionally
 * rendering and enabling/disabling sections of the UI.
 * <AUTHOR>
 * @date 12 May 2025
*/
@SuppressWarnings('PMD')
public with sharing class CashflowUIService {

    // --- VIEW PERMISSIONS ---
    private static final String VIEW_COSTS_PERM = 'View_Project_Costs_Section';
    private static final String VIEW_SOURCES_USES_PERM = 'View_Sources_Uses_Section';
    private static final String VIEW_BALANCES_PERM = 'View_Balances_Section';
    private static final String VIEW_PNL_PERM = 'View_PnL_Section';
    // ETC

    // --- EDIT PERMISSIONS ---
    private static final String EDIT_COSTS_PERM = 'Edit_Project_Costs_Section';
    private static final String EDIT_SOURCES_USES_PERM = 'Edit_Sources_Uses_Section';
    // ETC

    /**
     * @description Gets the UI configuration based on the running user's permissions.
     * @return Map<String, ComponentConfig> Map where key is section name, value is config object.
	*/
    @AuraEnabled(cacheable=true)
    public static Map<String, ComponentConfig> getUIComponentConfiguration() {
        Map<String, ComponentConfig> componentConfigs = new Map<String, ComponentConfig>();

        // Check permissions for each section
        componentConfigs.put('projectCosts', new ComponentConfig(
            FeatureManagement.checkPermission(VIEW_COSTS_PERM),
            FeatureManagement.checkPermission(EDIT_COSTS_PERM)
        ));

        componentConfigs.put('sourcesUses', new ComponentConfig(
            FeatureManagement.checkPermission(VIEW_SOURCES_USES_PERM),
            FeatureManagement.checkPermission(EDIT_SOURCES_USES_PERM)
        ));

        componentConfigs.put('balances', new ComponentConfig(
            FeatureManagement.checkPermission(VIEW_BALANCES_PERM),
            false
        ));

         componentConfigs.put('pnl', new ComponentConfig(
            FeatureManagement.checkPermission(VIEW_PNL_PERM),
            false
        ));
		
        DebugLogUtil.info('getUIComponentConfiguration Result: ' + JSON.serializePretty(componentConfigs));

        return componentConfigs;
    }

    /**
     * @description Wrapper class to hold visibility and editability for a UI component/section.
     */
    public class ComponentConfig {
        @AuraEnabled public Boolean isVisible { get; private set; }
        @AuraEnabled public Boolean canEdit { get; private set; }

        public ComponentConfig(Boolean isVisible, Boolean canEdit) {
            // If not visible, cannot be editable
            this.isVisible = isVisible;
            this.canEdit = isVisible && canEdit;
        }
    }
}