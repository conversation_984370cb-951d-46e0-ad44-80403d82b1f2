/**
* @File Name : PausedFlowsForReportTypeSFController.cls
* @Description :
* <AUTHOR>
* @Last Modified By :
* @Last Modified On : December 20, 2024
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | December 20, 2024 |   | Initial Version
**/
@SuppressWarnings('PMD')
public class PausedFlowsForReportTypeSFController {
    @AuraEnabled(cacheable=true)
    public static List<FlowInterviewWrapper> getPausedFlowsForReportType() {
        System.debug('Starting method getPausedFlowsForReportType');
        
        // Fetch paused flows
        List<FlowInterview> pausedFlows = [
            SELECT Id, OwnerId, Owner.FirstName, Owner.LastName, IsDeleted, Name, CreatedDate, CreatedById, 
                   CreatedBy.FirstName, LastModifiedDate, LastModifiedBy.FirstName, SystemModstamp,
                   CurrentElement, InterviewLabel, PauseLabel, Guid, WasPausedFromScreen, 
                   FlowVersionViewId, InterviewStatus 
            FROM FlowInterview 
            WHERE InterviewStatus = 'Paused'
            ORDER BY CreatedById, CreatedDate DESC
        ];
        System.debug('Fetched pausedFlows: ' + pausedFlows);

        // Map to store User to Account mappings
        Map<Id, Account> userToAccountMap = new Map<Id, Account>();
        System.debug('Initialized userToAccountMap: ' + userToAccountMap);

        // Get all unique OwnerIds
        Set<Id> ownerIds = new Set<Id>();
        for (FlowInterview flow : pausedFlows) {
            ownerIds.add(flow.OwnerId);
        }
        System.debug('Collected ownerIds: ' + ownerIds);

        // Query Account object based on Users
        if (!ownerIds.isEmpty()) {
            List<User> users = [
                SELECT Id, AccountId, Account.Name 
                FROM User 
                WHERE Id IN :ownerIds
            ];

            for (User user : users) {
                if (user.AccountId != null) {
                    userToAccountMap.put(user.Id, new Account(Id = user.AccountId, Name = user.Account.Name));
                }
            }
            System.debug('Populated userToAccountMap: ' + userToAccountMap);
        } else {
            System.debug('No ownerIds found to query Accounts.');
        }

        // Wrap the results with Account data
        List<FlowInterviewWrapper> wrappedResults = new List<FlowInterviewWrapper>();
        for (FlowInterview flow : pausedFlows) {
            FlowInterviewWrapper wrapper = new FlowInterviewWrapper(flow);
            if (userToAccountMap.containsKey(flow.OwnerId)) {
                wrapper.AccountId = userToAccountMap.get(flow.OwnerId).Id;
                wrapper.AccountName = userToAccountMap.get(flow.OwnerId).Name;
            }
            wrappedResults.add(wrapper);
        }
        System.debug('Wrapped results: ' + wrappedResults);

        System.debug('Exiting method getPausedFlowsForReportType');
        return wrappedResults;
    }

    public class FlowInterviewWrapper {
        @AuraEnabled public FlowInterview flow { get; set; }
        @AuraEnabled public Id AccountId { get; set; }
        @AuraEnabled public String AccountName { get; set; }

        public FlowInterviewWrapper(FlowInterview flow) {
            this.flow = flow;
        }
    }
}