@isTest
public class mf123oppTest {
    @testSetup
    static void setupTestData() {
        // Create a test Account
        Account testAccount = new Account(
            Name = 'Test Account'
        );
        insert testAccount;
        
        // Create a test Opportunity
        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity',
            AccountId = testAccount.Id,
            CloseDate = Date.today().addDays(30),
            StageName = 'Prospecting',
            Amount = 10000,
            Type = 'New Business',
            LeadSource = 'Web',
            Probability = 50,
            CampaignId = null,
            Supporting_Docs__c = 'http://example.com/docs',
            Signed_App__c = Date.today(),
            Description = 'Test Opportunity Description'
        );
        insert testOpportunity;

        // Create a test Project related to the Opportunity
        Project__c testProject = new Project__c(
            Name = 'Test Project',
            Loan_Opportunity__c = testOpportunity.Id,
            Email_for_Servicing_Updates__c = '<EMAIL>'
        );
        insert testProject;
        
         Disbursement_Request__c testDisbursement = new Disbursement_Request__c(
            Project_lookup__c = testProject.Id,
            Amount_Approved__c = 2000
        );
        insert testDisbursement;
    }

    @isTest
    static void testGetOpportunityById() {
        // Retrieve the test Opportunity
        Opportunity testOpportunity = [SELECT Id FROM Opportunity WHERE Name = 'Test Opportunity' LIMIT 1];

        Test.startTest();
        Opportunity opp = mf123opp.getOpportunityById(testOpportunity.Id);
        Test.stopTest();
    }
    
    @isTest
    static void testIsSandbox() {
        Test.startTest();
        Boolean result = mf123opp.isSandbox();
        Test.stopTest();
    }
    
      @isTest
    static void testGetCurrentUserAccountName() {
        Test.startTest();
        String accountName = mf123opp.getCurrentUserAccountName(); 
        Test.stopTest();
    }

    @isTest
    static void testGetOpprOfProject() {
        Project__c testProject = [SELECT Id FROM Project__c WHERE Name = 'Test Project' LIMIT 1];
        Test.startTest();
        List<SObject> results = mf123opp.getOpprOfProject(testProject.Id, 'Project');
        List<SObject> results2 = mf123opp.getOpprOfProject(testProject.Id, 'Disbursement request');
        List<SObject> results3 = mf123opp.getOpprOfProject(testProject.Id, 'Requested Item');
        List<SObject> results4 = mf123opp.getOpprOfProject(testProject.Id, 'Contact');
        List<SObject> results5 = mf123opp.getOpprOfProject(testProject.Id, 'Opportunity');
        List<SObject> results6 = mf123opp.getOpprOfProject(testProject.Id, 'Account');
        Test.stopTest();
    }

    @isTest
    static void testGetDisbursementRequests() {
        Project__c testProject = [SELECT Id FROM Project__c WHERE Name = 'Test Project' LIMIT 1];
        Test.startTest();
        List<Disbursement_Request__c> disbursements = mf123opp.getDisbursementRequests(testProject.Id);
        Test.stopTest();
    }

    @isTest
    static void testGetRequestItems() {
        Disbursement_Request__c testDisbursement = [SELECT Id FROM Disbursement_Request__c WHERE Amount_Approved__c = 2000 LIMIT 1];
        Test.startTest();
        List<Requested_Item__C> requestItems = mf123opp.getRequestItems(testDisbursement.Id);
        Test.stopTest();
    }

    @isTest
    static void testGetContactItems() {
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];
        Test.startTest();
        List<Contact> contacts = mf123opp.getContactItems(testAccount.Id);
        Test.stopTest();
    }

    @isTest
    static void testGetProjectsForOpportunity() {
        // Retrieve the test Opportunity
        Opportunity testOpportunity = [SELECT Id FROM Opportunity WHERE Name = 'Test Opportunity' LIMIT 1];

        Test.startTest();
        List<mf123opp.ProjectWrapper> projectWrappers = mf123opp.getProjectsForOpportunity(testOpportunity.Id);
        Test.stopTest();

        // Validate the results
        System.assertEquals(1, projectWrappers.size(), 'Expected to find one project');
        System.assertEquals('Test Project', projectWrappers[0].project.Name, 'Expected project name to be "Test Project"');
    }
}