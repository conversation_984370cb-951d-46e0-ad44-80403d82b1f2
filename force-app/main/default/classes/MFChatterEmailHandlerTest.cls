@isTest
private class MFChatterEmailHandlerTest {

    @testSetup
    static void setupTestData() {
        // Create a FeedItem for valid thread scenarios.
        // For testing purposes, we can insert a FeedItem with a valid ParentId.
        // Here we use the current user's Id as ParentId.
        FeedItem fi = new FeedItem(
            ParentId = UserInfo.getUserId(),
            Body = 'Test Feed Item Body'
        );
        insert fi;
    }
    
    static testMethod void testHandleInboundEmail_WithValidThreadInfo_WithAttachments() {
        // Get the inserted FeedItem
        FeedItem fi = [SELECT Id FROM FeedItem LIMIT 1];
        String threadPattern = ':::thread::' + fi.Id + '::' + UserInfo.getUserId() + '::';
        // Construct email body that includes the thread pattern
        String emailBody = 'This is the comment body. ' + threadPattern;
        
        // Prepare the inbound email with attachments
        Messaging.InboundEmail email = new Messaging.InboundEmail();
        email.fromName = 'Test Sender';
        email.plainTextBody = emailBody;
        email.Subject = 'Test Subject';
        email.fromAddress = '<EMAIL>';
        email.ccAddresses = new List<String>{'<EMAIL>'};
        email.toAddresses = new List<String>{'<EMAIL>'};
        
        // Create a binary attachment
        Messaging.InboundEmail.BinaryAttachment binAttach = new Messaging.InboundEmail.BinaryAttachment();
        binAttach.fileName = 'binary.txt';
        binAttach.body = Blob.valueOf('binary content');
        email.binaryAttachments = new List<Messaging.InboundEmail.BinaryAttachment>{ binAttach };
        
        // Create a text attachment
        Messaging.InboundEmail.TextAttachment textAttach = new Messaging.InboundEmail.TextAttachment();
        textAttach.fileName = 'text.txt';
        textAttach.body = 'text content';
        email.textAttachments = new List<Messaging.InboundEmail.TextAttachment>{ textAttach };
        
        Messaging.InboundEnvelope env = new Messaging.InboundEnvelope();
        
        Test.startTest();
            MFChatterEmailHandler handler = new MFChatterEmailHandler();
            Messaging.InboundEmailResult result = handler.handleInboundEmail(email, env);
        Test.stopTest();
        
    }
    
    static testMethod void testHandleInboundEmail_WithValidThreadInfo_NoAttachments() {
        // Get the inserted FeedItem
        FeedItem fi = [SELECT Id FROM FeedItem LIMIT 1];
        String threadPattern = ':::thread::' + fi.Id + '::' + UserInfo.getUserId() + '::';
        String emailBody = 'This is a comment without attachments. ' + threadPattern;
        
        Messaging.InboundEmail email = new Messaging.InboundEmail();
        email.fromName = 'Test Sender';
        email.plainTextBody = emailBody;
        email.Subject = 'No Attachments';
        email.fromAddress = '<EMAIL>';
        email.ccAddresses = new List<String>{'<EMAIL>'};
        email.toAddresses = new List<String>{'<EMAIL>'};
        // No attachments
        email.binaryAttachments = new List<Messaging.InboundEmail.BinaryAttachment>();
        email.textAttachments = new List<Messaging.InboundEmail.TextAttachment>();
        
        Messaging.InboundEnvelope env = new Messaging.InboundEnvelope();
        
        Test.startTest();
            MFChatterEmailHandler handler = new MFChatterEmailHandler();
            Messaging.InboundEmailResult result = handler.handleInboundEmail(email, env);
        Test.stopTest();
        
        // Verify that a FeedComment was inserted and RelatedRecordId is null.
        List<FeedComment> comments = [SELECT Id, FeedItemId, CommentBody, RelatedRecordId 
                                      FROM FeedComment 
                                      WHERE FeedItemId = :fi.Id];
        System.assertEquals(1, comments.size(), 'One FeedComment should be inserted.');
        FeedComment fc = comments[0];
        System.assertEquals('This is a comment without attachments. ' + threadPattern, fc.CommentBody, 'Comment body should match.');
        System.assertEquals(null, fc.RelatedRecordId, 'RelatedRecordId should be null when there are no attachments.');
    }
    
    static testMethod void testHandleInboundEmail_InvalidThreadInfo() {
        // Construct an email body with no valid thread information.
        String emailBody = 'This email does not have valid thread info.';
        
        Messaging.InboundEmail email = new Messaging.InboundEmail();
        email.fromName = 'Invalid Sender';
        email.plainTextBody = emailBody;
        email.Subject = 'Invalid Thread';
        email.fromAddress = '<EMAIL>';
        email.ccAddresses = new List<String>();
        email.toAddresses = new List<String>();
        email.binaryAttachments = new List<Messaging.InboundEmail.BinaryAttachment>();
        email.textAttachments = new List<Messaging.InboundEmail.TextAttachment>();
        
        Messaging.InboundEnvelope env = new Messaging.InboundEnvelope();
        
        Test.startTest();
            MFChatterEmailHandler handler = new MFChatterEmailHandler();
            Messaging.InboundEmailResult result = handler.handleInboundEmail(email, env);
        Test.stopTest();
        
        // Assert that no FeedComment was inserted because thread info is missing.
        List<FeedComment> comments = [SELECT Id FROM FeedComment WHERE CommentBody = :emailBody];
        System.assertEquals(0, comments.size(), 'No FeedComment should be inserted for an email without thread info.');
    }
    
    static testMethod void testHandleInboundEmail_FeedItemNotFound() {
        // Construct an email with thread info referencing a non-existent FeedItem.
        String fakeFeedItemId = '0D5xx0000000001';  // Use an ID that doesn't exist.
        String threadPattern = ':::thread::' + fakeFeedItemId + '::' + UserInfo.getUserId() + '::';
        String emailBody = 'Email with non-existent feed item. ' + threadPattern;
        
        Messaging.InboundEmail email = new Messaging.InboundEmail();
        email.fromName = 'Test Sender';
        email.plainTextBody = emailBody;
        email.Subject = 'Non-existent FeedItem';
        email.fromAddress = '<EMAIL>';
        email.ccAddresses = new List<String>();
        email.toAddresses = new List<String>();
        email.binaryAttachments = new List<Messaging.InboundEmail.BinaryAttachment>();
        email.textAttachments = new List<Messaging.InboundEmail.TextAttachment>();
        
        Messaging.InboundEnvelope env = new Messaging.InboundEnvelope();
        
        Test.startTest();
            MFChatterEmailHandler handler = new MFChatterEmailHandler();
            Messaging.InboundEmailResult result = handler.handleInboundEmail(email, env);
        Test.stopTest();
        
        // Since the FeedItem doesn't exist, no FeedComment should be inserted.
        List<FeedComment> comments = [SELECT Id FROM FeedComment WHERE CommentBody LIKE '%non-existent feed item%'];
        System.assertEquals(0, comments.size(), 'No FeedComment should be inserted when FeedItem is not found.');
    }
    
    static testMethod void testParseAndExtractMethods() {
        String sampleBody = 'This is the body. :::thread::abc123::user456:: Extra text. On Wed, Jul 7, 2020 wrote: previous email';
        
        Map<String, String> parsed = MFChatterEmailHandler.parseEmailBody(sampleBody);
        System.assertEquals('abc123', parsed.get('feedItemID'), 'feedItemID should be parsed correctly.');
        System.assertEquals('user456', parsed.get('userID'), 'userID should be parsed correctly.');
        
        String cleaned = MFChatterEmailHandler.extractCleanedBody(sampleBody);
        System.assert(cleaned.contains('This is the body. :::thread::abc123::user456:: Extra text.'), 'Cleaned body should remove the "On Wed, ..." part.');
    }
}