@SuppressWarnings('PMD')
public without sharing class CustomCommentFeederController {
    
    @AuraEnabled(cacheable=true)
    public static Opportunity getOpportunityType(Id oppId) {
        try {
            Opportunity opp = [SELECT Type FROM Opportunity WHERE Id = :oppId LIMIT 1];
            System.debug('Retrieved Opportunity: ' + opp);
            return opp;
        } catch (Exception e) {
            System.debug('Error retrieving Opportunity: ' + e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }
    
    // @AuraEnabled
    // public static List<returnWrapper> getFeedItemList( String parentId) {
        
    //     try {
    //         List<FeedItem> feedItems = [
    //             SELECT
    //                 Id,
    //                 Title,
    //                 ParentId,
    //                 Type,
    //                 Body,
    //                 TYPEOF CreatedBy WHEN User THEN Id END,
    //                 TYPEOF CreatedBy WHEN User THEN FirstName END,
    //                 TYPEOF CreatedBy WHEN User THEN LastName END,
    //                 TYPEOF CreatedBy WHEN User THEN SmallPhotoUrl END,
    //                 CreatedDate,
    //                 (SELECT Id, FeedItemId, CommentBody, CreatedById, ParentId,
    //                 TYPEOF CreatedBy WHEN User THEN Id END,
    //                 TYPEOF CreatedBy WHEN User THEN FirstName END,
    //                 TYPEOF CreatedBy WHEN User THEN LastName END,
    //                 TYPEOF CreatedBy WHEN User THEN SmallPhotoUrl END, 
    //                 CreatedDate
    //                 FROM FeedComments)
    //             FROM FeedItem
    //             WHERE 
    //             ParentId = :parentId
    //             AND Type = 'TextPost'
    //             ORDER BY CreatedDate DESC
    //         ];
            
    //         System.debug('Retrieved FeedItems: ' + feedItems);
    //         System.debug('Retrieved FeedItems.size: ' + feedItems.size());

    //         List<returnWrapper> result = new List<returnWrapper>();
            
    //         for (FeedItem feedItem : feedItems) {
    //             returnWrapper wrapper = new returnWrapper();
    //             wrapper.feedItemObj = feedItem;
    //             wrapper.feedComments = feedItem.FeedComments;
    //             result.add(wrapper);
    //             system.System.debug('result '+result);
    //             System.debug('Added to wrapper: FeedItem Id ' + feedItem.Id + ' with comments ' + feedItem.FeedComments);
    //         }
            
    //         return result;
    //     } catch (Exception e) {
    //         System.debug('Error retrieving FeedItems: ' + e.getMessage());
    //         throw new AuraHandledException(e.getMessage());
    //     }
    // }

    // Extract ContentDocumentId from Body using Regex
    public static Set<Id> extractContentDocumentIds(String bodyText) {
        Set<Id> contentDocumentIds = new Set<Id>();
        if (String.isNotBlank(bodyText)) {
            Matcher match = Pattern.compile('sfdc://(\\w{18})').matcher(bodyText);
            while (match.find()) {
                contentDocumentIds.add(match.group(1));
            }
        }
        return contentDocumentIds;
    }

    @AuraEnabled
    public static List<ReturnWrapper> getFeedItemList(String parentId, String sortOrder) {
        try {
            String orderByClause = 'ORDER BY CreatedDate ' + sortOrder;
            List<FeedItem> feedItems = Database.query(
                'SELECT Id, Title, ParentId, Type, Body, ' +
                'TYPEOF CreatedBy WHEN User THEN Id END, ' +
                'TYPEOF CreatedBy WHEN User THEN FirstName END, ' +
                'TYPEOF CreatedBy WHEN User THEN LastName END, ' +
                'TYPEOF CreatedBy WHEN User THEN SmallPhotoUrl END, ' +
                'CreatedDate, ' +
                '(SELECT Id, FeedItemId, CommentBody, CreatedById, ParentId, ' +
                'TYPEOF CreatedBy WHEN User THEN Id END, ' +
                'TYPEOF CreatedBy WHEN User THEN FirstName END, ' +
                'TYPEOF CreatedBy WHEN User THEN LastName END, ' +
                'TYPEOF CreatedBy WHEN User THEN SmallPhotoUrl END, ' +
                'CreatedDate FROM FeedComments) ' +
                'FROM FeedItem ' +
                'WHERE ParentId = :parentId AND Type = \'TextPost\' ' +
                'AND Visibility = \'AllUsers\' ' + orderByClause
            );

            System.debug('Retrieved FeedItems: ' + feedItems);
            System.debug('Retrieved FeedItems.size: ' + feedItems.size());

            // Extract ContentDocumentId from FeedItem.Body and FeedComment.Body
            //Pattern pattern = Pattern.compile('refid=([a-zA-Z0-9]+)');
            Pattern pattern = Pattern.compile('sfdc://([a-zA-Z0-9]+)');

            Map<Id, Set<Id>> feedItemToContentDocsMap = new Map<Id, Set<Id>>();
            Map<Id, Set<Id>> feedCommentToContentDocsMap = new Map<Id, Set<Id>>();
            Set<Id> allContentDocumentIds = new Set<Id>();

            for (FeedItem fi : feedItems) {
                Set<Id> contentDocIds = new Set<Id>();
                if (fi.Body != null) {
                    System.debug('fi.Body ' + fi.Body);
                    Matcher matcher = pattern.matcher(fi.Body);
                    while (matcher.find()) {
                        System.debug('Matched ContentDocumentId: ' + matcher.group(1));
                        contentDocIds.add(matcher.group(1)); 
                    }
                }
                if (!contentDocIds.isEmpty()) {
                    feedItemToContentDocsMap.put(fi.Id, contentDocIds);
                    allContentDocumentIds.addAll(contentDocIds);
                }

                for (FeedComment fc : fi.FeedComments) {
                    Set<Id> commentContentDocIds = new Set<Id>();
                    if (fc.CommentBody != null) {
                        Matcher commentMatcher = pattern.matcher(fc.CommentBody);
                        while (commentMatcher.find()) {
                            commentContentDocIds.add(commentMatcher.group(1));
                        }
                    }
                    if (!commentContentDocIds.isEmpty()) {
                        feedCommentToContentDocsMap.put(fc.Id, commentContentDocIds);
                        allContentDocumentIds.addAll(commentContentDocIds);
                    }
                }
            }

            System.debug('FeedItem -> ContentDocumentIds Map: ' + feedItemToContentDocsMap);
            System.debug('FeedComment -> ContentDocumentIds Map: ' + feedCommentToContentDocsMap);

            // Query ContentDownloadUrls from ContentDistribution
            Map<Id, String> contentDocToDownloadUrlMap = new Map<Id, String>();
            Map<Id, String> contentDocToDisbutionPublicUrlMap = new Map<Id, String>();

            if (!allContentDocumentIds.isEmpty()) {
                List<ContentDistribution> contentDistributions = [SELECT ContentDocumentId, ContentDownloadUrl, DistributionPublicUrl
                                                                FROM ContentDistribution
                                                                WHERE ContentDocumentId IN :allContentDocumentIds];

                for (ContentDistribution cd : contentDistributions) {
                    contentDocToDownloadUrlMap.put(cd.ContentDocumentId, cd.ContentDownloadUrl);
                    contentDocToDisbutionPublicUrlMap.put(cd.ContentDocumentId, cd.DistributionPublicUrl);
                }
            }

            // Map FeedItemId & FeedCommentId to ContentDownloadUrls
            Map<Id, List<String>> feedItemToDownloadUrlsMap = new Map<Id, List<String>>();
            Map<Id, List<String>> feedCommentToDownloadUrlsMap = new Map<Id, List<String>>();

            Map<Id, List<String>> feedItemToDisbutionPublicUrlMap = new Map<Id, List<String>>();
            Map<Id, List<String>> feedCommentToDisbutionPublicUrlMap = new Map<Id, List<String>>();

            for (Id feedItemId : feedItemToContentDocsMap.keySet()) {
                List<String> urls = new List<String>();
                List<String> publicUrl = new List<String>();
                for (Id contentDocId : feedItemToContentDocsMap.get(feedItemId)) {
                    if (contentDocToDownloadUrlMap.containsKey(contentDocId)) {
                        urls.add(contentDocToDownloadUrlMap.get(contentDocId));
                        publicUrl.add(contentDocToDisbutionPublicUrlMap.get(contentDocId));
                    }
                }
                feedItemToDownloadUrlsMap.put(feedItemId, urls);
                feedItemToDisbutionPublicUrlMap.put(feedItemId, publicUrl);
            }

            for (Id feedCommentId : feedCommentToContentDocsMap.keySet()) {
                List<String> urls = new List<String>();
                List<String> publicUrl = new List<String>();
                for (Id contentDocId : feedCommentToContentDocsMap.get(feedCommentId)) {
                    if (contentDocToDownloadUrlMap.containsKey(contentDocId)) {
                        urls.add(contentDocToDownloadUrlMap.get(contentDocId));
                        publicUrl.add(contentDocToDisbutionPublicUrlMap.get(contentDocId));
                    }
                }
                feedCommentToDownloadUrlsMap.put(feedCommentId, urls);
                feedCommentToDisbutionPublicUrlMap.put(feedCommentId, publicUrl);
            }

            System.debug('FeedItem -> ContentDownloadUrls Map: ' + feedItemToDownloadUrlsMap);
            System.debug('FeedComment -> ContentDownloadUrls Map: ' + feedCommentToDownloadUrlsMap);

        
             // Prepare a list to hold topic assignments
            List<TopicAssignment> topicAssignments = [
                SELECT Id, TopicId, EntityId 
                FROM TopicAssignment 
                WHERE EntityId IN :feedItems
            ];
            
            // Create a map to hold a list of topic names by FeedItem Id
            Map<Id, List<String>> feedItemTopicsMap = new Map<Id, List<String>>();
            
             // Fetch topic names for the topic assignments
            for (TopicAssignment topicAssignment : topicAssignments) {
                Topic topic = [SELECT Name FROM Topic WHERE Id = :topicAssignment.TopicId LIMIT 1];
            
             // Add the topic to the corresponding FeedItem's topic list
            	if (!feedItemTopicsMap.containsKey(topicAssignment.EntityId)) {
                    feedItemTopicsMap.put(topicAssignment.EntityId, new List<String>());
                }
                feedItemTopicsMap.get(topicAssignment.EntityId).add(topic.Name);
            }

            List<ReturnWrapper> result = new List<ReturnWrapper>();

            for (FeedItem feedItem : feedItems) {
                ReturnWrapper wrapper = new ReturnWrapper();
                String body = feedItem.body;
                body = body.replaceAll('alt="[^"]*"', '');
                feedItem.body = body;  
                wrapper.feedItemObj = feedItem;

                List<FeedComment> updatedComments = new List<FeedComment>();
                for (FeedComment comment : feedItem.FeedComments) {
                    comment.CommentBody = comment.CommentBody.replaceAll('alt="[^"]*"', '');
                    updatedComments.add(comment);
                }
                wrapper.feedComments = updatedComments;
                //wrapper.feedComments = feedItem.FeedComments;
                
                // Add the topic name(s) associated with the feed item
                wrapper.topics = new List<String>();
                if (feedItemTopicsMap.containsKey(feedItem.Id)) {
                    wrapper.topics.addAll(feedItemTopicsMap.get(feedItem.Id));
                }

                wrapper.contentDownloadUrls = new Map<Id, List<String>>();
                if (feedItemToDownloadUrlsMap.containsKey(feedItem.Id)) {
                    wrapper.contentDownloadUrls.put(feedItem.Id, feedItemToDownloadUrlsMap.get(feedItem.Id));
                    wrapper.distributionPublicUrl.put(feedItem.Id, feedItemToDisbutionPublicUrlMap.get(feedItem.Id));
                }

                // Add ContentDownloadUrls for FeedComments
                for (FeedComment fc : feedItem.FeedComments) {
                    if (feedCommentToDownloadUrlsMap.containsKey(fc.Id)) {
                        wrapper.contentDownloadUrls.put(fc.Id, feedCommentToDownloadUrlsMap.get(fc.Id));
                        wrapper.distributionPublicUrl.put(fc.Id, feedCommentToDisbutionPublicUrlMap.get(fc.Id));
                    }
                }

                //1
                // Add ContentDownloadUrls for FeedItem
                // if (feedItemToContentDocsMap.containsKey(feedItem.Id)) {
                //     for (Id docId : feedItemToContentDocsMap.get(feedItem.Id)) {
                //         if (contentDocToDownloadUrlMap.containsKey(docId)) {
                //             wrapper.contentDownloadUrls.put(docId, contentDocToDownloadUrlMap.get(docId));
                //         }
                //     }
                // }

                // // Add ContentDownloadUrls for FeedComments
                // for (FeedComment fc : feedItem.FeedComments) {
                //     if (feedCommentToContentDocsMap.containsKey(fc.Id)) {
                //         for (Id docId : feedCommentToContentDocsMap.get(fc.Id)) {
                //             if (contentDocToDownloadUrlMap.containsKey(docId)) {
                //                 wrapper.contentDownloadUrls.put(docId, contentDocToDownloadUrlMap.get(docId));
                //             }
                //         }
                //     }
                // }

                //1
                // Add ContentDownloadUrls
                // wrapper.contentDownloadUrls = new List<String>();
                // for (Id contentId : extractContentDocumentIds(feedItem.Body)) {
                //     if (contentDownloadUrls.containsKey(contentId)) {
                //         wrapper.contentDownloadUrls.add(contentDownloadUrls.get(contentId));
                //     }
                // }

                // for (FeedComment comment : feedItem.FeedComments) {
                //     for (Id contentId : extractContentDocumentIds(comment.CommentBody)) {
                //         if (contentDownloadUrls.containsKey(contentId)) {
                //             wrapper.contentDownloadUrls.add(contentDownloadUrls.get(contentId));
                //         }
                //     }
                // }
                result.add(wrapper);
                System.debug('Added to wrapper: FeedItem Id ' + feedItem.Id + ' with comments ' + feedItem.FeedComments);
            }

            return result;
        } catch (Exception e) {
            System.debug('Error retrieving FeedItems: ' + e.getMessage());
            throw new AuraHandledException(e.getMessage() + ' '+ e.getStackTraceString());
        }
    }

    @AuraEnabled
    public static void deleteFeedItem(String feedItemId) {
        try {
            FeedItem feedItem = [SELECT Id FROM FeedItem WHERE Id = :feedItemId LIMIT 1];
            delete feedItem;
    
            List<FeedComment> commentsToDelete = [SELECT Id FROM FeedComment WHERE FeedItemId = :feedItemId];
            delete commentsToDelete;
        } catch (Exception e) {
            throw new AuraHandledException('Error deleting FeedItem: ' + e.getMessage());
        }
    }

    @AuraEnabled
    public static void deleteFeedCommentItem(String feedCommentId) {
        try {
            // FeedItem feedItem = [SELECT Id FROM FeedItem WHERE Id = :feedItemId LIMIT 1];
            // delete feedItem;
    
            FeedComment commentsToDelete = [SELECT Id FROM FeedComment WHERE Id = :feedCommentId];
            delete commentsToDelete;
        } catch (Exception e) {
            throw new AuraHandledException('Error deleting FeedItem: ' + e.getMessage());
        }
    }
    
    @AuraEnabled
    public static void addTopicToFeedItem(Id feedItemId, List<String> topicNames) {
        System.debug('addTopicToFeedItem ' + feedItemId + '  ' + topicNames);
        // Step 1: Query existing topics outside the loop
        Map<String, Topic> existingTopicsMap = new Map<String, Topic>();
        for (Topic topic : [SELECT Id, Name FROM Topic WHERE Name IN :topicNames]) {
            existingTopicsMap.put(topic.Name, topic);
        }
        
        List<TopicAssignment> topicAssignments = new List<TopicAssignment>();
        
        // Step 2: Loop through the provided topic names
        for (String topicName : topicNames) {
            Topic topic;
            // Check if the topic exists in the map
            if (existingTopicsMap.containsKey(topicName)) {
                topic = existingTopicsMap.get(topicName);
                System.debug('Using existing topic: ' + topic);
            } else {
                topic = new Topic(Name = topicName);
                insert topic;
                System.debug('Created new topic: ' + topic);
            }
            TopicAssignment topicAssignment = new TopicAssignment(
                EntityId = feedItemId,   
                TopicId = topic.Id        
            );
            topicAssignments.add(topicAssignment);
        }
        // Insert all topic assignments in one go
        insert topicAssignments;
    }

    @AuraEnabled
    public static FeedItem createFeedItemRec(FeedItem feedItemRec, String sid) {
        try {
            String body = feedItemRec.Body;
            
            // Maps to store data for RTA images
            Map<String, Blob> rtaRefIdToBlob = new Map<String, Blob>(); // refId -> image Blob
            Map<String, String> rtaRefIdToOldTag = new Map<String, String>(); // refId -> original img tag
            Map<String, String> rtaRefIdToContentType = new Map<String, String>(); // refId -> content type
                
            Boolean isSandbox = mf123opp.isSandbox();
            String imgTagPattern;
            // Process RTA images
            if(isSandbox){
                imgTagPattern = '<img[^>]+src="/mf/servlet/rtaImage\\?refid=(\\w+)"[^>]*>';
            } else{
                imgTagPattern = '<img[^>]+src="/servlet/rtaImage\\?refid=(\\w+)"[^>]*>';
            }
                
            Pattern p = Pattern.compile(imgTagPattern);
            Matcher m = p.matcher(body);
            
            while (m.find()) {
                String refId = m.group(1); // Extract the refid from the RTA URL
                String oldImgTag = m.group(0); // Capture the entire original img tag
                
                // Fetch the image from the RTA URL
                String rtaUrl;
                if(isSandbox){
                    rtaUrl = URL.getSalesforceBaseUrl().toExternalForm() + '/mf/servlet/rtaImage?refid=' + refId;
                }
                else{
                    rtaUrl = URL.getSalesforceBaseUrl().toExternalForm() + '/servlet/rtaImage?refid=' + refId;
                }
                HttpRequest req = new HttpRequest();
                req.setEndpoint(rtaUrl);
                req.setMethod('GET');
                req.setHeader('Authorization', 'Bearer ' + UserInfo.getSessionId());
                req.setHeader('Cookie', 'sid=' + sid + ';');
                Http http = new Http();
                HTTPResponse res = http.send(req);
                
                if (res.getStatusCode() == 200) {
                    // Store the image data and metadata in maps
                    Blob imageBlob = res.getBodyAsBlob();
                    String contentType = res.getHeader('Content-Type');
                    
                    rtaRefIdToBlob.put(refId, imageBlob);
                    rtaRefIdToOldTag.put(refId, oldImgTag);
                    rtaRefIdToContentType.put(refId, contentType);
                } else {
                    throw new AuraHandledException('Failed to fetch image for refid: ' + refId);
                }
            }
            
            // Maps to store data for base64 images
            Map<String, Blob> base64KeyToBlob = new Map<String, Blob>(); // unique key -> image Blob
            Map<String, String> base64KeyToOldTag = new Map<String, String>(); // unique key -> original img tag
            Map<String, String> base64KeyToFileType = new Map<String, String>(); // unique key -> file type
            
            // Process base64 images
            String base64Pattern = '<img[^>]+src="data:image/([^;]+);base64,([^"]+)"[^>]*>';
            Pattern base64P = Pattern.compile(base64Pattern);
            Matcher base64M = base64P.matcher(body);
            
            while (base64M.find()) {
                String fileType = base64M.group(1);  // Extract file type (e.g., jpeg, png)
                String base64Data = base64M.group(2); // Extract base64 string
                String oldImgTag = base64M.group(0);  // Capture the original img tag
                String uniqueKey = 'Base64_' + System.currentTimeMillis() + '_' + Math.round(Math.random() * 1000); // Unique key
                
                // Convert base64 to Blob and store in maps
                Blob imageBlob = EncodingUtil.base64Decode(base64Data);
                base64KeyToBlob.put(uniqueKey, imageBlob);
                base64KeyToOldTag.put(uniqueKey, oldImgTag);
                base64KeyToFileType.put(uniqueKey, fileType);
            }
            
            // Process all RTA images - Insert ContentVersions
            List<ContentVersion> contentVersionsToInsert = new List<ContentVersion>();
            Map<String, String> refIdToNewUrl = new Map<String, String>(); // refId -> new URL
            
            for (String refId : rtaRefIdToBlob.keySet()) {
                ContentVersion cv = new ContentVersion();
                cv.VersionData = rtaRefIdToBlob.get(refId);
                cv.Title = 'Image_' + refId;
                String fileExtension = rtaRefIdToContentType.get(refId).split('/')[1];
                cv.PathOnClient = 'image.' + fileExtension;
                cv.FirstPublishLocationId = feedItemRec.ParentId;
                contentVersionsToInsert.add(cv);
            }
            
            // Process all base64 images - Add to ContentVersions list
            for (String key : base64KeyToBlob.keySet()) {
                ContentVersion cv = new ContentVersion();
                cv.VersionData = base64KeyToBlob.get(key);
                cv.Title = 'Base64Image_' + key;
                cv.PathOnClient = 'image.' + base64KeyToFileType.get(key);
                cv.FirstPublishLocationId = feedItemRec.ParentId;
                contentVersionsToInsert.add(cv);
            }
            
            // Perform single DML operation for all ContentVersions
            if (!contentVersionsToInsert.isEmpty()) {
                insert contentVersionsToInsert;
                
                // Query ContentDocumentIds
                List<ContentVersion> insertedCVs = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id IN :contentVersionsToInsert];
                Map<Id, String> cvIdToDocId = new Map<Id, String>();
                for (ContentVersion cv : insertedCVs) {
                    cvIdToDocId.put(cv.Id, cv.ContentDocumentId);
                }
                
                // Map new URLs for RTA images
                Integer index = 0;
                for (String refId : rtaRefIdToBlob.keySet()) {
                    String contentDocumentId = cvIdToDocId.get(contentVersionsToInsert[index].Id);
                    String newUrl = 'sfdc://' + contentDocumentId;
                    refIdToNewUrl.put(refId, newUrl);
                    index++;
                }
                
                // Map new URLs for base64 images
                Map<String, String> base64KeyToNewUrl = new Map<String, String>(); // unique key -> new URL
                for (String key : base64KeyToBlob.keySet()) {
                    String contentDocumentId = cvIdToDocId.get(contentVersionsToInsert[index].Id);
                    String newUrl = 'sfdc://' + contentDocumentId;
                    base64KeyToNewUrl.put(key, newUrl);
                    index++;
                }
                
                // Replace old tags with new URLs in body
                for (String refId : refIdToNewUrl.keySet()) {
                    String oldImgTag = rtaRefIdToOldTag.get(refId);
                    String newImgTag = '<img src="' + refIdToNewUrl.get(refId) + '"></img>';
                    body = body.replace(oldImgTag, newImgTag);
                }
                
                for (String key : base64KeyToNewUrl.keySet()) {
                    String oldImgTag = base64KeyToOldTag.get(key);
                    String newImgTag = '<img src="' + base64KeyToNewUrl.get(key) + '"></img>';
                    body = body.replace(oldImgTag, newImgTag);
                }
            }
            
            // Update and insert the FeedItem
            feedItemRec.Body = body;
            feedItemRec.Visibility = 'AllUsers';
            feedItemRec.IsRichText = true;
            insert feedItemRec;
            
            return feedItemRec;
        } catch (Exception e) {
            throw new AuraHandledException('Error creating FeedItem: ' + e.getMessage());
        }
    }

    @AuraEnabled
    public static FeedComment createFeedCommentRec(FeedComment feedCommentRec, String sid) {
        System.debug('feedCommentRec '+feedCommentRec);

        try {
            String body = feedCommentRec.CommentBody;
            
            // Maps to store data for RTA images
            Map<String, Blob> rtaRefIdToBlob = new Map<String, Blob>(); // refId -> image Blob
            Map<String, String> rtaRefIdToOldTag = new Map<String, String>(); // refId -> original img tag
            Map<String, String> rtaRefIdToContentType = new Map<String, String>(); // refId -> content type
            
            Boolean isSandbox = mf123opp.isSandbox();
            String imgTagPattern;
            // Process RTA images
            if(isSandbox){
                imgTagPattern = '<img[^>]+src="/mf/servlet/rtaImage\\?refid=(\\w+)"[^>]*>';
            }else{
                imgTagPattern = '<img[^>]+src="/servlet/rtaImage\\?refid=(\\w+)"[^>]*>';
            }
                
            Pattern p = Pattern.compile(imgTagPattern);
            Matcher m = p.matcher(body);
            
            while (m.find()) {
                String refId = m.group(1); // Extract the refid from the RTA URL
                String oldImgTag = m.group(0); // Capture the entire original img tag
                
                // Fetch the image from the RTA URL
                String rtaUrl;
                if(isSandbox){
                    rtaUrl = URL.getSalesforceBaseUrl().toExternalForm() + '/mf/servlet/rtaImage?refid=' + refId;
                }else{
                    rtaUrl = URL.getSalesforceBaseUrl().toExternalForm() + '/servlet/rtaImage?refid=' + refId;
                }
                HttpRequest req = new HttpRequest();
                req.setEndpoint(rtaUrl);
                req.setMethod('GET');
                req.setHeader('Authorization', 'Bearer ' + UserInfo.getSessionId());
                req.setHeader('Cookie', 'sid=' + sid + ';');
                Http http = new Http();
                HTTPResponse res = http.send(req);
                
                if (res.getStatusCode() == 200) {
                    // Store the image data and metadata in maps
                    Blob imageBlob = res.getBodyAsBlob();
                    String contentType = res.getHeader('Content-Type');
                    
                    rtaRefIdToBlob.put(refId, imageBlob);
                    rtaRefIdToOldTag.put(refId, oldImgTag);
                    rtaRefIdToContentType.put(refId, contentType);
                } else {
                    throw new AuraHandledException('Failed to fetch image for refid: ' + refId);
                }
            }
            
            // Maps to store data for base64 images
            Map<String, Blob> base64KeyToBlob = new Map<String, Blob>(); // unique key -> image Blob
            Map<String, String> base64KeyToOldTag = new Map<String, String>(); // unique key -> original img tag
            Map<String, String> base64KeyToFileType = new Map<String, String>(); // unique key -> file type
            
            // Process base64 images
            String base64Pattern = '<img[^>]+src="data:image/([^;]+);base64,([^"]+)"[^>]*>';
            Pattern base64P = Pattern.compile(base64Pattern);
            Matcher base64M = base64P.matcher(body);
            
            while (base64M.find()) {
                String fileType = base64M.group(1);  // Extract file type (e.g., jpeg, png)
                String base64Data = base64M.group(2); // Extract base64 string
                String oldImgTag = base64M.group(0);  // Capture the original img tag
                String uniqueKey = 'Base64_' + System.currentTimeMillis() + '_' + Math.round(Math.random() * 1000); // Unique key
                
                // Convert base64 to Blob and store in maps
                Blob imageBlob = EncodingUtil.base64Decode(base64Data);
                base64KeyToBlob.put(uniqueKey, imageBlob);
                base64KeyToOldTag.put(uniqueKey, oldImgTag);
                base64KeyToFileType.put(uniqueKey, fileType);
            }
            
            // Process all RTA images - Insert ContentVersions
            List<ContentVersion> contentVersionsToInsert = new List<ContentVersion>();
            Map<String, String> refIdToNewUrl = new Map<String, String>(); // refId -> new URL
            
            for (String refId : rtaRefIdToBlob.keySet()) {
                ContentVersion cv = new ContentVersion();
                cv.VersionData = rtaRefIdToBlob.get(refId);
                cv.Title = 'Image_' + refId;
                String fileExtension = rtaRefIdToContentType.get(refId).split('/')[1];
                cv.PathOnClient = 'image.' + fileExtension;
                cv.FirstPublishLocationId = feedCommentRec.ParentId;
                contentVersionsToInsert.add(cv);
            }
            
            // Process all base64 images - Add to ContentVersions list
            for (String key : base64KeyToBlob.keySet()) {
                ContentVersion cv = new ContentVersion();
                cv.VersionData = base64KeyToBlob.get(key);
                cv.Title = 'Base64Image_' + key;
                cv.PathOnClient = 'image.' + base64KeyToFileType.get(key);
                cv.FirstPublishLocationId = feedCommentRec.ParentId;
                contentVersionsToInsert.add(cv);
            }
            
            // Perform single DML operation for all ContentVersions
            if (!contentVersionsToInsert.isEmpty()) {
                insert contentVersionsToInsert;
                
                // Query ContentDocumentIds
                List<ContentVersion> insertedCVs = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id IN :contentVersionsToInsert];
                Map<Id, String> cvIdToDocId = new Map<Id, String>();
                for (ContentVersion cv : insertedCVs) {
                    cvIdToDocId.put(cv.Id, cv.ContentDocumentId);
                }
                
                // Map new URLs for RTA images
                Integer index = 0;
                for (String refId : rtaRefIdToBlob.keySet()) {
                    String contentDocumentId = cvIdToDocId.get(contentVersionsToInsert[index].Id);
                    String newUrl = 'sfdc://' + contentDocumentId;
                    refIdToNewUrl.put(refId, newUrl);
                    index++;
                }
                
                // Map new URLs for base64 images
                Map<String, String> base64KeyToNewUrl = new Map<String, String>(); // unique key -> new URL
                for (String key : base64KeyToBlob.keySet()) {
                    String contentDocumentId = cvIdToDocId.get(contentVersionsToInsert[index].Id);
                    String newUrl = 'sfdc://' + contentDocumentId;
                    base64KeyToNewUrl.put(key, newUrl);
                    index++;
                }
                
                // Replace old tags with new URLs in body
                for (String refId : refIdToNewUrl.keySet()) {
                    String oldImgTag = rtaRefIdToOldTag.get(refId);
                    String newImgTag = '<img src="' + refIdToNewUrl.get(refId) + '"></img>';
                    body = body.replace(oldImgTag, newImgTag);
                }
                
                for (String key : base64KeyToNewUrl.keySet()) {
                    String oldImgTag = base64KeyToOldTag.get(key);
                    String newImgTag = '<img src="' + base64KeyToNewUrl.get(key) + '"></img>';
                    body = body.replace(oldImgTag, newImgTag);
                }
            }
            
            // Update and insert the FeedItem
            feedCommentRec.CommentBody = body;
            //feedCommentRec.Visibility = 'AllUsers';
            feedCommentRec.IsRichText = true;
            insert feedCommentRec;
            
            //return feedItemRec;
        } catch (Exception e) {
            throw new AuraHandledException('Error creating FeedItem: ' + e.getMessage());
        }

        // try {
        // 	insert feedCommentRec;
        // }
        // catch(Exception e){
        //      System.debug('Exception occurred while inserting Post Trackers: ' + e.getMessage());
    	// 	 System.debug('Stack Trace: ' + e.getStackTraceString());
        //     System.debug('after feedCommentRec '+feedCommentRec);
        // }
        
        return [SELECT Id, CommentBody, CreatedById, CreatedDate, FeedItemId FROM FeedComment WHERE Id = :feedCommentRec.Id];
    }

    public class returnWrapper {
        @AuraEnabled
        public FeedItem feedItemObj;
        @AuraEnabled
        public List<FeedComment> feedComments;
        @AuraEnabled
        public List<String> topics;
        @AuraEnabled
        public Map<Id, List<String>> contentDownloadUrls;
        @AuraEnabled
        public Map<Id, List<String>> distributionPublicUrl;
        
        public ReturnWrapper() {
            feedComments = new List<FeedComment>();
            topics = new List<String>();
            distributionPublicUrl = new Map<Id, List<String>>();
        }
    }
}