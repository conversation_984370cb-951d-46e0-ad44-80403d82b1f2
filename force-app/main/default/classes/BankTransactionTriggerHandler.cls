@SuppressWarnings('PMD')
public class BankTransactionTriggerHandler {
    
    public static void assignMFCategory(List<Bank_Transaction__c> newTransactions, Map<Id, Bank_Transaction__c> oldMap) {

        // Filter records that need processing
        List<Bank_Transaction__c> transactionsToProcess = new List<Bank_Transaction__c>();
        
        if(oldMap == null) { // Insert scenario - process all
            transactionsToProcess = newTransactions;
        } else { 
            // Update scenario - process only changed records
            
            for(Bank_Transaction__c newTrans : newTransactions) {
                Bank_Transaction__c oldTrans = oldMap.get(newTrans.Id);
                if(newTrans.Category__c != oldTrans.Category__c || 
                   newTrans.Sub_Category__c != oldTrans.Sub_Category__c) {
                    transactionsToProcess.add(newTrans);
                }
            }
            
        }

        // Exit if no records to process
        if(transactionsToProcess.isEmpty()) {
            return;
        }

        // Collect case-insensitive category/subcategory pairs
        Set<String> transactionPairs = new Set<String>();
        for(Bank_Transaction__c bt : transactionsToProcess) {
            if(String.isNotBlank(bt.Category__c) && String.isNotBlank(bt.Sub_Category__c)) {
                String normalizedKey = bt.Category__c.toLowerCase() + '|' + bt.Sub_Category__c.toLowerCase();
                transactionPairs.add(normalizedKey);
            }
        }

        // Query relevant metadata
        Map<String, String> categoryMapping = new Map<String, String>();
        if(!transactionPairs.isEmpty()) {
            for(Flinks_Category__mdt fc : [
                SELECT Category__c, Sub_Category__c, MF_Category__r.Label 
                FROM Flinks_Category__mdt 
                WHERE Active__c = true
            ]) {
                if(String.isNotBlank(fc.Category__c) && String.isNotBlank(fc.Sub_Category__c)) {
                    String metadataKey = fc.Category__c.toLowerCase() + '|' + fc.Sub_Category__c.toLowerCase();
                    if(transactionPairs.contains(metadataKey)) {
                        categoryMapping.put(metadataKey, fc.MF_Category__r.Label);
                    }
                }
            }
        }

        // Assign MF Category
        for(Bank_Transaction__c bt : transactionsToProcess) {
            if(String.isNotBlank(bt.Category__c) && String.isNotBlank(bt.Sub_Category__c)) {
                String transactionKey = bt.Category__c.toLowerCase() + '|' + bt.Sub_Category__c.toLowerCase();
                bt.MF_Category__c = categoryMapping.get(transactionKey);
            } else {
                bt.MF_Category__c = null;
            }
        }
    }
}