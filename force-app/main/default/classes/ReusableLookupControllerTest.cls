@isTest
public class ReusableLookupControllerTest {
    
    static testMethod void testFetchRecordsWithParentRecord() {
        // Setup: Create parent and child records
        Account parentAccount = new Account(Name='Parent Account');
        insert parentAccount;
        Contact childContact = new Contact(LastName='Child', AccountId=parentAccount.Id);
        insert childContact;

        // Input wrapper setup
        ReusableLookupController.SearchWrapper wrapper = new ReusableLookupController.SearchWrapper();
        wrapper.objectApiName = 'Contact';
        wrapper.fieldApiName = 'LastName';
        wrapper.otherFieldApiName = 'AccountId';
        wrapper.parentFieldApiName = 'AccountId';
        wrapper.parentRecordId = parentAccount.Id;
        wrapper.searchString = 'Ch'; // Assuming we are looking for names starting with "Ch"

        // Test
        Test.startTest();
        List<ReusableLookupController.ResultWrapper> results = ReusableLookupController.fetchRecords(wrapper);
        Test.stopTest();

        // Verify
        System.assertEquals(1, results.size(), 'Should return exactly one contact');
        System.assertEquals('Child', results[0].mainField, 'The mainField should match the contact last name');
    }

}