public class CategorizationProcessor implements Queueable, Database.AllowsCallouts {
    public String loginId;
    public String requestId;
    //public Id bankAccountId;

    public CategorizationProcessor(String loginId, String requestId) {
        this.loginId = loginId;
        this.requestId = requestId;
        //this.bankAccountId = bankAccountId;
    }

    public void execute(QueueableContext context) {
        try {
            List<Bank_Transaction__c> transactions = [
                SELECT Id, Bank_Account__r.Request_Id__c, Transaction_Id__c, Category__c, Sub_Category__c
                FROM Bank_Transaction__c WHERE Bank_Account__r.Request_Id__c = :requestId
            ];
            /*List<Bank_Account__c> bankAccsAndTransactions = [
                SELECT Id, Name, Account_Id__c, Available_Balance__c, Currency__c, Contact__c, 
                Current_Balance__c, Login_Id__c, Request_Id__c, Type__c, Is_Get_Details_Running__c,
                (SELECT Id, Transaction_Id__c, Category__c, Sub_Category__c FROM BankTransactions__r)
                FROM Bank_Account__c Where Request_Id__c =: requestId
            ];*/

            if (transactions.isEmpty()) {
                System.debug('No transactions found for RequestId: ' + requestId);
                return;
            }

            //API Call
            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();
            HttpRequest req = new HttpRequest();
            req.setEndpoint(fc.Base_Url__c + fc.Customer_Id__c + '/categorization/login/' + loginId + '/requestid/' + requestId);
            req.setHeader('accept', 'application/json');
            req.setMethod('GET');

            Http http = new Http();
            HttpResponse res = http.send(req);
			
            UIT_Utility.LogFlinksCallout('',res.getBody(), loginId, 'GetCategorization', requestId, res.getStatusCode(),null,false);

            if (res.getStatusCode() == 200) {
                // Parse the response
                Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                List<Object> apiTransactions = (List<Object>) responseMap.get('Transactions');

                Map<String, Map<String, Object>> apiTransactionMap = new Map<String, Map<String, Object>>();
                for (Object apiTransaction : apiTransactions) {
                    Map<String, Object> apiTransactionData = (Map<String, Object>) apiTransaction;
                    String transactionId = (String) apiTransactionData.get('TransactionId');
                    apiTransactionMap.put(transactionId, apiTransactionData);
                }

                List<Bank_Transaction__c> transactionsToUpdate = new List<Bank_Transaction__c>();
                for (Bank_Transaction__c transac : transactions) {
                    if (apiTransactionMap.containsKey(transac.Transaction_Id__c)) {
                        Map<String, Object> apiData = apiTransactionMap.get(transac.Transaction_Id__c);
                        transac.Category__c = (String) apiData.get('Category');
                        transac.Sub_Category__c = (String) apiData.get('SubCategory');
                        transactionsToUpdate.add(transac);
                    }
                }

                if (!transactionsToUpdate.isEmpty()) {
                    update transactionsToUpdate;
                    System.debug('Updated transactions: ' + transactionsToUpdate);
                } else {
                    System.debug('No transactions to update.');
                }
            } else if (res.getStatusCode() == 401) {
                UIT_Utility.LogException(UserInfo.getUserId(), null, 'API Response Code: 401 - Unauthorized access in CategorizationProcessor');
            }
        } catch (Exception e) {
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'Exception error in catch block of CategorizationProcessor');
        }
    }
}

/*public with sharing class CategorizationProcessor implements Queueable, Database.AllowsCallouts {

    private String accountDetails;

    public CategorizationProcessor(String accountDetails) {
        this.accountDetails = accountDetails;
    }

    public void execute(QueueableContext context) {
        try {
            Map<String, Map<String, String>> categorizationData = getCategorizationData();
            if (categorizationData.isEmpty()) {
                System.debug('No categorization data found.');
                return;
            }

            List<Bank_Transaction__c> transactionRecords = [
                SELECT Id, Transaction_Id__c, Category__c, Sub_Category__c 
                FROM Bank_Transaction__c
            ];

            List<Bank_Transaction__c> transactionsToUpdate = new List<Bank_Transaction__c>();
            for (Bank_Transaction__c transRec : transactionRecords) {
                if (categorizationData.containsKey(transRec.Transaction_Id__c)) {
                    Map<String, String> categoryInfo = categorizationData.get(transRec.Transaction_Id__c);
                    transRec.Category__c = categoryInfo.get('category');
                    transRec.Sub_Category__c = categoryInfo.get('subcategory');
                    transactionsToUpdate.add(transRec);
                }
            }

            if (!transactionsToUpdate.isEmpty()) {
                upsert transactionsToUpdate;
            }

        } catch (Exception ex) {
            System.debug('Error in CategorizationProcessor: ' + ex.getMessage());
        }
    }
	
    private Map<String, Map<String, String>> getCategorizationData() {
        Map<String, Map<String, String>> categorizationData = new Map<String, Map<String, String>>();

        try {
            HttpRequest req = new HttpRequest();
            req.setHeader('Content-Type','application/json');
            req.setMethod('GET');
            //req.setEndpoint(fc.Base_Url__c+fc.Customer_Id__c+'/categorization/login/'+loginId+'/requestid/'+reqId);
            //req.setBody(accountDetails);

            Http http = new Http();
            HttpResponse res = http.send(req);

            if (res.getStatusCode() == 200) {
                System.debug('Categorization data fetched: ' + res.getBody());

                List<Map<String, Object>> responseData = 
                    (List<Map<String, Object>>) JSON.deserializeUntyped(res.getBody());

                for (Map<String, Object> record : responseData) {
                    String transactionId = (String) record.get('transactionId');
                    String category = (String) record.get('category');
                    String subcategory = (String) record.get('subcategory');

                    categorizationData.put(transactionId, new Map<String, String>{
                        'category' => category,
                        'subcategory' => subcategory
                    });
                }
            } else {
                System.debug('Failed to fetch categorization data: ' + res.getBody());
            }
        } catch (Exception ex) {
            System.debug('Error in getCategorizationData: ' + ex.getMessage());
        }

        return categorizationData;
    }
}*/