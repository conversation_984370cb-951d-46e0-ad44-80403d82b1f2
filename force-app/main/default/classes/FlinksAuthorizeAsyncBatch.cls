public class FlinksAuthorizeAsyncBatch implements Database.Batchable<sObject>,Database.Stateful, Database.AllowsCallouts{
    public Set<Id> retryBankAccIds;
    public String expiredAccs = ''; 

	public FlinksAuthorizeAsyncBatch(Set<Id> retryBankAccIds){
		this.retryBankAccIds = retryBankAccIds;
	}

	public Database.QueryLocator start(Database.BatchableContext info) { 
		Set<Id> bankAccIds = retryBankAccIds;
        retryBankAccIds = new Set<Id>();
        return Database.getQueryLocator([SELECT Id,Institution_Name__c,Name, Is_Active__c, Login_ID__c,Connection_Error_Code__c,Contact__r.Name,Contact__c,Request_Id__c FROM Bank_Account__c WHERE Id IN: bankAccIds]);
	}

    public void execute(Database.BatchableContext info, List<Bank_Account__c> records) { 
        try{
            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();
        
            for(Bank_Account__c bankAcc : records){
            	String accName = null;

				//callout
				HttpRequest req2 = new HttpRequest();
				req2.setEndpoint(fc.Base_Url__c+fc.Customer_Id__c+'/BankingServices/AuthorizeAsync');
				req2.setMethod('POST');
				req2.setHeader('Content-Type', 'application/json');
				req2.setBody('{"RequestId": "'+bankAcc.Request_Id__c+'"}');
				
				Http http2 = new Http();
				HTTPResponse res2 = http2.send(req2);
				system.debug('Retry body- '+res2.getBody());
				system.debug('Retry code- '+res2.getStatusCode());

				if(res2.getStatusCode() == 200){
					bankAcc.Authorize_Async_Request_Id__c = null;
					FlinksController.getAccountDetails(bankAcc.Request_Id__c);
				}else if(res2.getStatusCode() == 203){
					expiredAccs = expiredAccs != '' ? (expiredAccs + ', ') : expiredAccs;
					expiredAccs = expiredAccs + bankAcc.Name + '(' + bankAcc.Institution_Name__c + ') ';
					accName =  bankAcc.Name + '(' + bankAcc.Institution_Name__c + ') ';
					Map<String,Object> res2Map = (Map<String,Object>)JSON.deserializeUntyped(res2.getBody());
					bankAcc.Authorize_Async_Request_Id__c = (String)res2Map.get('RequestId');
				}else if(res2.getStatusCode() == 202){
					retryBankAccIds.add(bankAcc.Id);
				}else{
					accName =  bankAcc.Name + '(' + bankAcc.Institution_Name__c + ') ';
					expiredAccs = expiredAccs != '' ? (expiredAccs + ', ') : expiredAccs;
					expiredAccs = expiredAccs + bankAcc.Name + '(' + bankAcc.Institution_Name__c + ') ';
					bankAcc.Authorize_Async_Request_Id__c = null;
				}

				UIT_Utility.LogFlinksCallout('{"RequestId": "'+bankAcc.Request_Id__c+'"',res2.getBody(), bankAcc.Login_ID__c, 'Retry-AuthorizeAsync', bankAcc.Request_Id__c,res2.getStatusCode(),null,false);

                system.debug('accName-'+accName);

                if(accName != null){
                    List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
                
                    Messaging.SingleEmailMessage message = new Messaging.SingleEmailMessage();
                    message.setHtmlBody('Hi '+bankAcc.contact__r.name+ ',<br/>' +'Connection of your Account got interrupted - '+accName);
                    message.setSubject('Connection of Accounts got interrupted');
                    message.setTargetObjectId(bankAcc.contact__c);
                    emails.add(message);
        
                    Messaging.SendEmailResult [] r = Messaging.sendEmail(emails); 
                }
            }
            update records;
        } catch (Exception e) {
            UIT_Utility.LogException(UserInfo.getUserId(),e,'FlinksAuthorizeAsyncBatch.execute');
        }
    }

    public void finish(Database.BatchableContext BC){
        try{
            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();

            if(expiredAccs != ''){
                List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
                
                Messaging.SingleEmailMessage message2 = new Messaging.SingleEmailMessage();
                message2.setHtmlBody('Hi,<br/>' +'Connection of some Accounts got interrupted - '+expiredAccs);
                message2.setSubject('Connection of Accounts got interrupted');
                message2.setToAddresses(fc.email__c.split(','));

                emails.add(message2);

                Messaging.SendEmailResult [] r = Messaging.sendEmail(emails); 
            }

            if(!retryBankAccIds.isEmpty()){
                Database.executeBatch(new FlinksAuthorizeAsyncBatch(retryBankAccIds),1);
            }else{

                 fc.Is_Refresh_Running__c = false;
                 update fc;
            }
        } catch (Exception e) {
            UIT_Utility.LogException(UserInfo.getUserId(),e,'FlinksAuthorizeAsyncBatch.finish');
        }
    }


}