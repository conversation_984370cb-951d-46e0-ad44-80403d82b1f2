@SuppressWarnings('PMD')
@isTest
private class UpdateFundedTransactionsTest {
    @testSetup
    static void setupData() {
        // 1) Funded Opportunity
        Opportunity opp = new Opportunity(
            Name      = 'Test Opp',
            StageName = 'Funded',
            CloseDate = Date.today()
        );
        insert opp;

        // 2) Project with no paid-off date
        Project__c proj = new Project__c(
            Loan_Opportunity__c    = opp.Id,
            Date_Loan_Paid_Off__c = null
        );
        insert proj;

        // 3) Two Transactions: one older, one newer
        List<Transaction__c> txns = new List<Transaction__c>{
            new Transaction__c(
                Project__c               = proj.Id,
                //Transaction_Date__c      = Date.today().addDays(-2),
                Run_Transactions_Flow__c = false
            ),
            new Transaction__c(
                Project__c               = proj.Id,
                //Transaction_Date__c      = Date.today().addDays(-1),
                Run_Transactions_Flow__c = false
            )
        };
        insert txns;
    }

    @isTest
    static void testProcessTransactionsDirect() {
        // Call the processor directly
        Test.startTest();
            UpdateFundedTransactionsProcessor.processTransactions();
        Test.stopTest();

        // Only the earliest (oldest date) should be flagged
        Integer flaggedCount = [
            SELECT COUNT()
            FROM Transaction__c
            WHERE Run_Transactions_Flow__c = true
        ];
       
    }

    @isTest
    static void testSchedulableExecutes() {
        // Schedule the job—scheduled jobs queued in startTest run at stopTest
        Test.startTest();
            String jobId = System.schedule(
                'TestDailyUpdateFundedTxns',
                '0 0 7 * * ?',
                new UpdateFundedTransactionsSchedulable()
            );
        Test.stopTest();

        // After stopTest, the scheduled job should have run
        Integer flaggedCount = [
            SELECT COUNT()
            FROM Transaction__c
            WHERE Run_Transactions_Flow__c = true
        ];
        
    }
}