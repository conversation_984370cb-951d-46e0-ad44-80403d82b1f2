@isTest
private class GetClientNameAPITest {
    @testSetup
    static void setupTestData() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        Contact testContact = new Contact(FirstName = 'Test', LastName = 'User', AccountId = testAccount.Id);
        insert testContact;

        Profile userProfile = [SELECT Id FROM Profile WHERE Name = 'Customer Community User' LIMIT 1];

        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User12234',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'tuser',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = userProfile.Id,
            LanguageLocaleKey = 'en_US',
            ContactId = testContact.Id
        );
        insert testUser;
    }

    @isTest
    static void testGetAccountName() {
        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        
        // Run as test user
        System.runAs(testUser) {
            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();

            req.requestURI = '/services/apexrest/accountName';
            req.httpMethod = 'GET';
            RestContext.request = req;
            RestContext.response = res;

            // Call the API method
            GetClientNameAPI.getAccountName();

        }
    }

    @isTest
    static void testNoAccountForUser() {
        User noAccountUser = new User(
            FirstName = 'NoAccount',
            LastName = 'User',
            Email = '<EMAIL>',
            Username = '<EMAIL>',
            Alias = 'nuser',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
            LanguageLocaleKey = 'en_US'
        );
        insert noAccountUser;

        System.runAs(noAccountUser) {
            RestRequest req = new RestRequest();
            RestResponse res = new RestResponse();

            req.requestURI = '/services/apexrest/accountName';
            req.httpMethod = 'GET';
            RestContext.request = req;
            RestContext.response = res;

            // Call the API method
            GetClientNameAPI.getAccountName();
        }
    }
}