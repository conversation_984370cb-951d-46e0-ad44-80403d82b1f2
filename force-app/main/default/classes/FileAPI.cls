@RestResource(urlMapping='/files/*')
global without sharing class FileAPI {

    @HttpGet
    global static void getFiles() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;

        String recordId = req.requestURI.substring(req.requestURI.lastIndexOf('/') + 1);

        FileResponseWrapper fileWrapper = new FileResponseWrapper();
        
        try {
            if (String.isBlank(recordId)) {
                res.statusCode = 400;
                res.responseBody = Blob.valueOf('Record ID must be provided.');
                return;
            }

            List<ContentDocumentLink> linkedFiles = [
                SELECT ContentDocumentId, 
                       ContentDocument.Title, 
                       ContentDocument.FileType, 
                       ContentDocument.LastModifiedDate, 
                       ContentDocument.CreatedBy.Name
                FROM ContentDocumentLink 
                WHERE LinkedEntityId = :recordId
            ];

            fileWrapper.totalSize = linkedFiles.size();
            fileWrapper.done = true;
            fileWrapper.records = linkedFiles;
            
            res.statusCode = 200;
            res.responseBody = Blob.valueOf(JSON.serialize(fileWrapper));

        } catch (Exception ex) {
            res.statusCode = 500;
            res.responseBody = Blob.valueOf('Error retrieving files: ' + ex.getMessage());
        }
    }
   
    public class FileResponseWrapper {
        public Integer totalSize;
        public Boolean done;
        public List<ContentDocumentLink> records;

        public FileResponseWrapper() {
            this.records = new List<ContentDocumentLink>();
        }
    }
}