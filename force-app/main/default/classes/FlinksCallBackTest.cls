@isTest
public class FlinksCallBackTest {

    @isTest
    static void testDoGet() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/FlinksCallBack/';
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = res;

        Test.startTest();
        String result = FlinksCallBack.doGet();
        Test.stopTest();

        System.assertEquals('Get Result', result);
    }

    @isTest
    static void testDoPost() {
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Base_Url__c = 'https://api.test.com/',
            Customer_Id__c = '12345',
            Bearer_Token__c = 'testBearerToken'
        );
        insert config;

        Bank_Account__c bankAcc = new Bank_Account__c(
            Name = 'Test Account',
            Institution_Name__c = 'Test Bank',
            Login_ID__c = 'Login123',
            Is_Active__c = true,
            Request_Id__c = 'mockRequestId123'
        );
        insert bankAcc;

        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestURI = '/services/apexrest/FlinksCallBack/';
        req.httpMethod = 'POST';
        req.requestBody = Blob.valueOf('{"RequestId":"mockRequestId123","ResponseType":"TestResponse","HttpStatusCode":200,"Accounts":[{"Id":"AccountId123","AccountNumber":"123456"}]}');
        RestContext.request = req;
        RestContext.response = res;

        Test.setMock(HttpCalloutMock.class, new FlinksCallBackMock());

        Test.startTest();
        String result = FlinksCallBack.doPost();
        Test.stopTest();

        System.assertEquals('success', result);
    }

    private class FlinksCallBackMock implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setBody('{}');
            return res;
        }
    }
}