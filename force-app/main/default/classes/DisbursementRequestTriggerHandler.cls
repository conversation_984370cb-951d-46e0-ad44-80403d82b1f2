public class DisbursementRequestTriggerHandler {
    
    public static void createActivity(List<Disbursement_Request__c> drs){
        
        List<Disbursement_Request__c> drquests = new List<Disbursement_Request__c>();
        
        drquests = [SELECT Id,name,Project_lookup__r.Loan_Opportunity__r.AccountId FROM Disbursement_Request__c WHERE Id IN: drs];
        
        User usr = [SELECT Id,contactId,AccountId FROM User WHERE Id =: userinfo.getUserId()];        
        
        List<Activity_Logger__c> activities = new List<Activity_Logger__c>();
        
        for(Disbursement_Request__c dr : drquests){
            
            activities.add(new Activity_Logger__c(Item__c=dr.name,Related_Record__c = dr.Id,Account__c = dr.Project_lookup__r.Loan_Opportunity__r.AccountId,Activity_Time__c = system.now(),Activity_Type__c = 'Disbursement Request Submitted',Contact__c=usr.contactId,User__c=usr.Id));
            
        }
        
        if(!activities.isEmpty()){
            insert activities;
        }       
        
    }
}