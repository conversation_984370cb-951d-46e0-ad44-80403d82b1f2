@isTest
private class DropboxControllerTest {

    // Constant for Root Namespace ID (ensure it matches DropboxController)
    private static final String ROOT_NAMESPACE_ID = '11709235889';

    // Helper to create ContentVersion and ContentDocumentLink for tests
    // No changes needed here from previous version
    private static Id createContentVersionAndLink(String title, String extension, String content, Id parentId) {
        ContentVersion cv = new ContentVersion(
            Title = title,
            PathOnClient = title + '.' + extension,
            VersionData = Blob.valueOf(content),
            Origin = 'H' // Required for Salesforce Files (Chatter)
        );
        insert cv;

        // Get the ContentDocumentId from the inserted ContentVersion
        cv = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id LIMIT 1];

        // Link it to the parent record
        ContentDocumentLink cdl = new ContentDocumentLink(
            ContentDocumentId = cv.ContentDocumentId,
            LinkedEntityId = parentId,
            ShareType = 'V', // V = Viewer, I = Inferred, C = Collaborator
            Visibility = 'AllUsers'
        );
        insert cdl;

        return cv.Id; // Return ContentVersion Id
    }


     // === Test Setup ===
    @TestSetup
    static void makeData(){
        // Create a record to link files to (e.g., Account)
        Account acc = new Account(Name = 'Test Account for Files');
        insert acc;
    }


    // === Test Methods ===

    // --- fetchFiles Tests ---
    @isTest
    static void testFetchFilesSuccess() {
        // Corrected JSON String Syntax: No need to escape double quotes inside single quotes
        String mockResponseBody = '{"entries": [{"name": "test.txt", ".tag": "file"}], "cursor": "abc", "has_more": false}';
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(200, 'OK', mockResponseBody, null));
        DropboxController.isAllowNameSpaceId = false; // Test without namespace header

        Test.startTest();
        String result = DropboxController.fetchFiles('/some/path');
        Test.stopTest();

        System.assertEquals(mockResponseBody, result, 'Should return the mocked response body on success.');
    }

    @isTest
    static void testFetchFilesSuccessWithNamespace() {
        String mockResponseBody = '{"entries": [{"name": "test.txt", ".tag": "file"}], "cursor": "abc", "has_more": false}';
        // Create mock instance and set the flag
        MockHttpResponseGenerator mock = new MockHttpResponseGenerator(200, 'OK', mockResponseBody, null);
        mock.setExpectNamespaceHeader(true); // Signal mock to check header
        Test.setMock(HttpCalloutMock.class, mock);
        DropboxController.isAllowNameSpaceId = true; // Test WITH namespace header

        Test.startTest();
        String result = DropboxController.fetchFiles('/some/path');
        Test.stopTest();

        System.assertEquals(mockResponseBody, result, 'Should return the mocked response body on success with namespace.');
         // Assertion for header presence is inside the mock's respond method
    }

    @isTest
    static void testFetchFilesNotFound409() {
        // Corrected JSON String Syntax
        String mockErrorBody = '{"error_summary": "path/not_found/...", "error": {".tag": "path", "path": {".tag": "not_found", "path": "/nonexistent"}}}';
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(409, 'Conflict', mockErrorBody, null));
        DropboxController.isAllowNameSpaceId = false;

        Test.startTest();
        String result = DropboxController.fetchFiles('/nonexistent');
        Test.stopTest();

        // Corrected JSON String Syntax
        System.assertEquals('{"entries": []}', result, 'Should return empty entries JSON when path is not found (409).');
    }

    // --- getPreview Tests ---
    @isTest
    static void testGetPreviewSuccess() {
        // Corrected JSON String Syntax
        String mockPreviewBody = '{"link": "https://dl.dropboxusercontent.com/..."}'; // Assuming JSON response for simplicity
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(200, 'OK', mockPreviewBody, null));
         DropboxController.isAllowNameSpaceId = false;

        Test.startTest();
        String result = DropboxController.getPreview('/file/to/preview.pdf');
        Test.stopTest();

        System.assertEquals(mockPreviewBody, result, 'Should return the preview link body on success.');
    }

     @isTest
    static void testGetPreviewSuccessWithNamespace() {
        // Corrected JSON String Syntax
        String mockPreviewBody = '{"link": "https://dl.dropboxusercontent.com/..."}';
        MockHttpResponseGenerator mock = new MockHttpResponseGenerator(200, 'OK', mockPreviewBody, null);
        mock.setExpectNamespaceHeader(true); // Signal mock
        Test.setMock(HttpCalloutMock.class, mock);
        DropboxController.isAllowNameSpaceId = true;

        Test.startTest();
        String result = DropboxController.getPreview('/file/to/preview.pdf');
        Test.stopTest();

        System.assertEquals(mockPreviewBody, result, 'Should return the preview link body on success.');
        // Header check in mock
    }

    // --- uploadFileToDropbox Tests ---
    @isTest
    static void testUploadFileSuccess() {
        // Corrected JSON String Syntax
        String mockSuccessBody = '{"name": "uploaded.txt", "path_lower": "/uploaded.txt", "id": "id:abc", "size": 12}';
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(200, 'OK', mockSuccessBody, null));
        DropboxController.isAllowNameSpaceId = false;
        String base64Content = EncodingUtil.base64Encode(Blob.valueOf('Test content'));

        Test.startTest();
        String result = DropboxController.uploadFileToDropbox('/uploaded.txt', base64Content);
        Test.stopTest();

        System.assertEquals(mockSuccessBody, result, 'Should return success body after upload.');
    }

     @isTest
    static void testUploadFileSuccessWithNamespace() {
        // Corrected JSON String Syntax
        String mockSuccessBody = '{"name": "uploaded.txt", "path_lower": "/uploaded.txt", "id": "id:abc", "size": 12}';
        MockHttpResponseGenerator mock = new MockHttpResponseGenerator(200, 'OK', mockSuccessBody, null);
        mock.setExpectNamespaceHeader(true); // Signal mock
        Test.setMock(HttpCalloutMock.class, mock);
        DropboxController.isAllowNameSpaceId = true;
        String base64Content = EncodingUtil.base64Encode(Blob.valueOf('Test content'));

        Test.startTest();
        String result = DropboxController.uploadFileToDropbox('/uploaded.txt', base64Content);
        Test.stopTest();

        System.assertEquals(mockSuccessBody, result, 'Should return success body after upload.');
        // Header check in mock
    }

    // --- uploadFileToDropboxByUrl Tests ---
    @isTest
    static void testUploadFileByUrlSuccessAsync() {
        String asyncJobId = 'dbjid:12345';
        // Corrected JSON String Syntax
        String mockResponseBody = '{".tag": "async_job_id", "async_job_id": "' + asyncJobId + '"}';
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(200, 'OK', mockResponseBody, null));
        DropboxController.isAllowNameSpaceId = false;
        Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];

        Test.startTest();
        Map<String, Object> result = DropboxController.uploadFileToDropboxByUrl('/folder/', 'file.jpg', 'http://example.com/file.jpg', acc.Id);
        Test.stopTest();

        System.assertNotEquals(null, result.get('res'), 'Response object should be in the map.');
        System.assertNotEquals(null, result.get('req'), 'Request object should be in the map.');
        HttpResponse res = (HttpResponse)result.get('res'); // Cast for checking body
        System.assertEquals(asyncJobId, (String)((Map<String,Object>)JSON.deserializeUntyped(res.getBody())).get('async_job_id'), 'Async job ID should be extracted.');
    }

     @isTest
    static void testUploadFileByUrlSuccessAsyncWithNamespace() {
        String asyncJobId = 'dbjid:12345';
        // Corrected JSON String Syntax
        String mockResponseBody = '{".tag": "async_job_id", "async_job_id": "' + asyncJobId + '"}';
        MockHttpResponseGenerator mock = new MockHttpResponseGenerator(200, 'OK', mockResponseBody, null);
        mock.setExpectNamespaceHeader(true); // Signal mock
        Test.setMock(HttpCalloutMock.class, mock);
        DropboxController.isAllowNameSpaceId = true;
        Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];

        Test.startTest();
        Map<String, Object> result = DropboxController.uploadFileToDropboxByUrl('/folder/', 'file.jpg', 'http://example.com/file.jpg', acc.Id, null);
        Test.stopTest();

        System.assertNotEquals(null, result.get('res'), 'Response object should be in the map.');
        HttpResponse res = (HttpResponse)result.get('res');
        System.assertEquals(asyncJobId, (String)((Map<String,Object>)JSON.deserializeUntyped(res.getBody())).get('async_job_id'), 'Async job ID should be extracted.');
         // Header check in mock
    }

    @isTest
    static void testUploadFileByUrlApiError() {
        // Corrected JSON String Syntax
        String mockErrorBody = '{"error_summary": "download_failed/..."}';
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(400, 'Bad Request', mockErrorBody, null));
         DropboxController.isAllowNameSpaceId = false;
        Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];

        Test.startTest();
         Map<String, Object> result = DropboxController.uploadFileToDropboxByUrl('/folder/', 'file.jpg', 'http://example.com/invalid.jpg', acc.Id);
        Test.stopTest();

        System.assertNotEquals(null, result.get('res'), 'Response object should be in the map.');
        HttpResponse res = (HttpResponse)result.get('res');
        System.assertEquals(400, res.getStatusCode(), 'Status code should be 400.');
    }

    // --- checkDBJobStatus Tests ---
    @isTest
    static void testCheckJobStatusInProgress() {
        String jobId = 'dbjid:12345';
        // Corrected JSON String Syntax
        String mockResponseBody = '{".tag": "in_progress"}';
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(200, 'OK', mockResponseBody, null));
        DropboxController.isAllowNameSpaceId = false;
        Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];

        Test.startTest();
        Map<String, Object> result = DropboxController.checkDBJobStatus(jobId, acc.Id);
        Test.stopTest();

        System.assertNotEquals(null, result.get('res'), 'Response object should be in the map.');
        HttpResponse res = (HttpResponse)result.get('res');
        System.assertEquals(200, res.getStatusCode());
        System.assertEquals('in_progress', (String)((Map<String,Object>)JSON.deserializeUntyped(res.getBody())).get('.tag'));
    }

    @isTest
    static void testCheckJobStatusComplete() {
        String jobId = 'dbjid:67890';
        // Corrected JSON String Syntax
        String mockResponseBody = '{".tag": "complete", "name": "file.jpg", "id": "id:xyz", "size": 1024}';
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(200, 'OK', mockResponseBody, null));
         DropboxController.isAllowNameSpaceId = false;
         Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];

        Test.startTest();
        Map<String, Object> result = DropboxController.checkDBJobStatus(jobId, acc.Id);
        Test.stopTest();

        System.assertNotEquals(null, result.get('res'), 'Response object should be in the map.');
        HttpResponse res = (HttpResponse)result.get('res');
        System.assertEquals(200, res.getStatusCode());
        Map<String, Object> bodyMap = (Map<String, Object>)JSON.deserializeUntyped(res.getBody());
        System.assertEquals('complete', (String)bodyMap.get('.tag'));
        System.assertEquals('file.jpg', (String)bodyMap.get('name'));
    }

    @isTest
    static void testCheckJobStatusFailed() {
        String jobId = 'dbjid:11223';
        // Corrected JSON String Syntax
        String mockResponseBody = '{".tag": "failed", "error": { ".tag": "download_failed" }}'; // Example failure
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(200, 'OK', mockResponseBody, null)); // Status is still 200 OK!
         DropboxController.isAllowNameSpaceId = false;
         Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];

        Test.startTest();
        Map<String, Object> result = DropboxController.checkDBJobStatus(jobId, acc.Id);
        Test.stopTest();

        System.assertNotEquals(null, result.get('res'), 'Response object should be in the map.');
        HttpResponse res = (HttpResponse)result.get('res');
        System.assertEquals(200, res.getStatusCode());
        Map<String, Object> bodyMap = (Map<String, Object>)JSON.deserializeUntyped(res.getBody());
        System.assertEquals('failed', (String)bodyMap.get('.tag'));
    }

     @isTest
    static void testCheckJobStatusApiError() {
        String jobId = 'dbjid:invalid';
        // Corrected JSON String Syntax
        String mockErrorBody = '{"error_summary": "invalid_async_job_id/..."}';
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(409, 'Conflict', mockErrorBody, null));
        DropboxController.isAllowNameSpaceId = false;
        Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];

        Test.startTest();
        Map<String, Object> result = DropboxController.checkDBJobStatus(jobId, acc.Id);
        Test.stopTest();

        System.assertNotEquals(null, result.get('res'), 'Response object should be in the map.');
        HttpResponse res = (HttpResponse)result.get('res');
        System.assertEquals(409, res.getStatusCode());
    }

    // --- fetchFileContentAsBase64 Tests ---
    @isTest
    static void testFetchFileContentAsBase64Success() {
        Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];
        String fileContent = 'My Sample File Content';
        Id cvId = createContentVersionAndLink('TestFetch', 'txt', fileContent, acc.Id);

        Test.startTest();
        String base64Result = DropboxController.fetchFileContentAsBase64(cvId);
        Test.stopTest();

        System.assertEquals(EncodingUtil.base64Encode(Blob.valueOf(fileContent)), base64Result, 'Base64 encoded content should match.');
    }

    // --- retryUpload Tests ---
    @isTest
    static void testRetryUploadSuccess() {
         Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];
         Id cvId = createContentVersionAndLink('Retry Success', 'txt', 'Content to retry', acc.Id);
         String asyncJobId = 'dbjid:retry123';
        // Corrected JSON String Syntax
         String mockSaveUrlBody = '{".tag": "async_job_id", "async_job_id": "' + asyncJobId + '"}';
         Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(200, 'OK', mockSaveUrlBody, null));

        Test.startTest();
        // We need to ensure DropboxUtility and DropboxUploadBatch stubs exist or are mocked
        // For now, assume they execute and the callout mock is hit by dub.saveFileToDropbox
        try {
            DropboxController.retryUpload(new List<String>{ cvId });
        } catch (Exception e) {
            // Catch potential null pointers if utilities aren't properly stubbed/mocked
            System.debug('Exception during retryUpload setup/execution (check utility classes): ' + e);
            System.assert(false, 'retryUpload failed, check utility class setup/mocking. Error: ' + e.getMessage());
        }
        Test.stopTest();

        ContentVersion updatedCv = [SELECT Dropbox_Async_Job_Id__c, Dropbox_Sync_Status__c FROM ContentVersion WHERE Id = :cvId];
        System.assertEquals('Processing - Waiting Dropbox Confirmation', updatedCv.Dropbox_Sync_Status__c, 'Status should be Processing.');
         // Asserting JobId depends on exact return map structure from DropboxUploadBatch, may need adjustment
         // System.assertEquals(asyncJobId, updatedCv.Dropbox_Async_Job_Id__c, 'Async Job ID should be set.');
    }

     @isTest
    static void testRetryUploadFailure() {
         Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];
         Id cvId = createContentVersionAndLink('Retry Fail', 'txt', 'Content fail retry', acc.Id);
        // Corrected JSON String Syntax
         String mockErrorBody = '{"error_summary": "invalid_url/..."}';
         Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(400, 'Bad Request', mockErrorBody, null));

        Test.startTest();
         try{
            DropboxController.retryUpload(new List<String>{ cvId });
         } catch (Exception e) {
            System.debug('Exception during retryUpload setup/execution (check utility classes): ' + e);
            System.assert(false, 'retryUpload failed, check utility class setup/mocking. Error: ' + e.getMessage());
        }
        Test.stopTest();

        ContentVersion updatedCv = [SELECT Dropbox_Sync_Status__c FROM ContentVersion WHERE Id = :cvId];
        System.assertEquals('Failed', updatedCv.Dropbox_Sync_Status__c, 'Status should be Failed.');
    }

    // --- fetchFileShareLink Tests ---
     @isTest
    static void testFetchFileShareLinkSuccess() {
         // Define variable in accessible scope
         String shareUrl = 'https://www.dropbox.com/s/abc/file.txt?dl=0';
        // Corrected JSON String Syntax
         String mockResponseBody = '{"links": [{"url": "' + shareUrl + '", ".tag": "file"}], "has_more": false}';
         Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(200, 'OK', mockResponseBody, null));
         DropboxController.isAllowNameSpaceId = false;

        Test.startTest();
         Map<String, Object> result = DropboxController.fetchFileShareLink('/path/to/file.txt');
        Test.stopTest();

         System.assertEquals(shareUrl, result.get('url'), 'Should return the share URL.'); // Assertion uses variable
         System.assertEquals(null, result.get('error'), 'Error should be null on success.');
    }

    @isTest
    static void testFetchFileShareLinkNotFound() { // When the API returns 200 OK but empty links array
        // Corrected JSON String Syntax
         String mockResponseBody = '{"links": [], "has_more": false}';
         Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(200, 'OK', mockResponseBody, null));
         DropboxController.isAllowNameSpaceId = false;

        Test.startTest();
         Map<String, Object> result = DropboxController.fetchFileShareLink('/path/no/links.txt');
        Test.stopTest();

         System.assertEquals(null, result.get('url'), 'URL should be null when no links are found.');
         System.assert(String.valueOf(result.get('error')).contains('No links found'), 'Error message should indicate no links found.');
    }

     @isTest
    static void testFetchFileShareLinkApiError() {
        // Corrected JSON String Syntax
         String mockErrorBody = '{"error_summary": "path/not_found/..."}';
         Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(409, 'Conflict', mockErrorBody, null)); // 409 path not found for list_shared_links
         DropboxController.isAllowNameSpaceId = false;

        Test.startTest();
         Map<String, Object> result = DropboxController.fetchFileShareLink('/not/a/real/path.txt');
        Test.stopTest();

         System.assertEquals(null, result.get('url'), 'URL should be null on API error.');
         System.assert(String.valueOf(result.get('error')).contains('Failed to fetch links'), 'Error message should indicate failure.');
         System.assert(String.valueOf(result.get('error')).contains('409'), 'Error message should contain status code.');
    }

     @isTest
    static void testFetchFileShareLinkCalloutException() {
        // Use the top-level mock exception class
        Test.setMock(HttpCalloutMock.class, new MockHttpCalloutException());
        DropboxController.isAllowNameSpaceId = false;

        Test.startTest();
        Map<String, Object> result = DropboxController.fetchFileShareLink('/path/causes/exception.txt');
        Test.stopTest();

        System.assertEquals(null, result.get('url'), 'URL should be null on callout exception.');
        System.assert(String.valueOf(result.get('error')).contains('An exception occurred'), 'Error message should indicate an exception.');
        // Check for the specific message thrown by MockHttpCalloutException if needed
        // System.assert(String.valueOf(result.get('error')).contains('Mock callout exception'), 'Error message should contain mock exception text.');
    }

    // --- createFileShareLink Tests ---
     @isTest
    static void testCreateFileShareLinkSuccess() {
         Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];
         Id cvId = createContentVersionAndLink('Share Link', 'docx', 'Share this', acc.Id);
         ContentVersion cv = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cvId LIMIT 1];
         // Define variable in accessible scope
         String shareUrl = 'https://www.dropbox.com/scl/fi/abc/sharelink.docx?rlkey=key&dl=0';
        // Corrected JSON String Syntax
         String mockResponseBody = '{".tag": "file", "url": "' + shareUrl + '", "id": "id:link123", "path_lower": "/share_link.docx"}';
         Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(200, 'OK', mockResponseBody, null));
         DropboxController.isAllowNameSpaceId = false;

        Test.startTest();
        String resultUrl = DropboxController.createFileShareLink('/path/share_link.docx', cv.ContentDocumentId);
        Test.stopTest(); // Future method executes here

        System.assertEquals(shareUrl, resultUrl, 'Should return the created share URL.'); // Assertion uses variable

        ContentVersion updatedCv = [SELECT Dropbox_Create_Share_Link__c FROM ContentVersion WHERE Id = :cvId];
        System.assertEquals(shareUrl, updatedCv.Dropbox_Create_Share_Link__c, 'Future method should update the share link field.'); // Assertion uses variable
    }

     @isTest
    static void testCreateFileShareLinkAlreadyExists() {
         Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];
         Id cvId = createContentVersionAndLink('Share Link Exists', 'pdf', 'Already shared', acc.Id);
         ContentVersion cv = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cvId LIMIT 1];
        // Corrected JSON String Syntax
         String mockErrorBody = '{"error_summary": "shared_link_already_exists/...", "error": {".tag": "shared_link_already_exists"}}';
         Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator(409, 'Conflict', mockErrorBody, null));
         DropboxController.isAllowNameSpaceId = false;

        Test.startTest();
        String result = DropboxController.createFileShareLink('/path/exists.pdf', cv.ContentDocumentId);
        Test.stopTest();

        System.assertEquals('shared_link_exists', result, 'Should return specific string when link already exists.');

        ContentVersion notUpdatedCv = [SELECT Dropbox_Create_Share_Link__c FROM ContentVersion WHERE Id = :cvId];
        System.assertEquals(null, notUpdatedCv.Dropbox_Create_Share_Link__c, 'Share link field should remain null if link already existed.');
    }

    // --- getDropboxShareLink Tests ---
     @isTest
    static void testGetDropboxShareLinkSuccess() {
         String expectedLink = 'http://existing.link/123';
         Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];
         Id cvId = createContentVersionAndLink('Get Link', 'txt', 'Find my link', acc.Id);
         ContentVersion cv = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id = :cvId LIMIT 1];
         cv.Dropbox_Create_Share_Link__c = expectedLink;
         update cv;

        Test.startTest();
        String resultLink = DropboxController.getDropboxShareLink(cv.ContentDocumentId);
        Test.stopTest();

        System.assertEquals(expectedLink, resultLink, 'Should retrieve the link from the ContentVersion field.');
    }

    // --- updateContentVersion (@future) Tests ---
    @isTest static void testUpdateContentVersionFutureSuccess() {
        Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];
        Id cvId = createContentVersionAndLink('Future Update', 'txt', 'update me', acc.Id);
        ContentVersion cv = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cvId LIMIT 1];
        String futureUrl = 'http://future.link/update';

        Test.startTest();
        DropboxController.updateContentVersion(cv.ContentDocumentId, futureUrl);
        Test.stopTest(); // Ensures future method executes

        ContentVersion updatedCv = [SELECT Dropbox_Create_Share_Link__c FROM ContentVersion WHERE Id = :cvId];
        System.assertEquals(futureUrl, updatedCv.Dropbox_Create_Share_Link__c, 'Future method should update the field.');
    }

     @isTest static void testUpdateContentVersionFutureError() {
        String futureUrl = 'http://future.link/error';
        // Corrected: Use a non-ContentDocument Id
        Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Files' LIMIT 1];
        Id nonExistentCdId = acc.Id;

        Test.startTest();
        DropboxController.updateContentVersion(nonExistentCdId, futureUrl);
        Test.stopTest();

        List<ContentVersion> cvs = [SELECT Id FROM ContentVersion WHERE Dropbox_Create_Share_Link__c = :futureUrl];
        System.assertEquals(0, cvs.size(), 'No ContentVersion should have been updated with the URL.');
    }

}