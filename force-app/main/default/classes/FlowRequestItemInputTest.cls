@isTest
private class FlowRequestItemInputTest {

    @isTest
    static void testConstructorAndProperties() {
        // Arrange: Define test data
        String testName = 'Test Item';
        String testDescription = 'Test Description';
        Date testInvoiceDate = Date.today();
        Decimal testInvoiceAmount = 123.45;
        String testInvoice = 'INV-TEST-001';
        Date testInvoiceDueDate = Date.today().addDays(30);
        List<String> testDocIds = new List<String>{'069xxxxxxxxxxxxxxx', '069yyyyyyyyyyyyyyy'}; // Example ContentDocument IDs

        // Act: Instantiate the class
        Test.startTest();
        FlowRequestItemInput inputItem = new FlowRequestItemInput();

        // Set properties
        inputItem.Name = testName;
        inputItem.descriptionWork = testDescription;
        inputItem.invoiceDate = testInvoiceDate;
        inputItem.invoiceAmount = testInvoiceAmount;
        inputItem.invoice = testInvoice;
        inputItem.invoiceDueDate = testInvoiceDueDate;
        // Add items to the initialized list
        inputItem.contentDocumentIds.addAll(testDocIds);
        Test.stopTest();

        // Assert: Verify values and constructor initialization
        // Check if the list was initialized (not null)
        System.assertNotEquals(null, inputItem.contentDocumentIds, 'contentDocumentIds list should be initialized by the constructor.');
        // Check if the list contains the added items
        System.assertEquals(testDocIds.size(), inputItem.contentDocumentIds.size(), 'contentDocumentIds list should contain the added IDs.');
        // Check other properties
        System.assertEquals(testName, inputItem.Name, 'Name property should match the set value.');
        System.assertEquals(testDescription, inputItem.descriptionWork, 'descriptionWork property should match the set value.');
        System.assertEquals(testInvoiceDate, inputItem.invoiceDate, 'invoiceDate property should match the set value.');
        System.assertEquals(testInvoiceAmount, inputItem.invoiceAmount, 'invoiceAmount property should match the set value.');
        System.assertEquals(testInvoice, inputItem.invoice, 'invoice property should match the set value.');
        System.assertEquals(testInvoiceDueDate, inputItem.invoiceDueDate, 'invoiceDueDate property should match the set value.');
        System.assertEquals(testDocIds[0], inputItem.contentDocumentIds[0], 'First document ID should match.');
    }

    @isTest
    static void testConstructorInitializesList() {
         // Act: Instantiate the class
        Test.startTest();
        FlowRequestItemInput inputItem = new FlowRequestItemInput();
        Test.stopTest();

        // Assert: Verify the list is initialized and empty
        System.assertNotEquals(null, inputItem.contentDocumentIds, 'contentDocumentIds list should be initialized by the constructor.');
        System.assertEquals(0, inputItem.contentDocumentIds.size(), 'contentDocumentIds list should be empty upon initialization.');
    }
}