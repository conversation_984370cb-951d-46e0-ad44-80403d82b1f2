@SuppressWarnings('PMD')
@isTest(SeeAllData=false)
private class DebugLogUtilTest {

    @isTest
    static void testInfoAndLogOverloads() {
        Test.startTest();
            // Simple info
            DebugLogUtil.info('Simple info message');

            // Info with format args
            DebugLogUtil.info('Hello, {0}!', new List<Object>{ 'Salesforce' });

            // Info with tags only
            DebugLogUtil.info('Tagged info', null, new List<String>{ 'TagA','TagB' });

            // Info with both args and tags
            DebugLogUtil.info(
                'User {0} has {1} items', 
                new List<Object>{ 'Alice', 5 }, 
                new List<String>{ 'Metrics','UserOps' }
            );

            // log() aliases
            DebugLogUtil.log('Alias log message');
            DebugLogUtil.log('Alias log with tags', new List<String>{ 'AliasTag' });
            DebugLogUtil.log('Alias log with args', new List<Object>{ 'Arg1' });
        Test.stopTest();
    }

    @isTest
    static void testWarnOverloads() {
        Test.startTest();
            // Simple warn
            DebugLogUtil.warn('Simple warning');

            // Warn with args
            DebugLogUtil.warn('Threshold at {0}%', new List<Object>{ 85 });

            // Warn with tags
            DebugLogUtil.warn('Tagged warning', null, new List<String>{ 'Performance','Alert' });

            // Warn with args + tags
            DebugLogUtil.warn(
                'Disk {0} is {1}% full', 
                new List<Object>{ 'C:', 92 }, 
                new List<String>{ 'Storage','Urgent' }
            );
        Test.stopTest();
    }

    @isTest
    static void testErrorOverloads() {
        // Force and capture a real exception
        Exception capturedEx;
        try {
            Integer zero = 0;
            Integer fail = 1 / zero;  // this will throw a DivideByZero exception
        } catch (Exception e) {
            capturedEx = e;
        }
        System.assertNotEquals(null, capturedEx, 'Expected to capture an exception');

        Test.startTest();
            // Simple error message
            DebugLogUtil.error('Simple error');

            // Error from captured exception
            DebugLogUtil.error(capturedEx);

            // Exception + tags
            DebugLogUtil.error(capturedEx, new List<String>{ 'Critical' });

            // Message + exception
            DebugLogUtil.error('Caught exception', capturedEx);

            // Message + exception + tags
            DebugLogUtil.error('Failed to process record', capturedEx, null, new List<String>{ 'RecordBatch' });

            // Full overload: message + ex + args + tags
            DebugLogUtil.error(
                'User {0} failed at step {1}',
                capturedEx,
                new List<Object>{ 'Bob', 'Validation' },
                new List<String>{ 'UserFlow','ErrorFlow' }
            );
        Test.stopTest();
    }

    @isTest
    static void testEntryAndExitLogging() {
        Test.startTest();
            // Entry without params/tags
            DebugLogUtil.entry('myMethod');

            // Entry with params only
            Map<String,Object> params = new Map<String,Object>{
                'recordId' => '001xx000003NGsY',
                'count'    => 42
            };
            DebugLogUtil.entry('processRecords', params);

            // Entry with params + tags
            DebugLogUtil.entry('processRecords', params, new List<String>{ 'BatchJob' });

            // Exit without tags
            DebugLogUtil.exit('myMethod');

            // Exit with tags
            DebugLogUtil.exit('cleanup', new List<String>{ 'PostProcessing' });
        Test.stopTest();
    }
}