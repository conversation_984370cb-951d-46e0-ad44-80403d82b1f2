@SuppressWarnings('PMD')
@RestResource(urlMapping='/SignatureUpload/')
global without sharing class SignatureUploadAPI {

    global class SignatureUploadWrapper {
        public String Title;
        public String PathOnClient;
        public String VersionData;
        public String FirstPublishLocationId; //Opp/Dis. Req.
        public String FormName;
        public String FirstOwnerId;
        public String SecondOwnerId;
        //public String OppId;
        //public String DisbursementId;
    }

    global class SignatureUploadResponse {
        public String id;
        public String message;
        public Boolean success;
        public List<String> errors = new List<String>();
    }

    @HttpPost
    global static SignatureUploadResponse signatureFile() {
        SignatureUploadResponse response = new SignatureUploadResponse();

        try {
            // 1) Grab the raw body from the POST request
            RestRequest request = RestContext.request;
            String requestBody = request.requestBody.toString();
            Nebula.Logger.info('requestBody -> ' + requestBody).addTag('Signature Upload API');

            // 2) Deserialize that body into your new wrapper
            SignatureUploadWrapper reqWrapper =
                (SignatureUploadWrapper) JSON.deserialize(requestBody, SignatureUploadWrapper.class);

            // 3) Proceed using data from 'reqWrapper' instead of separate parameters
            Blob bodyBlob = Blob.valueOf(reqWrapper.VersionData);

            ContentVersion contentVersion = new ContentVersion(
                Title          = reqWrapper.Title,
                PathOnClient   = reqWrapper.PathOnClient,
                VersionData    = EncodingUtil.base64Decode(reqWrapper.VersionData),
                IsMajorVersion = true,
                ContentLocation = 'S'
            );

            insert contentVersion;

            contentVersion = [
                SELECT ContentDocumentId
                FROM ContentVersion
                WHERE Id = :contentVersion.Id
            ];
            
            ContentDocumentLink contentDocLink = new ContentDocumentLink(
                ContentDocumentId = contentVersion.ContentDocumentId,
                LinkedEntityId    = reqWrapper.FirstPublishLocationId,
                Visibility        = 'AllUsers'
            );
            insert contentDocLink;

            // Convert Base64 to Data URL format (for RTA field, if desired)
            String dataUrl = '<img src="data:image/png;base64,' + reqWrapper.VersionData + '" width="250px" height="150px"/>';
            Id recordId = Id.valueOf(reqWrapper.FirstPublishLocationId);
            String sObjectType = recordId.getSObjectType().getDescribe().getName();

            if (sObjectType == 'Disbursement_Request__c') {
                Disbursement_Request__c obj = [
                    SELECT Id, Signature_Picture__c
                    FROM Disbursement_Request__c
                    WHERE Id = :reqWrapper.FirstPublishLocationId
                    LIMIT 1
                ];
                obj.Signature_Picture__c = dataUrl;
                update obj;
            } else if (sObjectType == 'Opportunity') {
                Opportunity opp = [
                    SELECT Id, Signature_Picture__c
                    FROM Opportunity
                    WHERE Id = :reqWrapper.FirstPublishLocationId
                    LIMIT 1
                ];
                opp.Signature_Picture__c = dataUrl;
                update opp;
            }

            // Check if ContentDistribution exists for this version
            List<ContentDistribution> existingDistributions = [
                SELECT Id, ContentDownloadUrl
                FROM ContentDistribution
                WHERE ContentVersionId = :contentVersion.Id
                LIMIT 1
            ];

            ContentDistribution contentDist;
            if (existingDistributions.isEmpty()) {
                contentDist = new ContentDistribution(
                    Name = 'File Distribution',
                    ContentVersionId = contentVersion.Id,
                    PreferencesAllowOriginalDownload = true,
                    PreferencesAllowPDFDownload = true,
                    PreferencesLinkLatestVersion = true,
                    PreferencesAllowViewInBrowser = true
                );
                insert contentDist;

                // Fetch newly created ContentDistribution
                contentDist = [
                    SELECT Id, ContentDownloadUrl
                    FROM ContentDistribution
                    WHERE Id = :contentDist.Id
                    LIMIT 1
                ];
            } else {
                contentDist = existingDistributions[0];
            }
			
            Boolean formDetailResult;
            if(reqWrapper.FormName == 'Send Email Of New Projects Form'){
				List<sendEmailOfNewProjectFormDetails.inputVariables> inputList = new List<sendEmailOfNewProjectFormDetails.inputVariables>();
                
                sendEmailOfNewProjectFormDetails.inputVariables inputVar = new sendEmailOfNewProjectFormDetails.inputVariables();
                inputVar.contactId = reqWrapper.FirstOwnerId;
                inputVar.oppId = reqWrapper.FirstPublishLocationId;
                inputVar.contentDownloadUrl =  contentDist.ContentDownloadUrl;
                inputList.add(inputVar);
                List<sendEmailOfNewProjectFormDetails.outputVariables> outputList = sendEmailOfNewProjectFormDetails.sendEmailOfNewProjectsForm(inputList);
            	formDetailResult = outputList[0].result;
            }
            
            else if(reqWrapper.FormName == 'Send Email Of Disbursement Request Form'){
				List<sendEmailOfDisbursementReqForm.inputVariables> inputList = new List<sendEmailOfDisbursementReqForm.inputVariables>();
                
                sendEmailOfDisbursementReqForm.inputVariables inputVar = new sendEmailOfDisbursementReqForm.inputVariables();
                inputVar.requestedItemIds = [Select Id from Requested_Item__c Where Disbursement_Request__c =: reqWrapper.FirstPublishLocationId];
                inputVar.disbursementReqId = reqWrapper.FirstPublishLocationId;
                inputVar.contentDownloadUrl = contentDist.ContentDownloadUrl;
                inputList.add(inputVar);
                List<sendEmailOfDisbursementReqForm.outputVariables> outputList = sendEmailOfDisbursementReqForm.sendEmailOfDisbursementRequestForm(inputList);
            	formDetailResult = outputList[0].result;
            }
            
            else if(reqWrapper.FormName == 'Send Email Of Application Detail Form'){
				List<sendEmailOfApplicationFormDetails.inputVariables> inputList = new List<sendEmailOfApplicationFormDetails.inputVariables>();
                
                sendEmailOfApplicationFormDetails.inputVariables inputVar = new sendEmailOfApplicationFormDetails.inputVariables();
                List<Opportunity> opp = [Select Id, AccountId from Opportunity Where Id =: reqWrapper.FirstPublishLocationId Limit 1];
				inputVar.accountId = opp[0].AccountId;
                inputVar.contactOwnerId = reqWrapper.FirstOwnerId;
                inputVar.contactOwnerId2 = reqWrapper.SecondOwnerId;
                inputVar.oppId = reqWrapper.FirstPublishLocationId;
                inputVar.contentDownloadUrl = contentDist.ContentDownloadUrl;
                inputList.add(inputVar);
                List<sendEmailOfApplicationFormDetails.outputVariables> outputList = sendEmailOfApplicationFormDetails.sendEmailOfApplicationDetailForm(inputList);
            	formDetailResult = outputList[0].success;
            }

            // Set success response
            if(formDetailResult){
            	response.id = contentDocLink.Id;
                response.message = 'File uploaded successfully';
                response.success = true;
            }

        } catch (Exception e) {
            // Handle error
            Nebula.Logger.error('Error processing file  ' + e.getMessage() + '--' + e.getStackTraceString()).addTag('Submit New Loan Request API');
            Nebula.Logger.saveLog();
            response.success = false;
            response.errors.add(e.getMessage() + ' '+e.getStackTraceString());
        }
        
        return response;
    }
}