@isTest
private class DropboxUtilityTest {

    @testSetup
    static void setupTestData() {
        // Create a test Account
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        // Create a test Opportunity
        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity',
            AccountId = testAccount.Id,
            StageName = 'Prospecting',
            CloseDate = System.today()
        );
        insert testOpportunity;

        // Create a test Project
        Project__c testProject = new Project__c(Account_Name__c = testAccount.Id);
        insert testProject;

        // Create a test Disbursement Request
        Disbursement_Request__c testDisbursement = new Disbursement_Request__c(Project_lookup__c = testProject.Id);
        insert testDisbursement;

        // Ensure profile retrieval is safe
        Profile userProfile;
        try {
            userProfile = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        } catch (Exception e) {
            System.debug('No Standard User profile found, using an alternative profile.');
            userProfile = [SELECT Id FROM Profile LIMIT 1]; // Use any available profile
        }

        // Create a test User with the retrieved profile
        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Alias = 'tuser',
            Email = '<EMAIL>',
            Username = 'testuser' + System.currentTimeMillis() + '@example.com.salesforce',
            CommunityNickname = 'testuser' + System.currentTimeMillis(),
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = userProfile.Id,
            LanguageLocaleKey = 'en_US'
        );
        insert testUser;

        // Create a test ContentVersion
        ContentVersion testCV = new ContentVersion(
            Title = 'Test Document',
            PathOnClient = 'TestFile.pdf',
            VersionData = Blob.valueOf('Test Data'),
            FirstPublishLocationId = testOpportunity.Id
        );
        insert testCV;

        // Retrieve the related ContentDocument
        ContentDocument testCD = [SELECT Id FROM ContentDocument WHERE LatestPublishedVersionId = :testCV.Id LIMIT 1];

        // Check if a ContentDocumentLink already exists
        List<ContentDocumentLink> existingLinks = [
            SELECT Id FROM ContentDocumentLink
            WHERE ContentDocumentId = :testCD.Id AND LinkedEntityId = :testOpportunity.Id
            LIMIT 1
        ];

        // Insert only if it does not exist
        if (existingLinks.isEmpty()) {
            ContentDocumentLink testCDL = new ContentDocumentLink(
                LinkedEntityId = testOpportunity.Id,
                ContentDocumentId = testCD.Id,
                ShareType = 'V',
                Visibility = 'AllUsers'
            );
            insert testCDL;
        } else {
            System.debug('Skipping ContentDocumentLink insertion as it already exists.');
        }
    }

    @isTest
    static void testGetContentDocumentLinkByContentVersionId() {
        // Retrieve ContentVersion ID
        ContentVersion testCV = [SELECT Id FROM ContentVersion LIMIT 1];

        Test.startTest();
        ContentDocumentLink resultCDL = DropboxUtility.getContentDocumentLinkByContentVersionId(testCV.Id);
        Test.stopTest();

        // Assert that ContentDocumentLink is found
        System.assertNotEquals(null, resultCDL, 'ContentDocumentLink should be retrieved.');
        System.assertNotEquals(null, resultCDL.LinkedEntityId, 'LinkedEntityId should not be null.');
    }

    @isTest
    static void testGetFolderPath() {
        // Retrieve a valid ContentDocument first
        ContentDocument testCD = [SELECT Id FROM ContentDocument LIMIT 1];

        // Query a ContentDocumentLink using a proper WHERE clause
        ContentDocumentLink testCDL = [SELECT Id, LinkedEntityId, ContentDocumentId 
                                       FROM ContentDocumentLink 
                                       WHERE ContentDocumentId = :testCD.Id 
                                       LIMIT 1];

        Test.startTest();
        String folderPath = DropboxUtility.getFolderPath(testCDL);
        Test.stopTest();

        // Assert that the folder path is generated correctly
        System.assertNotEquals('', folderPath, 'Folder path should not be empty.');
        System.debug('Generated Folder Path: ' + folderPath);
    }
}