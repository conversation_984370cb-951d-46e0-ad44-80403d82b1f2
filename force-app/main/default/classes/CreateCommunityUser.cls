@SuppressWarnings('PMD')
public class CreateCommunityUser {

    @InvocableMethod(label='Create Community User')
    public static List<outputVariables> createCommunityUser(List<inputVariables> inputVariables) {
        List<outputVariables> outputVariablesList = new List<outputVariables>();

        for (inputVariables inputVar : inputVariables) {
            Map<String, Object> resultMap = createUser(inputVar.profileId, inputVar.firstName, inputVar.lastName, inputVar.email, inputVar.contactId, inputVar.isUserFromExistingAccount);

            outputVariables outputVar = new outputVariables();
            outputVar.isSuccess = (Boolean)resultMap.get('isSuccess') ? 'True' : 'False';
            outputVar.userId = (String)resultMap.get('userId');
            outputVar.message = (String)resultMap.get('message');

            outputVariablesList.add(outputVar);
        }

        System.debug(outputVariablesList);
        return outputVariablesList;
    }

    @future
    public static void sendEmailNotification(Id userId, String password, Id contactId, Boolean isUserFromExistingAccount) {
        User usr = [SELECT Id, Name, Username, Email FROM User WHERE Id = :userId];
        System.debug('usr ' + usr);

        Contact cont = [SELECT AccountId FROM Contact WHERE Id = :contactId LIMIT 1];
        Account acc = [SELECT Id FROM Account WHERE Id = :cont.AccountId LIMIT 1 ];
        
        // List<Opportunity> oppList = [SELECT Status_Update_For_Client__c FROM Opportunity WHERE AccountId = :acc.Id AND Status_Update_For_Client__c = 'Approved'];

        Organization orgInfo = [SELECT Id, Name, OrganizationType, IsSandbox FROM Organization LIMIT 1];
        String orgWideDisplayName;
        String ccAddresses;
        String bccAddresses;
        if (orgInfo.IsSandbox) {
            orgWideDisplayName = System.Label.Form_Details;
            ccAddresses = System.Label.CCRecipientsSandbox;
            bccAddresses = System.Label.BccRecipientsSandbox;
        } else {
            orgWideDisplayName = System.Label.Form_Details_Prod;
            ccAddresses = System.Label.CCRecipientsProd;
            bccAddresses = System.Label.BccRecipientsProd;
        }

        //List<OrgWideEmailAddress> owealist = [SELECT Id, Address, DisplayName FROM OrgWideEmailAddress WHERE DisplayName = :System.Label.Form_Details];
        List<OrgWideEmailAddress> owealist = [SELECT Id, Address, DisplayName FROM OrgWideEmailAddress WHERE Address = :orgWideDisplayName];
        System.debug('owealist ' + owealist);

        Network community = [SELECT Id, Name, UrlPathPrefix FROM Network WHERE Name = 'Mobilization Funding' LIMIT 1 ];
        String rediredCommunityUrl = Network.getLoginUrl(community.Id);
        System.debug('Network.getLoginUrl(community.Id) ' + Network.getLoginUrl(community.Id));
        String baseOrgUrl = URL.getOrgDomainUrl().toExternalForm();
        System.debug('baseOrgUrl: ' + baseOrgUrl);
        String communityURL = baseOrgUrl + '/' + community.UrlPathPrefix;
        System.debug('Community URL: ' + communityURL);

        Messaging.SingleEmailMessage emailMessage = new Messaging.SingleEmailMessage();
        emailMessage.setToAddresses(new String[]{usr.Email});
        if (owealist.size() > 0) {
            emailMessage.setOrgWideEmailAddressId(owealist[0].Id);
        }
        //emailMessage.setOrgWideEmailAddressId(orgWideEmailAddress);
        emailMessage.setSubject('Credential Details');
        emailMessage.setCcAddresses(ccAddresses.split(','));
        emailMessage.setBccAddresses(bccAddresses.split(','));

        String emailBodyContent;
		EmailTemplate emailTemplate;
        if (isUserFromExistingAccount) {
			emailTemplate = [SELECT Id, Name, Body, Subject, HtmlValue FROM EmailTemplate WHERE Name = 'Welcome New Community Email Updated2' LIMIT 1];
        }
        else {
			emailTemplate = [SELECT Id, Name, Body, Subject, HtmlValue FROM EmailTemplate WHERE Name = 'Welcome Existing Community Email Updated' LIMIT 1];
        }
        emailMessage.setSubject(emailTemplate.Subject);
        String emailBody = emailTemplate.Body;
        String htmlBody = emailTemplate.HtmlValue;
        List<String> bodies = new List<String>();
        String body = (htmlBody != null) ? htmlBody : emailBody;
        String subject = emailTemplate.Subject;
        body = body.replace('&lt;&lt;&lt;Name&gt;&gt;&gt;', usr.Name );
        body = body.replace('&lt;&lt;&lt;Username&gt;&gt;&gt;', usr.Username);
        body = body.replace('&lt;&lt;&lt;userPassword&gt;&gt;&gt;', password);
        body = body.replace('rediredCommunityUrl', rediredCommunityUrl);
        emailMessage.setHtmlBody(body);

        System.debug('emailMessage ' + emailMessage);
        try {
            Messaging.sendEmail(new List<Messaging.SingleEmailMessage> { emailMessage });
            System.debug('after email sent ' + emailMessage);
        } catch (Exception e) {
            System.debug('An exception occurred while sending portal User Credential: ' + e.getMessage() + ' --> ' + e.getStackTraceString());
        }
    }

    public static Map<String, Object> createUser(Id profileId, String firstName, String lastName, String email, Id contactId, Boolean isUserFromExistingAccount) {
        Map<String, Object> resultMap = new Map<String, Object>();
        resultMap.put('isSuccess', false);
        Id userPortalAccId = null;

        String communityNickName = (firstName + ' ' + lastName);

        // Attempt to create user without .mf suffix
        try {
            userPortalAccId = createCommunityUserInternal(profileId, firstName, lastName, email, contactId, communityNickName, isUserFromExistingAccount);
            resultMap.put('userId', userPortalAccId);
            resultMap.put('isSuccess', true);
            return resultMap;
        } catch (Exception e) {
            System.debug('An exception occurred while inserting portal account User without .mf: ' + e.getMessage() + ' --> ' + e.getStackTraceString());
        }

        // Attempt to create user with .mf suffix
        try {
            userPortalAccId = createCommunityUserInternal(profileId, firstName, lastName, email + '.mf', contactId, communityNickName, isUserFromExistingAccount);
            resultMap.put('userId', userPortalAccId);
            resultMap.put('isSuccess', true);
        } catch (Exception e) {
            resultMap.put('message', 'An exception occurred while inserting portal account User: ' + e.getMessage());
        }

        return resultMap;
    }

    private static String generateAlias(String firstName, String lastName) {
        if (String.isNotBlank(firstName) && firstName.length() > 1 && lastName.length() > 1) {
            return lastName.substring(0, 2) + firstName.substring(0, 2);
        } 
        else if (String.isNotBlank(firstName) && firstName.length() == 1 && lastName.length() > 1) {
            return lastName.substring(0, 2) + firstName.substring(0, 1);
        }
        else if (String.isNotBlank(firstName) && lastName.length() == 1 && firstName.length() > 1) {
            return lastName.substring(0, 1) + firstName.substring(0, 2);
        } 
        else if (String.isNotBlank(firstName) && lastName.length() == 1 && firstName.length() == 1) {
            return lastName.substring(0, 1) + firstName.substring(0, 1);
        } 
        else if (String.isBlank(firstName) && lastName.length() > 1) {
            return lastName.substring(0, 2);
        } 
        else {
        // (String.isBlank(firstName) && lastName.length() == 1) {
            return lastName.substring(0, 1);
        } 
    }
    private static Id createCommunityUserInternal(Id profileId, String firstName, String lastName, String email, Id contactId, String communityNickName, Boolean isUserFromExistingAccount) {
        String alias;
        try {
            alias = generateAlias(firstName, lastName);
        } catch (Exception e) {
            System.debug('Failed to generate alias: ' + e.getMessage());
        }

        User portalAccountOwner = new User(
            ProfileId = profileId,
            Username = email,
            Alias = alias,
            //lastName.substring(0, 2) + firstName.substring(0, 2),
            Email = email,
            EmailEncodingKey = 'UTF-8',
            Firstname = firstName,
            Lastname = lastName,
            CommunityNickname = communityNickName,
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            TimeZoneSidKey = 'America/Chicago',
            ContactId = contactId
        );
        if (Schema.sObjectType.User.isCreateable()) {
            Database.insert(portalAccountOwner);
        } else {
            System.debug('User does not have permission to create Chatter_Post_Tracker__c records.');
        }
        //Database.insert(portalAccountOwner);
        System.debug('userPortalAccId ' + portalAccountOwner.Id);

        List<Network> netWId = [SELECT Id, Name, Description, Status, EmailSenderName, EmailSenderAddress, NewSenderAddress FROM Network where Name =: System.Label.Network];
        List<NetworkMember> nmToUpdate = new List<NetworkMember>();
        NetworkMember nm = [SELECT Id, MemberId,NetworkId FROM NetworkMember WHERE NetworkId =: netWId[0].Id AND 
                                MemberId =: portalAccountOwner.Id];
        nm.PreferencesDisableAllFeedsEmail = true;
        nmToUpdate.add(nm);
        update nmToUpdate;
        
        /*PermissionSet permissionSets = [SELECT Id, Name, Label, Description FROM PermissionSet WHERE Name = 'Mobilization_Funding_Experience_Member'];
        PermissionSetAssignment psa = new PermissionSetAssignment();
        psa.AssigneeId = portalAccountOwner.Id;
        psa.PermissionSetId = permissionSets.Id;
        insert psa;*/
        assignPermissionSetAsync(portalAccountOwner.Id);

        Integer passwordLength = 10;
        Blob aesKey = Crypto.generateAesKey(128);
        String aesKeyString = EncodingUtil.convertToHex(aesKey);
        String randomPassword = aesKeyString.substring(0, passwordLength);
        System.debug('randomPassword ' + randomPassword);
        System.setPassword(portalAccountOwner.Id, randomPassword);

        sendEmailNotification(portalAccountOwner.Id, randomPassword, contactId, isUserFromExistingAccount);

        return portalAccountOwner.Id;
    }

    @future
    public static void assignPermissionSetAsync(Id userId) {
        try {
            PermissionSet permissionSet = [SELECT Id FROM PermissionSet WHERE Name = 'Mobilization_Funding_Experience_Member' LIMIT 1 ];
            PermissionSetAssignment psa = new PermissionSetAssignment();
            psa.AssigneeId = userId;
            psa.PermissionSetId = permissionSet.Id;
            insert psa;
        } catch (Exception e) {
            System.debug('Failed to assign permission set: ' + e.getMessage());
        }
    }

    public class inputVariables {
        @InvocableVariable 
        public Id contactId;
        @InvocableVariable 
        public String firstName;
        @InvocableVariable 
        public String lastName;
        @InvocableVariable 
        public String email;
        @InvocableVariable 
        public Id profileId;
        @InvocableVariable 
        public Boolean isUserFromExistingAccount;
    }

    public class outputVariables {
        @InvocableVariable 
        public String isSuccess;
        @InvocableVariable
        public String userId;
        @InvocableVariable
        public String message;
    }
}