@SuppressWarnings('PMD')
public with sharing class FlinkNightlyRefresh implements Schedulable, Database.AllowsCallouts{

    public static final Map<String,String> FlinksErrorCodeMap = new Map<String,String>{
        'INVALID_PASSWORD' => 'The password provided was different from what the bank expected',
        'INVALID_USERNAME' => 'The username provided was different from what the bank expected',
        'INVALID_SECURITY_RESPONSE' => 'The MFA response provided was different from what the bank expected',
        'UNAUTHORIZED' => 'You need to be authorized in the bank account before going further.'
    };

    public void execute(SchedulableContext sc){
        getIneligibleCards();
    }

    @future(callout=true)
    public static void getIneligibleCards(){
        try{
            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();
            
            HttpRequest req = new HttpRequest();
            req.setEndpoint(fc.Base_Url__c+fc.Customer_Id__c+'/BankingServices/GetNightlyRefreshStatus');
            req.setMethod('GET');
            req.setHeader('Authorization', 'Bearer '+fc.Bearer_Token__c);
            
            Http http = new Http();
            HTTPResponse res = http.send(req);
            system.debug('body- '+res.getBody());
            system.debug('code- '+res.getStatusCode());

            //create a log record for the nightly refresh API
            UIT_Utility.LogFlinksCallout(null,res.getBody(), null, 'GetNightlyRefreshStatus', null,res.getStatusCode(),null,false);

            Map<String,Object> mp_StrObj = (Map<String,Object>)JSON.deserializeUntyped(res.getBody());
            List<Object> lst_Obj = (List<Object>)mp_StrObj.get('IneligibleCards');

            Set<String> loginIds = new Set<String>();
            
            Map<String,String> loginErrorCode = new Map<String,String>();

            for(Object obj : lst_Obj){
                Map<String,Object> mp_Obj = (Map<String,Object>)obj;
                loginIds.add((String)mp_Obj.get('LoginId')); 
                loginErrorCode.put((String)mp_Obj.get('LoginId'),(String)mp_Obj.get('LastRefreshErrorCode'));
            }

            List<Bank_Account__c> lst_BkAccts = new List<Bank_Account__c>();

            fc.Is_Refresh_Running__c = true;
            fc.Last_Refresh_Run_Time__c = System.now();
            
            if(!loginIds.isEmpty()){
                lst_BkAccts = [SELECT Id, Is_Active__c, Login_ID__c,Connection_Error_Code__c,Connection_Error_Message__c FROM Bank_Account__c WHERE Login_ID__c IN: loginIds];
                system.debug('lst_BkAccts-'+lst_BkAccts);
                if(!lst_BkAccts.isEmpty()){
                    Set<Id> bankAccIds = new Set<Id>();

                    for(Bank_Account__c bkAcct : lst_BkAccts){
                        bankAccIds.add(bkAcct.Id);
                        bkAcct.Is_Active__c = false;
                        if(loginErrorCode.get(bkAcct.Login_ID__c) != null){
                            bkAcct.Connection_Error_Code__c = loginErrorCode.get(bkAcct.Login_ID__c);
                            bkAcct.Connection_Error_Message__c = FlinksErrorCodeMap.get(loginErrorCode.get(bkAcct.Login_ID__c));
                        }else{
                            bkAcct.Connection_Error_Code__c = null;
                            bkAcct.Connection_Error_Message__c = 'Connection of this account got revoked!';
                        }
                    }

                    update lst_BkAccts;

                    Datetime scheduleTime = Datetime.now();
                    scheduleTime = scheduleTime.addSeconds(2);
                    String hour = String.valueOf(scheduleTime.hour());
                    String min = String.valueOf(scheduleTime.minute()); 
                    String ss = String.valueOf(scheduleTime.second());
            
                    //parse to cron expression
                    String nextFireTime = ss + ' ' + min + ' ' + hour + ' * * ?';

                    System.schedule('FlinkNightlyRefreshBatch', nextFireTime, new FlinkNightlyRefreshBatch(bankAccIds));

                }else{
                    fc.Is_Refresh_Running__c = false;
                }
            }else{
                fc.Is_Refresh_Running__c = false;
            }

            update fc;

        } catch (Exception e) {
            UIT_Utility.LogException(UserInfo.getUserId(),e,'FlinkNightlyRefresh.getIneligibleCards');
        }

    }

}