public class FlinkNightlyRefresh<PERSON>atch implements Database.Batchable<sObject>,Database.Stateful, Schedulable, Database.AllowsCallouts{
    public String expiredAccs = ''; 
    public Set<Id> bankAccIds;
    public Set<Id> retryBankAccIds;

	public void execute(SchedulableContext sc){
        Database.executeBatch(new FlinkNightlyRefreshBatch(bankAccIds), 1);
        System.abortJob(sc.getTriggerId());
    }

    public Database.QueryLocator start(Database.BatchableContext info) { 
        retryBankAccIds = new Set<Id>();
        return Database.getQueryLocator([SELECT Id,Institution_Name__c,Name, Is_Active__c, Login_ID__c,Connection_Error_Code__c,Contact__r.Name,Contact__c,Request_Id__c FROM Bank_Account__c WHERE Id IN: bankAccIds]);
    }
        
    public FlinkNightlyRefreshBatch(Set<Id> accountIds) {
        bankAccIds = accountIds;
    }

    public void execute(Database.BatchableContext info, List<Bank_Account__c> records) { 
        try{
            String urlPrefix = mf123Opp.isSandbox() ? '/mf/s/' : '/s/';
            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();
            String siteLink='';
            //fetching survey site link
            Site sit = [SELECT Id, Name, Subdomain, UrlPathPrefix, Status,masterlabel FROM Site WHERE masterlabel = 'Mobilization Funding' AND Status = 'Active' AND UrlPathPrefix = 'mf/s' LIMIT 1];
            String siteDomain = [SELECT Id, Domain.Domain, SiteId, PathPrefix FROM DomainSite WHERE SiteId =: sit.Id LIMIT 1].Domain.Domain;
            //siteLink = 'https://'+siteLink+'/mf/s/linkedaccounts';
            siteLink = 'https://'+siteLink+urlPrefix+'linkedaccounts';

            for(Bank_Account__c bankAcc : records){

                HttpRequest req = new HttpRequest();
                req.setEndpoint(fc.Base_Url__c+fc.Customer_Id__c+'/BankingServices/AuthorizeAsync');
                req.setMethod('POST');
                req.setHeader('Content-Type', 'application/json');
                req.setBody('{"LoginId": "'+bankAcc.Login_ID__c+'","MostRecentCached":false,"Save":true}');
                
                Http http = new Http();
                HTTPResponse res = http.send(req);
                system.debug('body- '+res.getBody());
                system.debug('code- '+res.getStatusCode());

                String accName = null;
                if(res.getStatusCode() == 202){

                    Map<String,Object> resMap = (Map<String,Object>)JSON.deserializeUntyped(res.getBody());
                    bankAcc.Request_Id__c = (String)resMap.get('RequestId');
                	system.debug('reqId body- '+resMap.get('RequestId'));

                    //callout
                    HttpRequest req2 = new HttpRequest();
                    req2.setEndpoint(fc.Base_Url__c+fc.Customer_Id__c+'/BankingServices/AuthorizeAsync');
                    req2.setMethod('POST');
                    req2.setHeader('Content-Type', 'application/json');
                    req2.setBody('{"RequestId": "'+bankAcc.Request_Id__c+'"}');
                    
                    Http http2 = new Http();
                    HTTPResponse res2 = http2.send(req2);
                    system.debug('body2- '+res2.getBody());
                    system.debug('code2- '+res2.getStatusCode());

                    if(res2.getStatusCode() == 200){
                        bankAcc.Authorize_Async_Request_Id__c = null;
                        FlinksController.getAccountDetails(bankAcc.Request_Id__c);
                    }else if(res2.getStatusCode() == 203){
                        expiredAccs = expiredAccs != '' ? (expiredAccs + ', ') : expiredAccs;
                        expiredAccs = expiredAccs + bankAcc.Name + '(' + bankAcc.Institution_Name__c + ') ';
                        accName =  bankAcc.Name + '(' + bankAcc.Institution_Name__c + ') ';
                        Map<String,Object> res2Map = (Map<String,Object>)JSON.deserializeUntyped(res2.getBody());
                        bankAcc.Authorize_Async_Request_Id__c = (String)res2Map.get('RequestId');
                    }else if(res2.getStatusCode() == 202){
                        retryBankAccIds.add(bankAcc.Id);
                    }else{
                        accName =  bankAcc.Name + '(' + bankAcc.Institution_Name__c + ') ';
                        expiredAccs = expiredAccs != '' ? (expiredAccs + ', ') : expiredAccs;
                        expiredAccs = expiredAccs + bankAcc.Name + '(' + bankAcc.Institution_Name__c + ') ';
                        bankAcc.Authorize_Async_Request_Id__c = null;
                    }

                    UIT_Utility.LogFlinksCallout('{"RequestId": "'+bankAcc.Request_Id__c+'"',res2.getBody(), bankAcc.Login_ID__c, 'AuthorizeAsync', bankAcc.Request_Id__c,res2.getStatusCode(),null,false);

                }else{
                    expiredAccs = expiredAccs != '' ? (expiredAccs + ', ') : expiredAccs;
                    expiredAccs = expiredAccs + bankAcc.Name + '(' + bankAcc.Institution_Name__c + ') ';
                    accName =  bankAcc.Name + '(' + bankAcc.Institution_Name__c + ') ';
                }

                UIT_Utility.LogFlinksCallout('{"LoginId": "'+bankAcc.Login_ID__c+'","MostRecentCached":false,"Save":true}',res.getBody(), bankAcc.Login_ID__c, 'AuthorizeAsync', bankAcc.Request_Id__c,res.getStatusCode(),null,false);

                system.debug('accName-'+accName);

                if(accName != null){
                    List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
                
                    Messaging.SingleEmailMessage message = new Messaging.SingleEmailMessage();
                    message.setHtmlBody('Hi '+bankAcc.contact__r.name+ ',<br/>' +'Connection of your Account got interrupted - '+accName + '<br/><br/>Please login <a href="'+siteLink+'">here</a> to reauthenticate.');
                    message.setSubject('Connection of Accounts got interrupted');
                    message.setTargetObjectId(bankAcc.contact__c);
                    emails.add(message);
        
                    Messaging.SendEmailResult [] r = Messaging.sendEmail(emails); 
                }
            }
            update records;
        } catch (Exception e) {
            UIT_Utility.LogException(UserInfo.getUserId(),e,'FlinkNightlyRefreshBatch.execute');
        }
    }

    public void finish(Database.BatchableContext BC){
        try{
            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();

            if(expiredAccs != ''){
                List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
                
                Messaging.SingleEmailMessage message2 = new Messaging.SingleEmailMessage();
                message2.setHtmlBody('Hi,<br/>' +'Connection of some Accounts got interrupted - '+expiredAccs);
                message2.setSubject('Connection of Accounts got interrupted');
                message2.setToAddresses(fc.email__c.split(','));

                emails.add(message2);

                Messaging.SendEmailResult [] r = Messaging.sendEmail(emails); 
            }
            system.debug('retryBankAccIds-'+retryBankAccIds);
            if(!Test.isRunningTest() && !retryBankAccIds.isEmpty()){
                Database.executeBatch(new FlinksAuthorizeAsyncBatch(retryBankAccIds),1);
            }else{

                 fc.Is_Refresh_Running__c = false;
                 update fc;
            }
        } catch (Exception e) {
            UIT_Utility.LogException(UserInfo.getUserId(),e,'FlinkNightlyRefreshBatch.finish');
        }
    }

}