public class DropboxUtility {
 
    public static ContentDocumentLink getContentDocumentLinkByContentVersionId(Id cvId) {
        ContentDocument contentDocumentObj = [SELECT Id FROM ContentDocument WHERE LatestPublishedVersionId = :cvId LIMIT 1];
        
        List<ContentDocumentLink> cdls = [SELECT LinkedEntityId, ContentDocumentId, Id, ShareType, Visibility 
                                          FROM ContentDocumentLink 
                                          WHERE ContentDocumentId = :contentDocumentObj.Id];
        ContentDocumentLink cdObj;
    
        for(ContentDocumentLink cdObjTemp : cdls) {
            String sobj = cdObjTemp.LinkedEntityId.getSObjectType().getDescribe().getName();
    
            if(sobj != 'User') {
                cdObj = cdObjTemp;
                break;
            }
        }
        
        return cdObj;
        
    }
    

    public static string getFolderPath(ContentDocumentLink cdObj) {
          
        String folderPath = '';
        
        ContentDocument cdDocu = [SELECT Id, Title, CreatedById FROM ContentDocument WHERE Id = :cdObj.ContentDocumentId LIMIT 1];
        String fileName = cdDocu != null ? cdDocu.Title : 'Unknown File';
        String sObjName = cdObj.LinkedEntityId.getSObjectType().getDescribe().getName();
        String fileUrl = '';
        
        //User currentUser = [SELECT Id, Name, AccountId, Account.Name FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        User currentUser = [SELECT Id, Name, AccountId, Account.Name FROM User WHERE Id = :cdDocu.CreatedById LIMIT 1];
        String accName = ''; //currentUser.Account.Name;
        String userName = currentUser.AccountId != null ? 'Client' : 'User'; //currentUser.Name;
        String sObjNameForPath = '';
        String parentRecordName = '';
        
        switch on sObjName {
            when 'Opportunity' {
                sObjNameForPath = '/MF Team Folder/Salesforce Back-Ups/Applications/';
                Opportunity opp = [SELECT Id, Name, Account.Name FROM Opportunity WHERE Id = :cdObj.LinkedEntityId];
                parentRecordName = opp.Name;
                accName = opp.Account.Name;
            }
            when 'Disbursement_Request__c' {
                sObjNameForPath = '/MF Team Folder/Salesforce Back-Ups/Disbursements/';
                Disbursement_Request__c dr = [SELECT Id, Name, Project_lookup__r.Account_Name__r.Name FROM Disbursement_Request__c WHERE Id = :cdObj.LinkedEntityId];
                parentRecordName = dr.Name;
                accName = dr.Project_lookup__r.Account_Name__r.Name;
            }
            when 'Project__c' {
                sObjNameForPath = '/MF Team Folder/Salesforce Back-Ups/Projects/';
                Project__c proj = [SELECT Id, Name, Account_Name__r.Name FROM Project__c WHERE Id = :cdObj.LinkedEntityId];
                parentRecordName = proj.Name;
                accName = proj.Account_Name__r.Name;
            }
        }
        
        fileUrl = sObjNameForPath + userName + '.' + accName + '.' + parentRecordName + '.';

        return fileUrl;
    }    
    
}