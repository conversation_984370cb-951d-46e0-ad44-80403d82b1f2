@SuppressWarnings('PMD')
public class FeedCommentTriggerHandler {
    public static void handleAfterInsert(List<FeedComment> newFeedComments) {
        System.debug('Starting handleAfterInsert method...');
        
        if (newFeedComments == null || newFeedComments.isEmpty()) {
            System.debug('No new FeedComments to process.');
            return;
        }
        
        List<Activity_Logger__c> activities = new List<Activity_Logger__c>();
        Set<Id> relatedOpportunityIds = new Set<Id>();
        Set<Id> relatedProjectIds = new Set<Id>();
        Set<Id> relatedDisbursementRequestIds = new Set<Id>();
        Set<Id> userIds = new Set<Id>();
        
        for (FeedComment fc : newFeedComments) {
            userIds.add(fc.CreatedById);
        }
        
        Map<Id, User> userMap = new Map<Id, User>([SELECT Id, AccountId, ContactId FROM User WHERE Id IN :userIds]);
        User currentUser = [SELECT Id, contactId, AccountId FROM User WHERE Id = :UserInfo.getUserId()];
        
        Set<Id> communityUserIds = new Set<Id>();
        for (FeedComment fc : newFeedComments) {
            if (userMap.containsKey(fc.CreatedById) && userMap.get(fc.CreatedById).ContactId != null) {
                communityUserIds.add(fc.CreatedById);
                if (fc.ParentId != null && String.valueOf(fc.ParentId).startsWith('006')) {
                    relatedOpportunityIds.add(fc.ParentId);
                } else if (fc.ParentId != null && ((fc.ParentId).getSObjectType().getDescribe().getName()) == 'Project__c') {
                    relatedProjectIds.add(fc.ParentId);
                } else if (fc.ParentId != null && ((fc.ParentId).getSObjectType().getDescribe().getName()) == 'Disbursement_Request__c'){
                    relatedDisbursementRequestIds.add(fc.ParentId);
                }
            }
        }

        Map<Id, Opportunity> opportunityMap = relatedOpportunityIds.isEmpty() ? new Map<Id, Opportunity>() :
            new Map<Id, Opportunity>([SELECT Id, OwnerId, name, AccountId FROM Opportunity WHERE Id IN :relatedOpportunityIds]);
        Map<Id, Project__c> projectMap = relatedProjectIds.isEmpty() ? new Map<Id, Project__c>() :
            new Map<Id, Project__c>([SELECT Id, OwnerId, name, Loan_Opportunity__r.AccountId FROM Project__c WHERE Id IN :relatedProjectIds]);
        Map<Id, Disbursement_Request__c> disbursementRequestMap = relatedDisbursementRequestIds.isEmpty() ? new Map<Id, Disbursement_Request__c>() :
            new Map<Id, Disbursement_Request__c>([SELECT Id, OwnerId, name, Project_lookup__r.Loan_Opportunity__r.AccountId FROM Disbursement_Request__c WHERE Id IN :relatedDisbursementRequestIds]);
        
        List<Chatter_Post_Tracker__c> postTrackers = new List<Chatter_Post_Tracker__c>();
        List<Chatter_Post_Tracker__c> updatedTrackers = new List<Chatter_Post_Tracker__c>();
        
        String feedCmmt;
        for (FeedComment fc : newFeedComments) {
            feedCmmt = ChatterPostTrackerController.removeTags(fc.CommentBody);
            
            // Check for mentions
            Pattern pattern = Pattern.compile('@([A-Za-z]+(?: [A-Za-z]+)*)');
            Matcher matcher = pattern.matcher(feedCmmt);
            List<String> mentionUsers = new List<String>();
            while (matcher.find()) {
                mentionUsers.add(matcher.group(1));
            }
            
            Boolean hasExternalUser = false;
            if (!mentionUsers.isEmpty()) {
                List<User> mentionedUsersFetch = [SELECT Id, Name, AccountId FROM User WHERE Name IN :mentionUsers];
                for (User usr : mentionedUsersFetch) {
                    if (usr.AccountId != null) {
                        hasExternalUser = true;
                    }
                }
            }
            
            // Check if this is a response to an existing tracker
            List<Chatter_Post_Tracker__c> trackers = [
                SELECT Id, Status__c FROM Chatter_Post_Tracker__c
                WHERE (Related_Opportunity__c = :fc.ParentId OR
                       Related_Project__c = :fc.ParentId OR
                       Related_Disbursement_Request__c = :fc.ParentId)
                AND Status__c = 'New'
                ORDER BY CreatedDate DESC LIMIT 1
            ];
            
            Activity_Logger__c al = new Activity_Logger__c(
                Related_Record__c = fc.ParentId,
                Activity_Time__c = System.now(),
                Contact__c = currentUser.contactId,
                User__c = currentUser.Id,
                Comment_Body__c = feedCmmt
            );
            
            // Set the appropriate activity type and handle tracker updates
            if (!trackers.isEmpty()) {
                // This is a response to an existing tracker
                Chatter_Post_Tracker__c tracker = trackers[0];
                tracker.Status__c = 'Responded';
                tracker.Answered_By__c = fc.CreatedById;
                tracker.Answered_Time__c = System.now();
                updatedTrackers.add(tracker);
                al.Activity_Type__c = 'Reply Added';
            } else if (communityUserIds.contains(fc.CreatedById) || hasExternalUser) {
                // This is a new comment from community user or mentioning external user
                Chatter_Post_Tracker__c tracker = new Chatter_Post_Tracker__c(
                    Post_Link__c = '/' + fc.Id,
                    Status__c = 'New',
                    Client__c = fc.CreatedById,
                    Related_Account__c = currentUser.AccountId,
                    Comments__c = feedCmmt
                );
                
                if (fc.ParentId != null) {
                    if (String.valueOf(fc.ParentId).startsWith('006')) {
                        tracker.Related_Opportunity__c = fc.ParentId;
                    } else if (((fc.ParentId).getSObjectType().getDescribe().getName()) == 'Project__c') {
                        tracker.Related_Project__c = fc.ParentId;
                    } else if ((fc.ParentId).getSObjectType().getDescribe().getName() == 'Disbursement_Request__c') {
                        tracker.Related_Disbursement_Request__c = fc.ParentId;
                    }
                }
                postTrackers.add(tracker);
                al.Activity_Type__c = 'Comment Added';
            } else {
                // Default case for internal user comments that aren't responses
                al.Activity_Type__c = 'Reply Added';
            }
            
            // Set Account and Item fields for Activity Logger
            if (fc.ParentId != null) {
                if (String.valueOf(fc.ParentId).startsWith('006') && opportunityMap.containsKey(fc.ParentId)) {
                    al.Account__c = opportunityMap.get(fc.ParentId).AccountId;
                    al.Item__c = opportunityMap.get(fc.ParentId).Name;
                } else if (String.valueOf(fc.ParentId).startsWith('a09') && projectMap.containsKey(fc.ParentId)) {
                    al.Account__c = projectMap.get(fc.ParentId).Loan_Opportunity__r.AccountId;
                    al.Item__c = projectMap.get(fc.ParentId).Name;
                } else if (disbursementRequestMap.containsKey(fc.ParentId)) {
                    al.Account__c = disbursementRequestMap.get(fc.ParentId).Project_lookup__r.Loan_Opportunity__r.AccountId;
                    al.Item__c = disbursementRequestMap.get(fc.ParentId).Name;
                }
            }
            
            activities.add(al);
        }
        
        if (!activities.isEmpty()) {
            insert activities;
        }
        
        if (!postTrackers.isEmpty()) {
            insert postTrackers;
            ChatterPostTrackerController.createTrackerOwners(postTrackers);
            ChatterPostTrackerController.notifyAccountOwners(postTrackers);
        }
        
        if (!updatedTrackers.isEmpty()) {
            update updatedTrackers;
        }
        
        System.debug('Completed handleAfterInsert method.');
    }
    
    public static void preventUnauthorizedDelete(List<FeedComment> feedCmmts) {
        User currentUser = [SELECT Id, Profile.Name FROM User WHERE Id = :UserInfo.getUserId()];
        String currentProfileName = currentUser.Profile.Name;

        Set<String> communityProfileNames = new Set<String>{
            'Customer Community User',
            'Customer Community Login User'
        };

        for (FeedComment cmmt : feedCmmts) {
            Boolean isCommunityUser = communityProfileNames.contains(currentProfileName);
            Boolean isPostOwner = cmmt.CreatedById == UserInfo.getUserId();

            if (isCommunityUser && !isPostOwner) {
                cmmt.addError('Delete prevented: User does not have permission to delete this Internal post');
            }
        }
    }
}