@SuppressWarnings('PMD')  
@RestResource(urlMapping='/SubmitApplicationForm/')
global without sharing class SubmitApplicationService {

    @HttpPost
    global static ResponseWrapper updateAccountAndCreateOpportunity() {
        ResponseWrapper response = new ResponseWrapper();
        try {
            RestRequest req = RestContext.request;
            String jsonInput = req.requestBody.toString();
            Nebula.Logger.info('input -> ' + jsonInput).addTag('Submit Application Request API');
            
            SubmitApplicationWrapper application = (SubmitApplicationWrapper) JSON.deserialize(jsonInput, SubmitApplicationWrapper.class);
            
            //'005QL00000A7ZOWYA3
            User currentUser = [SELECT Id,name, ContactId FROM User WHERE Id =:UserInfo.getUserId() LIMIT 1];
            System.debug('currentUser '+currentUser);
            //:UserInfo.getUserId()
            
            String conId = currentUser.ContactId;
            if (currentUser.ContactId != null) {
                System.debug('currentUser.ContactId '+currentUser.ContactId);
                Contact currentContact = [SELECT Id, AccountId FROM Contact WHERE Id =: conId LIMIT 1];
                System.debug('currentContact '+currentContact);
                
                if (currentContact != null && currentContact.AccountId != null) {
                    Account existingAccount = [SELECT Id FROM Account WHERE Id = :currentContact.AccountId LIMIT 1];
                    System.debug('existingAccount '+existingAccount);
                    
                    if (existingAccount != null) {
                        updateAccount(existingAccount, application);
                        
                        Opportunity newOpportunity = createOpportunity(existingAccount.Id, application);
                        insert newOpportunity;
                        
                        updateContact(currentContact, application.OwnerInformation);
                        Id contact1Id = currentContact.Id;
                        
                        Id contact2Id;
                        if (application.OwnerInformation2 != null) {
                            contact2Id = handleContact2(existingAccount.Id, application.OwnerInformation2);
                        }

                        sendEmailOfApplicationFormDetails(existingAccount.Id, contact1Id, contact2Id, newOpportunity.Id);

                        response.status = 'success';
                        response.accountId = existingAccount.Id;
                        response.contact1Id = contact1Id;
                        response.contact2Id = contact2Id;
                        response.opportunityId = newOpportunity.Id;
                    } else {
                        response.status = 'No account found associated with the current user.';
                    }
                } else {
                    response.status = 'No contact found for the current user or contact is not associated with any account.';
                }
            } else {
                response.status = 'Running user is not a community user or does not have a contact record.';
            }
        } catch (Exception e) {
            System.debug('exception '+e.getMessage());
            System.debug('e.getStackTraceString() '+e.getStackTraceString());
            response.status = 'An error occurred: ' + e.getMessage();
            response.stackTrace = e.getStackTraceString();
            Nebula.Logger.error('Error --- ' + e.getMessage() + '--' + e.getStackTraceString()).addTag('Submit Application Request API');
            Nebula.Logger.saveLog();
        }
        return response;
    }

    private static void updateAccount(Account existingAccount, SubmitApplicationWrapper application) {
        existingAccount.BillingCity = application.BusinessInformation.BusinessAddress.CITY;
        existingAccount.BillingPostalCode = application.BusinessInformation.BusinessAddress.ZIP_CODE;
        existingAccount.BillingState = application.BusinessInformation.BusinessAddress.STATE;
        existingAccount.BillingStreet = application.BusinessInformation.BusinessAddress.STREET_ADDRESS;
        existingAccount.Description = application.BusinessInformation.Type_of_Work;
        existingAccount.EIN__c = application.BusinessInformation.Federal_Tax_ID_Number;
        //existingAccount.Email__c = application.OwnerInformation.Email;
        if(application.BusinessInformation.Number_of_Employees != null) {
            existingAccount.NumberOfEmployees = Integer.valueOf(application.BusinessInformation.Number_of_Employees);
        }
        existingAccount.Phone = application.BusinessInformation.businessPhone;
        existingAccount.Website = application.BusinessInformation.Website;
        existingAccount.Year_Founded__c = application.BusinessInformation.Year_Business_Was_Founded;
        if(application.BusinessInformation.Number_of_Owners_Above_10 != null) {
        	existingAccount.of_Owners__c = Integer.valueOf(application.BusinessInformation.Number_of_Owners_Above_10);
        }
        update existingAccount;
    }

    public static Opportunity createOpportunity(Id accountId, SubmitApplicationWrapper application) {
        Opportunity newOpportunity = new Opportunity();
        
        	newOpportunity.AccountId = accountId;
        	newOpportunity.App_Signature__c = application.VerifyAndSubmit.Signature;
        	newOpportunity.Name = application.BusinessInformation.Business_Name;
        	newOpportunity.Loan_Amount_Requested__c = application.BusinessInformation.Loan_Amount_Requested;
        	
        	newOpportunity.StageName = 'Application';
        	newOpportunity.CloseDate = Date.today().addDays(30);
        	newOpportunity.Bad_Debt__c = application.AdditionalInformation.Are_you_delinquent_or_in_default_of_any_debt_or_other_loans_including_Federal_or;
        	newOpportunity.Bankruptcy__c = application.AdditionalInformation.Have_you_or_any_of_the_majority_owners_ever_filed_for_bankruptcy;
        	newOpportunity.Confirmation_Email__c = application.VerifyAndSubmit.Email_for_Confirmation;
        	newOpportunity.Current_Lawsuits__c = application.AdditionalInformation.Are_you_or_any_of_the_majority_owners_party_to_any_current_lawsuits;
        	
        	//newOpportunity.Overhead_Debt_Schedule__c = application.DebtSchedule.OverheadAndDebtSchedule;
        	newOpportunity.Status_Update_for_Client__c = 'Application Review';
        	newOpportunity.UCC_Filings__c = application.AdditionalInformation.Are_there_any_UCC_Filings_against_the_company_or_any_of_its_majority_owners;
        	newOpportunity.of_active_contracts_POs__c = application.BusinessInformation.purchaseOrder;
        	newOpportunity.Signed_App__c = application.VerifyAndSubmit.Date1;
        
        return newOpportunity;
    }

    public static void updateContact(Contact currentContact, SubmitApplicationWrapper.OwnerInformation ownerInfo) {
        currentContact.FirstName = ownerInfo.Name.First;
        currentContact.LastName = ownerInfo.Name.Last;
        currentContact.Email = ownerInfo.Email;
        currentContact.Phone = ownerInfo.cellPhone;
        //currentContact.MailingStreet = ownerInfo.homeAddress;
        currentContact.MailingStreet = ownerInfo.HomeAddress.street;
        currentContact.MailingState = ownerInfo.HomeAddress.state;
        currentContact.MailingPostalCode = ownerInfo.HomeAddress.postalCode;
        currentContact.MailingCountry = ownerInfo.HomeAddress.country;
        currentContact.MailingCity = ownerInfo.HomeAddress.city;
        currentContact.Title = ownerInfo.Title;
        currentContact.Date_of_Birth__c = Date.valueOf(ownerInfo.Date_Of_Birth);
        currentContact.SSN__c = ownerInfo.Social_Security_Number != null ? ownerInfo.Social_Security_Number.replaceAll('-', '') : null;
        //currentContact.SSN__c = ownerInfo.Social_Security_Number;
        currentContact.Married__c = ownerInfo.Married;
        currentContact.Ownership__c = Decimal.valueOf(ownerInfo.Percent_Ownership);
        currentContact.Do_you_have_a_life_insurance_policy__c = ownerInfo.Do_you_have_a_life_insurance_policy;
        currentContact.Life_Insurance_Policy_Limit__c = ownerInfo.If_yes_what_is_the_policy_limit;
        currentContact.Web_Entry__c = True;

        update currentContact;
    }

    public static Id handleContact2(Id accountId, SubmitApplicationWrapper.OwnerInformation2 ownerInfo2) {
        List<Contact> contact2 = [SELECT Id FROM Contact WHERE Email =:ownerInfo2.Email2 AND AccountId =:accountId LIMIT 1];
        
        Contact conn2;
        if (!contact2.isEmpty() ) {
            conn2 = contact2[0];
            conn2 = updateContact2(conn2, ownerInfo2);
            update conn2;
            return conn2.Id;
        } else {
            Contact newContact2 = new Contact();
            newContact2 = updateContact2(newContact2, ownerInfo2);
            newContact2.AccountId = accountId;
			newContact2.MobilePhone = ownerInfo2.cellPhone2;
            newContact2.Email = ownerInfo2.Email2;
			insert newContact2; 
            return newContact2.Id;
        }
    }

    public static Contact updateContact2(Contact contact, SubmitApplicationWrapper.OwnerInformation2 ownerInfo2) {
        contact.FirstName = ownerInfo2.Name.First2;
        contact.LastName = ownerInfo2.Name.Last2;
        //contact.Email = ownerInfo2.Email2;
        //contact.MobilePhone = ownerInfo2.cellPhone2;
        //contact.MailingStreet = ownerInfo2.homeAddress2;
        
        contact.MailingStreet = ownerInfo2.HomeAddress2.street2;
        contact.MailingState = ownerInfo2.HomeAddress2.state2;
        contact.MailingPostalCode = ownerInfo2.HomeAddress2.postalCode2;
        contact.MailingCountry = ownerInfo2.HomeAddress2.country2;
        contact.MailingCity = ownerInfo2.HomeAddress2.city2;
        
        contact.Title = ownerInfo2.Title2;
        contact.Date_of_Birth__c = Date.valueOf(ownerInfo2.Date_Of_Birth2);
        contact.SSN__c = ownerInfo2.Social_Security_Number2 != null ? ownerInfo2.Social_Security_Number2.replaceAll('-', '') : null;
        //contact.SSN__c = ownerInfo2.Social_Security_Number2;
        contact.Married__c = ownerInfo2.Married2;
        contact.Ownership__c = Decimal.valueOf(ownerInfo2.Percent_Ownership2);
        contact.Do_you_have_a_life_insurance_policy__c = ownerInfo2.Do_you_have_a_life_insurance_policy2;
        contact.Life_Insurance_Policy_Limit__c = ownerInfo2.If_yes_what_is_the_policy_limit2;
        contact.Web_Entry__c = True;

        return contact;
    }

    public static void sendEmailOfApplicationFormDetails(String accId, String conId1, String conId2, String oppId){
        sendEmailOfApplicationFormDetails.inputVariables inputVar = new sendEmailOfApplicationFormDetails.inputVariables();
        inputVar.accountId = accId;
        inputVar.contactOwnerId = conId1;
        inputVar.contactOwnerId2 = conId2;
        inputVar.oppId = oppId;
        
        List<sendEmailOfApplicationFormDetails.inputVariables> inputList = new List<sendEmailOfApplicationFormDetails.inputVariables>();
        inputList.add(inputVar);

        List<sendEmailOfApplicationFormDetails.outputVariables> outputList = sendEmailOfApplicationFormDetails.sendEmailOfApplicationDetailForm(inputList);

        for (sendEmailOfApplicationFormDetails.outputVariables outputVar : outputList) {
            System.debug('success: ' + outputVar.success);
            System.debug('message: ' + outputVar.message);
        }
    }
    
    global class ResponseWrapper {
        public String status { get; set; }
        public Id accountId { get; set; }
        public Id contact1Id { get; set; }
        public Id contact2Id { get; set; }
        public Id opportunityId { get; set; }
        public String stackTrace { get; set; }
    }
}