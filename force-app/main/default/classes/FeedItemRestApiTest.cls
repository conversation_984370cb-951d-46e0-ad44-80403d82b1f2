@IsTest
public class FeedItemRestApiTest {

    @TestSetup
    static void setupData() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        
        FeedItem testFeedItem = new FeedItem();
        testFeedItem.ParentId = testAccount.Id;
        testFeedItem.Body = 'This is a test feed item';
        testFeedItem.Title = 'Test Title';
        testFeedItem.Type = 'TextPost';
        insert testFeedItem;

        FeedComment testComment = new FeedComment();
        testComment.FeedItemId = testFeedItem.Id;
        testComment.CommentBody = 'This is a test comment';
        insert testComment;
    }

    @IsTest
    static void testCreateFeedItem() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        RestContext.request = req;
        RestContext.response = res;

        Account testAccount = [SELECT Id FROM Account LIMIT 1];

        String result = FeedItemRestApi.createFeedItem(
            testAccount.Id, 
            'This is a test post', 
            'Test Title', 
            'TextPost' 
        );
    }
    
    @IsTest
    static void testCreateFeedItemError() {
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        RestContext.request = req;
        RestContext.response = res;

        Account testAccount = [SELECT Id FROM Account LIMIT 1];

        String result = FeedItemRestApi.createFeedItem(
            testAccount.Id, 
            '', 
            'Test Title', 
            'TextPost'
        );
    }

    @IsTest
    static void testGetFeedItemAndComments() {
        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/feedItem/' + [SELECT Id FROM FeedItem LIMIT 1].Id;
        RestResponse res = new RestResponse();
        RestContext.request = req;
        RestContext.response = res;

        List<FeedItem> result = FeedItemRestApi.getFeedItemAndComments();
    }
    
    @IsTest
    static void testGetFeedItemAndCommentsNullId() {
        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/feedItem/';
        RestResponse res = new RestResponse();
        RestContext.request = req;
        RestContext.response = res;

        List<FeedItem> result = FeedItemRestApi.getFeedItemAndComments();
    }
}