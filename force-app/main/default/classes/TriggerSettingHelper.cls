/**
 * @description Helper class to manage trigger execution based on Custom Metadata Type settings
 * <AUTHOR>
 */
@SuppressWarnings('PMD')  
public with sharing class TriggerSettingHelper {
    
    // Cache to store trigger settings to avoid repeated SOQL queries
    private static Map<String, Boolean> triggerSettingsCache = new Map<String, Boolean>();
    
    /**
     * @description Check if a trigger should execute based on Custom Metadata Type setting
     * @param triggerName The name of the trigger setting record (e.g., 'AccountTriggerBeforeInsert')
     * @return Boolean indicating whether the trigger should execute
     */
    public static Boolean isTriggerActive(String triggerName) {
        // Check cache first
        if (triggerSettingsCache.containsKey(triggerName)) {
            return triggerSettingsCache.get(triggerName);
        }
        
        // Query Custom Metadata Type if not in cache
        Boolean isActive = false;
        try {
            List<Trigger_Setting__mdt> settings = [
                SELECT Is_Active__c 
                FROM Trigger_Setting__mdt 
                WHERE DeveloperName = :triggerName 
                LIMIT 1
            ];
            
            if (!settings.isEmpty()) {
                isActive = settings[0].Is_Active__c;
            }
        } catch (Exception e) {
            // Log error and default to false for safety
            System.debug('Error querying trigger setting for ' + triggerName + ': ' + e.getMessage());
            isActive = false;
        }
        
        // Cache the result
        triggerSettingsCache.put(triggerName, isActive);
        
        return isActive;
    }
    
    /**
     * @description Clear the cache (useful for testing)
     */
    @TestVisible
    private static void clearCache() {
        triggerSettingsCache.clear();
    }
    
    /**
     * @description Get all active trigger settings
     * @return Map of trigger names to their active status
     */
    public static Map<String, Boolean> getAllTriggerSettings() {
        Map<String, Boolean> allSettings = new Map<String, Boolean>();
        
        try {
            List<Trigger_Setting__mdt> settings = [
                SELECT DeveloperName, Is_Active__c 
                FROM Trigger_Setting__mdt
            ];
            
            for (Trigger_Setting__mdt setting : settings) {
                allSettings.put(setting.DeveloperName, setting.Is_Active__c);
            }
        } catch (Exception e) {
            System.debug('Error querying all trigger settings: ' + e.getMessage());
        }
        
        return allSettings;
    }
}