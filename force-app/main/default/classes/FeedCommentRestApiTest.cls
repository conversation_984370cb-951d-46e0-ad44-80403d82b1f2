@isTest
public class FeedCommentRestApiTest {
    
    @isTest
    static void testCreateFeedComment_Success() {
        Account acc = new Account(Name = 'Test Account');
        insert acc;
        
        FeedItem feedItem = new FeedItem();
        feedItem.ParentId = acc.Id;
        feedItem.Body = 'Test feed item';
        insert feedItem;
        
        String feedItemId = feedItem.Id;
        String commentBody = 'This is a test comment';
        
        Test.startTest();
        String result = FeedCommentRestApi.createFeedComment(feedItemId, commentBody);
        Test.stopTest();
    }
    
    @isTest
    static void testCreateFeedComment_EmptyBody() {
        Account acc = new Account(Name = 'Test Account');
        insert acc;
        
        FeedItem feedItem = new FeedItem();
        feedItem.ParentId = acc.Id;
        feedItem.Body = 'Test feed item';
        insert feedItem;
        
        String feedItemId = feedItem.Id;
        String emptyCommentBody = ''; 
        
        Test.startTest();
        String result = FeedCommentRestApi.createFeedComment(feedItemId, emptyCommentBody);
        Test.stopTest();
    }
}