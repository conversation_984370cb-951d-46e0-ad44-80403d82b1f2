@isTest
private class RequestItemControllerTest {

    // Helper method to create ContentVersion and return ContentDocumentId
    private static Id createContentVersionAndGetDocumentId(String title) {
        ContentVersion cv = new ContentVersion(
            Title = title,
            PathOnClient = title + '.txt',
            VersionData = Blob.valueOf('Test Content for ' + title)
        );
        insert cv;
        // Query ContentDocumentId after insert
        cv = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id];
        return cv.ContentDocumentId;
    }

    // Test Setup to create a common Disbursement Request record
    @testSetup
    static void setupData() {
        Disbursement_Request__c dr = new Disbursement_Request__c(Loan_Number__c = 'TestDR-123');
        insert dr;
        System.debug('Setup Disbursement_Request__c Id: ' + dr.Id);

        // Pre-create a document to test deletion scenarios more easily
        createContentVersionAndGetDocumentId('PreExistingFile');
    }

    // Test successful creation of items with files
    @isTest
    static void testSaveItemsSuccessWithFiles() {
        Disbursement_Request__c dr = [SELECT Id FROM Disbursement_Request__c LIMIT 1];
        Id docId1 = createContentVersionAndGetDocumentId('File1');
        Id docId2 = createContentVersionAndGetDocumentId('File2');

        RequestItemController.RequestItemDetail item1 = new RequestItemController.RequestItemDetail();
        item1.Name = 'Success Item 1';
        item1.descriptionWork = 'Work Description 1';
        item1.invoiceDate = Date.today();
        item1.invoiceAmount = 150.75;
        item1.invoice = 'INV-101';
        item1.invoiceDueDate = Date.today().addDays(15);
        item1.contentDocumentIds = new List<String>{ docId1, docId2 };

        RequestItemController.RequestItemDetail item2 = new RequestItemController.RequestItemDetail();
        item2.Name = 'Success Item 2';
        item2.descriptionWork = 'Work Description 2';
        item2.invoiceDate = Date.today().addDays(-5);
        item2.invoiceAmount = 200.00;
        item2.invoice = 'INV-102';
        item2.invoiceDueDate = Date.today().addDays(25);
        // item2 has no files intentionally

        String jsonPayload = JSON.serialize(new List<RequestItemController.RequestItemDetail>{ item1, item2 });

        RequestItemController.SaveContext ctx = new RequestItemController.SaveContext();
        ctx.disbursementRequestId = dr.Id;
        ctx.requestItemsJSON = jsonPayload;

        Test.startTest();
        List<RequestItemController.SaveResultOutput> results = RequestItemController.saveRequestItemsAndLinkFiles(new List<RequestItemController.SaveContext>{ ctx });
        Test.stopTest();

        // No Assertions - Verification would normally happen here
         System.debug('testSaveItemsSuccessWithFiles results: ' + JSON.serializePretty(results));
    }

    // Test successful creation of items without any files linked
     @isTest
    static void testSaveItemsSuccessNoFiles() {
        Disbursement_Request__c dr = [SELECT Id FROM Disbursement_Request__c LIMIT 1];

        RequestItemController.RequestItemDetail item = new RequestItemController.RequestItemDetail();
        item.Name = 'No File Item';
        item.descriptionWork = 'Description for item without files';
        item.invoiceDate = Date.newInstance(2025, 5, 1);
        item.invoiceAmount = 50.00;
        item.invoice = 'INV-NF-01';
        item.invoiceDueDate = Date.newInstance(2025, 6, 1);
        item.contentDocumentIds = new List<String>(); // Empty list

        String jsonPayload = JSON.serialize(new List<RequestItemController.RequestItemDetail>{ item });

        RequestItemController.SaveContext ctx = new RequestItemController.SaveContext();
        ctx.disbursementRequestId = dr.Id;
        ctx.requestItemsJSON = jsonPayload;

        Test.startTest();
        List<RequestItemController.SaveResultOutput> results = RequestItemController.saveRequestItemsAndLinkFiles(new List<RequestItemController.SaveContext>{ ctx });
        Test.stopTest();

         System.debug('testSaveItemsSuccessNoFiles results: ' + JSON.serializePretty(results));
    }

    // Test handling of multiple contexts in one call
    @isTest
    static void testSaveItemsMultipleContexts() {
        // Create a second Disbursement Request
        Disbursement_Request__c dr1 = [SELECT Id FROM Disbursement_Request__c LIMIT 1];
        Disbursement_Request__c dr2 = new Disbursement_Request__c(Loan_Number__c = 'TestDR-456');
        insert dr2;

        Id docId = createContentVersionAndGetDocumentId('MultiContextFile');

        // Context 1 Detail
        RequestItemController.RequestItemDetail item1 = new RequestItemController.RequestItemDetail();
        item1.Name = 'Context 1 Item';
        item1.invoiceAmount = 10.0;
        item1.contentDocumentIds = new List<String>{ docId };
        String jsonPayload1 = JSON.serialize(new List<RequestItemController.RequestItemDetail>{ item1 });

        RequestItemController.SaveContext ctx1 = new RequestItemController.SaveContext();
        ctx1.disbursementRequestId = dr1.Id;
        ctx1.requestItemsJSON = jsonPayload1;

        // Context 2 Detail
        RequestItemController.RequestItemDetail item2 = new RequestItemController.RequestItemDetail();
        item2.Name = 'Context 2 Item';
        item2.invoiceAmount = 20.0;
        // No file for item 2
        String jsonPayload2 = JSON.serialize(new List<RequestItemController.RequestItemDetail>{ item2 });

        RequestItemController.SaveContext ctx2 = new RequestItemController.SaveContext();
        ctx2.disbursementRequestId = dr2.Id;
        ctx2.requestItemsJSON = jsonPayload2;

        List<RequestItemController.SaveContext> contexts = new List<RequestItemController.SaveContext>{ ctx1, ctx2 };

        Test.startTest();
        List<RequestItemController.SaveResultOutput> results = RequestItemController.saveRequestItemsAndLinkFiles(contexts);
        Test.stopTest();

        // No Assertions
         System.debug('testSaveItemsMultipleContexts results: ' + JSON.serializePretty(results));
    }


    // Test handling when the input JSON is valid but represents an empty list
    @isTest
    static void testSaveItemsEmptyItemListInJSON() {
        Disbursement_Request__c dr = [SELECT Id FROM Disbursement_Request__c LIMIT 1];
        String jsonPayload = '[]'; // Valid JSON, empty array

        RequestItemController.SaveContext ctx = new RequestItemController.SaveContext();
        ctx.disbursementRequestId = dr.Id;
        ctx.requestItemsJSON = jsonPayload;

        Test.startTest();
        List<RequestItemController.SaveResultOutput> results = RequestItemController.saveRequestItemsAndLinkFiles(new List<RequestItemController.SaveContext>{ ctx });
        Test.stopTest();

        // No Assertions - Covers the 'itemDetails != null && !itemDetails.isEmpty()' check evaluating to false
         System.debug('testSaveItemsEmptyItemListInJSON results: ' + JSON.serializePretty(results));
    }

    // Test handling of invalid JSON input
    @isTest
    static void testSaveItemsInvalidJSON() {
        Disbursement_Request__c dr = [SELECT Id FROM Disbursement_Request__c LIMIT 1];
        String invalidJsonPayload = 'This is definitely not JSON';

        RequestItemController.SaveContext ctx = new RequestItemController.SaveContext();
        ctx.disbursementRequestId = dr.Id;
        ctx.requestItemsJSON = invalidJsonPayload;

        Test.startTest();
        List<RequestItemController.SaveResultOutput> results = RequestItemController.saveRequestItemsAndLinkFiles(new List<RequestItemController.SaveContext>{ ctx });
        Test.stopTest();

        // No Assertions - Covers the JSON.deserialize catch block
         System.debug('testSaveItemsInvalidJSON results: ' + JSON.serializePretty(results));
    }

    // Test handling when required context data (ID or JSON) is missing
    @isTest
    static void testSaveItemsMissingContextData() {
        // Scenario 1: Missing disbursementRequestId
        RequestItemController.SaveContext ctx1 = new RequestItemController.SaveContext();
        ctx1.disbursementRequestId = null;
        ctx1.requestItemsJSON = '[]'; // Provide valid JSON to isolate the ID issue

        // Scenario 2: Missing requestItemsJSON
        Disbursement_Request__c dr = [SELECT Id FROM Disbursement_Request__c LIMIT 1];
        RequestItemController.SaveContext ctx2 = new RequestItemController.SaveContext();
        ctx2.disbursementRequestId = dr.Id;
        ctx2.requestItemsJSON = ''; // Blank JSON

        // Scenario 3: Null requestItemsJSON
         RequestItemController.SaveContext ctx3 = new RequestItemController.SaveContext();
        ctx3.disbursementRequestId = dr.Id;
        ctx3.requestItemsJSON = null;

        List<RequestItemController.SaveContext> contexts = new List<RequestItemController.SaveContext>{ ctx1, ctx2, ctx3 };

        Test.startTest();
        List<RequestItemController.SaveResultOutput> results = RequestItemController.saveRequestItemsAndLinkFiles(contexts);
        Test.stopTest();

        // No Assertions - Covers the initial null/blank checks
        System.debug('testSaveItemsMissingContextData results: ' + JSON.serializePretty(results));
    }

    // Test handling when the list of contexts itself is null or empty
    @isTest
    static void testSaveItemsNullOrEmptyContextList() {
        Test.startTest();
        // Call with null list
        List<RequestItemController.SaveResultOutput> resultsNull = RequestItemController.saveRequestItemsAndLinkFiles(null);

        // Call with empty list
        List<RequestItemController.SaveResultOutput> resultsEmpty = RequestItemController.saveRequestItemsAndLinkFiles(new List<RequestItemController.SaveContext>());
        Test.stopTest();

        // No Assertions - Covers the initial check for empty/null contexts list
         System.debug('testSaveItemsNullOrEmptyContextList results (null): ' + JSON.serializePretty(resultsNull));
         System.debug('testSaveItemsNullOrEmptyContextList results (empty): ' + JSON.serializePretty(resultsEmpty));
    }

    // Test successful deletion of an uploaded file
    @isTest
    static void testDeleteFileSuccess() {
        Id docIdToDelete = createContentVersionAndGetDocumentId('FileToDeleteSuccess');

        Test.startTest();
        RequestItemController.deleteUploadedFile(docIdToDelete);
        Test.stopTest();

        // No Assertions - Verification (e.g., checking if the ContentDocument is gone) would normally happen here
        // Integer count = [SELECT COUNT() FROM ContentDocument WHERE Id = :docIdToDelete];
        // System.debug('Remaining ContentDocument count after successful delete: ' + count);
    }

    // Test attempting to delete a file that doesn't exist
    @isTest
    static void testDeleteFileNotFound() {
         // Create a file and get its ID
        Id docIdToCreateThenDelete = createContentVersionAndGetDocumentId('FileToDeleteThenAttemptDelete');

        // Manually delete the ContentDocument before calling the method under test
        ContentDocument cd = [SELECT Id FROM ContentDocument WHERE Id = :docIdToCreateThenDelete];
        delete cd;

        Test.startTest();
        // This call should execute without error, but log a warning internally
        RequestItemController.deleteUploadedFile(docIdToCreateThenDelete);
        Test.stopTest();

        // No Assertions - Covers the scenario where the query finds no document
         System.debug('testDeleteFileNotFound completed execution.');
    }

    // Test attempting to delete a file with a blank Id
    @isTest
    static void testDeleteFileBlankId() {
        Test.startTest();
        try {
            RequestItemController.deleteUploadedFile(''); // Test with blank string
        } catch (AuraHandledException e) {
            // Exception is expected, catch it to allow test execution to continue
            System.debug('Caught expected AuraHandledException for blank ID: ' + e.getMessage());
        }
        try {
            RequestItemController.deleteUploadedFile(null); // Test with null
        } catch (AuraHandledException e) {
            // Exception is expected
            System.debug('Caught expected AuraHandledException for null ID: ' + e.getMessage());
        }
        Test.stopTest();

        // No Assertions - Covers the initial blank check and the AuraHandledException throw/catch
    }

     // Test the constructor of the inner RequestItemDetail class
     @isTest static void testRequestItemDetailConstructor() {
         Test.startTest();
         RequestItemController.RequestItemDetail detail = new RequestItemController.RequestItemDetail();
         Test.stopTest();
         // No assertions, just ensuring the constructor runs and initializes the list
         System.debug('Initialized RequestItemDetail: ' + detail);
         System.debug('Initialized contentDocumentIds list size: ' + detail.contentDocumentIds.size());
     }

     // Test the constructor of the inner SaveResultOutput class
     @isTest static void testSaveResultOutputConstructor() {
          Test.startTest();
         RequestItemController.SaveResultOutput output = new RequestItemController.SaveResultOutput();
          Test.stopTest();
         // No assertions, just ensuring the constructor runs and initializes fields
         System.debug('Initialized SaveResultOutput: ' + output);
         System.debug('Initialized isSuccess: ' + output.isSuccess);
         System.debug('Initialized createdItems list size: ' + output.createdItems.size());
         System.debug('Initialized errorMessage: ' + output.errorMessage);
     }

     // Test the constructor of the private PairRequestedItemAndStringList class
      @isTest static void testPairClassConstructor() {
         Requested_Item__c item = new Requested_Item__c(Name = 'Test');
         // No need to insert the item for constructor test
         List<String> ids = new List<String>{'id1', 'id2'};

         Test.startTest();
         // As PairRequestedItemAndStringList is private, we can't directly instantiate it here.
         // Its constructor is implicitly tested when used within saveRequestItemsAndLinkFiles.
         // We can add a dummy call to ensure the main method runs if needed for coverage of the line
         // where the Pair is instantiated inside the loop.
         // Let's rely on testSaveItemsSuccessWithFiles covering this instantiation.
         Test.stopTest();
         System.debug('Pair class constructor is tested implicitly by save method tests.');
     }
}