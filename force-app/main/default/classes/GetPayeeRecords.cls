@SuppressWarnings('PMD')
/**
* @File Name : GetPayeeRecords.cls
* @Description :
* <AUTHOR>
* @Last Modified By :
* @Last Modified On : May 6, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | May 6, 2025 |   | Initial Version
**/

public without sharing class GetPayeeRecords {
  
  @AuraEnabled(cacheable=true)
  public static List<Payee_Information__c> getPayeeOptions() {

    User currentUser = [SELECT id, AccountId FROM USER WHERE Id = :UserInfo.getUserId()];
    
    return [
      SELECT Id, Payee_Name__c
      FROM Payee_Information__c
      WHERE Project__r.Loan_Opportunity__r.AccountId = :currentUser.AccountId
      ORDER BY Name
    ];
  }

  @AuraEnabled(cacheable=false)
  public static Payee_Information__c getPayeeRecord(Id id) {
    return [
      SELECT
        Payee_Name__c,
        Payee_First_Name__c,
        Payee_Last_Name__c,
        Payee_Contact_Email__c,
        Payment_Method__c,
        Payee_Phone__c,
        City__c,
        Country__c,
        State_Province__c,
        Street__c,
        Zip_Postal_Code__c,
        Mail_Check_To__c,
        Account_Name__c,
        Bank_Account_Number__c,
        Bank_Name__c,
        Bank_Routing_Number__c
      FROM Payee_Information__c
      WHERE Id = :id
      LIMIT 1
    ];
  }

}