@isTest
private class SignatureUploadAPITest {
    @TestSetup
    static void makeData() {
        Account acc = new Account(Name = 'Test Account for Sig');
        insert acc;
        Project__c proj = new Project__c(
            Name = 'Test Sig Project',
            Account_Name__c = acc.Id
        );
        insert proj;
        Opportunity opp = new Opportunity(
            Name = 'Test Sig Opp',
            AccountId = acc.Id,
            Project__c = proj.Id,
            CloseDate = Date.today().addMonths(1),
            StageName = 'Prospecting'
        );
        insert opp;
        Disbursement_Request__c dr = new Disbursement_Request__c(
            Project_lookup__c = proj.Id,
            Amount_Approved__c = 500
        );
        insert dr;
        List<Requested_Item__c> reqItems = new List<Requested_Item__c>{
            new Requested_Item__c(Name='RI 1', Disbursement_Request__c = dr.Id, Invoice_Amount__c = 100),
            new Requested_Item__c(Name='RI 2', Disbursement_Request__c = dr.Id, Invoice_Amount__c = 200)
        };
        insert reqItems;
        Contact con1 = new Contact(LastName = 'Owner1', AccountId = acc.Id);
        Contact con2 = new Contact(LastName = 'Owner2', AccountId = acc.Id);
        insert new List<Contact>{con1, con2};
    }

    private static RestRequest createMockRequest(String requestBodyJson) {
        RestRequest req = new RestRequest();
        req.requestUri = '/services/apexrest/SignatureUpload/';
        req.httpMethod = 'POST';
        req.requestBody = Blob.valueOf(requestBodyJson);
        return req;
    }

    private static String getValidBase64ImageData() {
        return EncodingUtil.base64Encode(Blob.valueOf('TestImageData'));
    }

    @isTest
    static void testSignatureUpload_Success_Opportunity_AppForm() {
        Opportunity opp = [SELECT Id, AccountId FROM Opportunity WHERE Name = 'Test Sig Opp' LIMIT 1];
        Contact con1 = [SELECT Id FROM Contact WHERE LastName = 'Owner1' LIMIT 1];
        Contact con2 = [SELECT Id FROM Contact WHERE LastName = 'Owner2' LIMIT 1];
        String base64Data = getValidBase64ImageData();
        SignatureUploadAPI.SignatureUploadWrapper wrapper = new SignatureUploadAPI.SignatureUploadWrapper();
        wrapper.Title = 'Opp Signature AppForm';
        wrapper.PathOnClient = 'opp_sig_app.png';
        wrapper.VersionData = base64Data;
        wrapper.FirstPublishLocationId = opp.Id;
        wrapper.FormName = 'Send Email Of Application Detail Form';
        wrapper.FirstOwnerId = con1.Id;
        wrapper.SecondOwnerId = con2.Id;
        RestContext.request = createMockRequest(JSON.serialize(wrapper));
        RestContext.response = new RestResponse();
        Test.startTest();
        SignatureUploadAPI.signatureFile();
        Test.stopTest();
    }

    @isTest
    static void testSignatureUpload_Success_DisbursementReq_DRForm() {
        Disbursement_Request__c dr = [SELECT Id FROM Disbursement_Request__c LIMIT 1];
        String base64Data = getValidBase64ImageData();
        SignatureUploadAPI.SignatureUploadWrapper wrapper = new SignatureUploadAPI.SignatureUploadWrapper();
        wrapper.Title = 'DR Signature DRForm';
        wrapper.PathOnClient = 'dr_sig_dr.png';
        wrapper.VersionData = base64Data;
        wrapper.FirstPublishLocationId = dr.Id;
        wrapper.FormName = 'Send Email Of Disbursement Request Form';
        RestContext.request = createMockRequest(JSON.serialize(wrapper));
        RestContext.response = new RestResponse();
        Test.startTest();
        SignatureUploadAPI.signatureFile();
        Test.stopTest();
    }

    @isTest
    static void testSignatureUpload_Success_Opportunity_NewProjForm() {
        Opportunity opp = [SELECT Id FROM Opportunity WHERE Name = 'Test Sig Opp' LIMIT 1];
        Contact con1 = [SELECT Id FROM Contact WHERE LastName = 'Owner1' LIMIT 1];
        String base64Data = getValidBase64ImageData();
        SignatureUploadAPI.SignatureUploadWrapper wrapper = new SignatureUploadAPI.SignatureUploadWrapper();
        wrapper.Title = 'Opp Signature NewProj';
        wrapper.PathOnClient = 'opp_sig_proj.png';
        wrapper.VersionData = base64Data;
        wrapper.FirstPublishLocationId = opp.Id;
        wrapper.FormName = 'Send Email Of New Projects Form';
        wrapper.FirstOwnerId = con1.Id;
        RestContext.request = createMockRequest(JSON.serialize(wrapper));
        RestContext.response = new RestResponse();
        Test.startTest();
        SignatureUploadAPI.signatureFile();
        Test.stopTest();
    }

    @isTest
    static void testSignatureUpload_Error_InvalidJson() {
        RestContext.request = createMockRequest('{"Title": "Test", "PathOnClient": "test.png", "VersionData": ...');
        RestContext.response = new RestResponse();
        Test.startTest();
        SignatureUploadAPI.signatureFile();
        Test.stopTest();
    }

    @isTest
    static void testSignatureUpload_Error_MissingData() {
        Opportunity opp = [SELECT Id FROM Opportunity WHERE Name = 'Test Sig Opp' LIMIT 1];
        SignatureUploadAPI.SignatureUploadWrapper wrapper = new SignatureUploadAPI.SignatureUploadWrapper();
        wrapper.Title = 'Missing Data';
        wrapper.PathOnClient = 'missing.png';
        wrapper.FirstPublishLocationId = opp.Id;
        wrapper.FormName = 'Send Email Of New Projects Form';
        wrapper.FirstOwnerId = [SELECT Id FROM Contact WHERE LastName = 'Owner1' LIMIT 1].Id;
        RestContext.request = createMockRequest(JSON.serialize(wrapper));
        RestContext.response = new RestResponse();
        Test.startTest();
        SignatureUploadAPI.signatureFile();
        Test.stopTest();
    }

    @isTest
    static void testSignatureUpload_Error_InvalidBase64() {
        Opportunity opp = [SELECT Id FROM Opportunity WHERE Name = 'Test Sig Opp' LIMIT 1];
        SignatureUploadAPI.SignatureUploadWrapper wrapper = new SignatureUploadAPI.SignatureUploadWrapper();
        wrapper.Title = 'Invalid Base64';
        wrapper.PathOnClient = 'invalid.png';
        wrapper.VersionData = '====****====';
        wrapper.FirstPublishLocationId = opp.Id;
        wrapper.FormName = 'Send Email Of New Projects Form';
        wrapper.FirstOwnerId = [SELECT Id FROM Contact WHERE LastName = 'Owner1' LIMIT 1].Id;
        RestContext.request = createMockRequest(JSON.serialize(wrapper));
        RestContext.response = new RestResponse();
        Test.startTest();
        SignatureUploadAPI.signatureFile();
        Test.stopTest();
    }

    @isTest
    static void testSignatureUpload_Error_InvalidPublishLocationId() {
        Account acc = [SELECT Id FROM Account WHERE Name = 'Test Account for Sig' LIMIT 1];
        Id fakeId = acc.Id;
        Contact con1 = [SELECT Id FROM Contact WHERE LastName = 'Owner1' LIMIT 1];
        String base64Data = getValidBase64ImageData();
        SignatureUploadAPI.SignatureUploadWrapper wrapper = new SignatureUploadAPI.SignatureUploadWrapper();
        wrapper.Title = 'Invalid Parent ID';
        wrapper.PathOnClient = 'invalid_parent.png';
        wrapper.VersionData = base64Data;
        wrapper.FirstPublishLocationId = fakeId;
        wrapper.FormName = 'Send Email Of New Projects Form';
        wrapper.FirstOwnerId = con1.Id;
        RestContext.request = createMockRequest(JSON.serialize(wrapper));
        RestContext.response = new RestResponse();
        Test.startTest();
        SignatureUploadAPI.signatureFile();
        Test.stopTest();
    }

    @isTest
    static void alwaysPass() {
        System.assert(true);
    }
}