@RestResource(urlMapping='/SubmitNewLoanApplicationForm/')
global without sharing class SubmitNewLoanApplicationService {

    @HttpPost
    global static ResponseWrapper processNewApplication() {
        ResponseWrapper response = new ResponseWrapper();
        try {
            RestRequest req = RestContext.request;
            String jsonInput = req.requestBody.toString();
            Nebula.Logger.info('input -> ' + jsonInput).addTag('Submit New Loan Request API');
            SubmitNewLoanRequestWrapper application = (SubmitNewLoanRequestWrapper) JSON.deserialize(jsonInput, SubmitNewLoanRequestWrapper.class);
            
            User currentUser = [SELECT Id, ContactId FROM User WHERE Id =: UserInfo.getUserId() LIMIT 1];
            
            if (currentUser.ContactId != null) {
                Contact currentContact = [SELECT Id, AccountId FROM Contact WHERE Id = :currentUser.ContactId LIMIT 1];
                
                if (currentContact != null && currentContact.AccountId != null) {
                    Account existingAccount = [SELECT Id FROM Account WHERE Id = :currentContact.AccountId LIMIT 1];
                    
                    if (existingAccount != null) {
                        //updateAccount(existingAccount, application);
                        Opportunity newOpportunity = createOpportunity(existingAccount.Id, application);
                        insert newOpportunity;
                        
                        sendEmailOfNewProjectFormDetails(currentContact.Id, newOpportunity.Id);

                        response.status = 'success';
                        response.accountId = existingAccount.Id;
                        response.opportunityId = newOpportunity.Id;
                    } else {
                        response.status = 'No account found associated with the current user.';
                    }
                } else {
                    response.status = 'No contact found for the current user or contact is not associated with any account.';
                }
            } else {
                response.status = 'Running user is not a community user or does not have a contact record.';
            }
        } catch (Exception e) {
            response.status = 'An error occurred: ' + e.getMessage();
            response.stackTrace = e.getStackTraceString();
            Nebula.Logger.error('Error - ' + e.getMessage() + '--' + e.getStackTraceString()).addTag('Submit New Loan Request API');
            Nebula.Logger.saveLog();
        }
        return response;
    }

    /*public static void updateAccount(Account existingAccount, SubmitNewLoanRequestWrapper application) {
        existingAccount.Name = application.BusinessInformation.Business_Name;
        existingAccount.Description = application.BusinessInformation.Project_Name;
        update existingAccount;
    }*/

    public static Opportunity createOpportunity(Id accountId, SubmitNewLoanRequestWrapper application) {
        Opportunity newOpportunity = new Opportunity();
        newOpportunity.AccountId = accountId;
        newOpportunity.App_Signature__c = application.VerifyAndSubmit.Signature;
        newOpportunity.Name = application.BusinessInformation.Project_Name;
        newOpportunity.StageName = 'Application';
        newOpportunity.Signed_App__c = application.VerifyAndSubmit.Date1;
        newOpportunity.CloseDate = Date.today().addDays(60);
        newOpportunity.UCC_Filings__c = application.AdditionalInformation.Are_there_any_new_UCC_Filings_against_the_company_or_any_of_its_majority_owners;
        newOpportunity.Bad_Debt__c = application.AdditionalInformation.Are_you_delinquent_or_in_default_of_any_debt_or_other_loans_including_Federal_or;
        newOpportunity.Bankruptcy__c = application.AdditionalInformation.Are_there_any_new_bankruptcy_fillings_by_you_or_your_majority_owners;
        newOpportunity.Current_Lawsuits__c = application.AdditionalInformation.Are_you_or_any_of_the_majority_owners_party_to_any_current_lawsuits_not_previous;
        newOpportunity.Confirmation_Email__c = application.VerifyAndSubmit.Email_for_Confirmation;
        newOpportunity.Status_Update_for_Client__c = 'Application Review';
        return newOpportunity;
    }
    
    public static void sendEmailOfNewProjectFormDetails(String conId1, String oppId){
        sendEmailOfNewProjectFormDetails.inputVariables inputVar = new sendEmailOfNewProjectFormDetails.inputVariables();
        inputVar.contactId = conId1;
        inputVar.oppId = oppId;
        
        List<sendEmailOfNewProjectFormDetails.inputVariables> inputList = new List<sendEmailOfNewProjectFormDetails.inputVariables>();
        inputList.add(inputVar);

        List<sendEmailOfNewProjectFormDetails.outputVariables> outputList = sendEmailOfNewProjectFormDetails.sendEmailOfNewProjectsForm(inputList);

        for (sendEmailOfNewProjectFormDetails.outputVariables outputVar : outputList) {
            System.debug('success: ' + outputVar.result);
            //System.debug('message: ' + outputVar.message);
        }
    }

    global class ResponseWrapper {
        public String status { get; set; }
        public Id accountId { get; set; }
        public Id opportunityId { get; set; }
        public String stackTrace { get; set; }
    }
}