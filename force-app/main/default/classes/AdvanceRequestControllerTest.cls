@isTest
private class AdvanceRequestControllerTest {

    // Helper method to create a ContentVersion for testing
    // Returns ContentVersion ID
    private static Id createTestContentVersion(Id relatedRecordId, String title, String pathOnClient, Blob body) {
        ContentVersion cv = new ContentVersion(
            Title = title,
            PathOnClient = pathOnClient,
            VersionData = body,
            Origin = 'H' // Using 'H' as it's common for test data
            // Not setting FirstPublishLocationId here to keep it simple
        );
        insert cv;
        // Return the ID of the inserted ContentVersion
        return cv.Id;
    }

    // Test setup data
    @testSetup static void makeData(){
        // Create a parent record (e.g., Account) to link the file to
        Account parentAcc = new Account(Name='Test Parent Account');
        insert parentAcc;

        // Create a dummy ContentVersion for the signature image
        Blob dummyImageData = EncodingUtil.base64Decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='); // Tiny 1x1 pixel PNG
        createTestContentVersion(null, 'Test Signature', 'signature.png', dummyImageData);

        // Check if network exists to avoid duplicates in test setup if run multiple times (less likely needed now)
        List<Network> existingNets = [SELECT Id FROM Network WHERE Name = 'Mobilization Funding' LIMIT 1];
        
    }

    // --- Tests for the Controller Constructor (implicitly called via VF page context) ---

    @isTest static void testControllerConstructor_Success() {
        ContentVersion testSigCv = [SELECT Id FROM ContentVersion WHERE Title = 'Test Signature' LIMIT 1];
        PageReference pageRef = Page.DisbursementPerjuryDoc; // Or Page.DynamicPDFRenderer
        Test.setCurrentPage(pageRef);
        String testDownloadUrl = 'https://example.com/download?ids=' + testSigCv.Id + '&otherParam=value';
        ApexPages.currentPage().getParameters().put('agreementDate', 'Jun 1, 2025');
        ApexPages.currentPage().getParameters().put('borrowerName', 'Test Borrower');
        ApexPages.currentPage().getParameters().put('advanceDate', 'Jun 10, 2025');
        ApexPages.currentPage().getParameters().put('advanceAmount', '5000');
        ApexPages.currentPage().getParameters().put('purpose', 'Test Purpose');
        ApexPages.currentPage().getParameters().put('borrowerEntityName', 'Test Entity Inc.');
        ApexPages.currentPage().getParameters().put('signatoryName', 'Test Signatory');
        ApexPages.currentPage().getParameters().put('signatoryTitle', 'CEO');
        ApexPages.currentPage().getParameters().put('contentDownloadUrlParam', testDownloadUrl);

        Test.startTest();
        AdvanceRequestController controller = new AdvanceRequestController();
        Test.stopTest();

        // Verification (Implicit via code execution for coverage)
        System.debug('Constructor Success Test - Controller agreementDate: ' + controller.agreementDate);
        System.debug('Constructor Success Test - Controller signatureBase64 populated: ' + String.isNotBlank(controller.signatureBase64));
        System.debug('Constructor Success Test - Controller dynamicHtmlContent size: ' + (controller.dynamicHtmlContent != null ? controller.dynamicHtmlContent.length() : 0) );
        // Check HTML contains specific elements (manual inspection of logs if needed)
        System.debug('Constructor Success Test - HTML contains img tag with Base64: ' + controller.dynamicHtmlContent?.contains('data:image/png;base64,')); // Check if correct img src is used
    }

    @isTest static void testControllerConstructor_NoContentVersion() {
        PageReference pageRef = Page.DisbursementPerjuryDoc; // Or Page.DynamicPDFRenderer
        Test.setCurrentPage(pageRef);
        String nonExistentCvId = '068000000000001AAA';
        String testDownloadUrl = 'https://example.com/download?ids=' + nonExistentCvId + '&otherParam=value';
        ApexPages.currentPage().getParameters().put('contentDownloadUrlParam', testDownloadUrl);
        ApexPages.currentPage().getParameters().put('borrowerName', 'No CV Borrower');

        Test.startTest();
        AdvanceRequestController controller = new AdvanceRequestController();
        Test.stopTest();

        // Verification
        System.debug('Constructor No CV Test - Controller signatureBase64 populated: ' + String.isNotBlank(controller.signatureBase64)); // Should be false
        System.debug('Constructor No CV Test - Controller dynamicHtmlContent size: ' + (controller.dynamicHtmlContent != null ? controller.dynamicHtmlContent.length() : 0) );
        // Check HTML contains placeholder
        System.debug('Constructor No CV Test - HTML contains placeholder: ' + controller.dynamicHtmlContent?.contains('[Signature Image Not Available]'));
    }

    @isTest static void testControllerConstructor_UrlParsingFail() {
        PageReference pageRef = Page.DisbursementPerjuryDoc; // Or Page.DynamicPDFRenderer
        Test.setCurrentPage(pageRef);
        String malformedUrl = 'https://example.com/download?noIdsHere=true';
        ApexPages.currentPage().getParameters().put('contentDownloadUrlParam', malformedUrl);
        ApexPages.currentPage().getParameters().put('borrowerName', 'Malformed URL Borrower');

        Test.startTest();
        AdvanceRequestController controller = new AdvanceRequestController();
        Test.stopTest();

        // Verification
        System.debug('Constructor URL Fail Test - Controller signatureBase64 populated: ' + String.isNotBlank(controller.signatureBase64)); // Should be false
        System.debug('Constructor URL Fail Test - Controller dynamicHtmlContent size: ' + (controller.dynamicHtmlContent != null ? controller.dynamicHtmlContent.length() : 0) );
         // Check HTML contains placeholder
        System.debug('Constructor URL Fail Test - HTML contains placeholder: ' + controller.dynamicHtmlContent?.contains('[Signature Image Not Available]'));
    }

     // --- Tests for the Invocable Method & Queueable Execution ---

    @isTest static void testQueueableExecution_Success() {
        Account parentAcc = [SELECT Id FROM Account WHERE Name = 'Test Parent Account' LIMIT 1];
        ContentVersion testSigCv = [SELECT Id FROM ContentVersion WHERE Title = 'Test Signature' LIMIT 1];

        AdvanceRequestController.PDFRequest req = new AdvanceRequestController.PDFRequest();
        req.recordId = parentAcc.Id;
        req.agreementDate = 'Jul 1, 2025';
        req.borrowerName = 'Queueable Borrower';
        req.advanceDate = 'Jul 15, 2025'; // Test a parsable date format if possible
        req.advanceAmount = '12345';
        req.purpose = 'Queueable Test';
        req.borrowerEntityName = 'Queueable Entity';
        req.signatoryName = 'Queueable Signatory';
        req.signatoryTitle = 'Q Tester';
        // Pass the non-decoded URL, as the Queueable expects this now
        req.contentDownloadUrl = 'https://example.com/download?ids=' + testSigCv.Id + '&otherParam=value'; // No need to encode here

        List<AdvanceRequestController.PDFRequest> requests = new List<AdvanceRequestController.PDFRequest>{ req };

        Test.startTest();
        AdvanceRequestController.generateAndAttachPDF(requests);
        Test.stopTest(); // This executes the Queueable job synchronously

        // Verification (Query records created by the Queueable job)
        List<ContentVersion> createdFiles = [
            SELECT Id, Title, PathOnClient, NetworkId
            FROM ContentVersion
            WHERE Title LIKE '%Queueable Borrower%' ORDER BY CreatedDate DESC
        ];
        List<ContentDocumentLink> createdLinks = [
            SELECT Id, LinkedEntityId, ContentDocumentId
            FROM ContentDocumentLink
            WHERE LinkedEntityId = :parentAcc.Id
        ];

        System.debug('Queueable Success Test - # Files created: ' + createdFiles.size()); // Expect 1
        System.debug('Queueable Success Test - # Links created: ' + createdLinks.size()); // Expect 1
        if (!createdFiles.isEmpty()) {
             System.debug('Queueable Success Test - File Title: ' + createdFiles[0].Title);
             System.debug('Queueable Success Test - File Path: ' + createdFiles[0].PathOnClient);
             System.debug('Queueable Success Test - File NetworkId: ' + createdFiles[0].NetworkId); // Check if NetworkId was set
        }
        if (!createdLinks.isEmpty()) {
             System.debug('Queueable Success Test - Link EntityId: ' + createdLinks[0].LinkedEntityId);
        }
    }

    @isTest static void testQueueableExecution_BlankDate() {
        Account parentAcc = [SELECT Id FROM Account WHERE Name = 'Test Parent Account' LIMIT 1];
        ContentVersion testSigCv = [SELECT Id FROM ContentVersion WHERE Title = 'Test Signature' LIMIT 1];

        AdvanceRequestController.PDFRequest req = new AdvanceRequestController.PDFRequest();
        req.recordId = parentAcc.Id;
        req.borrowerName = 'Blank Date Borrower';
        req.advanceDate = null; // Test blank date
        req.advanceAmount = '999';
        req.contentDownloadUrl = 'https://example.com/download?ids=' + testSigCv.Id;

        List<AdvanceRequestController.PDFRequest> requests = new List<AdvanceRequestController.PDFRequest>{ req };

        Test.startTest();
        AdvanceRequestController.generateAndAttachPDF(requests);
        Test.stopTest(); // Execute Queueable job

        // Verification
        List<ContentVersion> createdFiles = [
            SELECT Id, Title, PathOnClient
            FROM ContentVersion WHERE Title LIKE '%Blank Date Borrower%' ORDER BY CreatedDate DESC
        ];
         List<ContentDocumentLink> createdLinks = [
            SELECT Id FROM ContentDocumentLink WHERE LinkedEntityId = :parentAcc.Id
        ];

        System.debug('Queueable Blank Date Test - # Files created: ' + createdFiles.size()); // Expect 1
        System.debug('Queueable Blank Date Test - # Links created: ' + createdLinks.size()); // Expect 1
         if (!createdFiles.isEmpty()) {
             System.debug('Queueable Blank Date Test - File Title: ' + createdFiles[0].Title); // Should contain today's date formatted YYYY-MM-DD
             System.debug('Queueable Blank Date Test - File Path: ' + createdFiles[0].PathOnClient);
        }
    }

     @isTest static void testGenerateAndAttachPDF_SkipsOnNoRecordId() {
        ContentVersion testSigCv = [SELECT Id FROM ContentVersion WHERE Title = 'Test Signature' LIMIT 1];

        AdvanceRequestController.PDFRequest req = new AdvanceRequestController.PDFRequest();
        req.recordId = null; // *** Test null recordId ***
        req.borrowerName = 'No Record ID Borrower';
        req.contentDownloadUrl = 'https://example.com/download?ids=' + testSigCv.Id;

        List<AdvanceRequestController.PDFRequest> requests = new List<AdvanceRequestController.PDFRequest>{ req };

        Test.startTest();
        AdvanceRequestController.generateAndAttachPDF(requests);
        Test.stopTest(); // Allow async processes to attempt running (none should start for this request)

        // Verification (Query should find NO files related to this test)
        List<ContentVersion> createdFiles = [
            SELECT Id FROM ContentVersion WHERE Title LIKE '%No Record ID Borrower%'
        ];

        System.debug('Invocable Skip Test - # Files created: ' + createdFiles.size()); // Expect 0
    }
}