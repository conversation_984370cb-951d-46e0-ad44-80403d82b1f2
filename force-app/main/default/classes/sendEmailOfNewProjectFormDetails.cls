@SuppressWarnings('PMD')
public without sharing class sendEmailOfNewProjectFormDetails {
    @InvocableMethod(label='Send Email Of New Projects Form')
    public static List<outputVariables> sendEmailOfNewProjectsForm(List<inputVariables> inputVariables) {

        List<outputVariables> outputVariablesList = new List<outputVariables>();

        for(inputVariables inputVariable : inputVariables) {
            // List<Opportunity> opportunityRecords = new List<Opportunity>();
            // List<Account> accountRecords = new List<Account>();
            // List<Contact> contactRecords = new List<Contact>();

            Opportunity opp = new Opportunity();
            Account acc = new Account();
            Contact con = new Contact();
            
            String accountDetails = '';
            String contactDetails = '';
            String opportunityDetails = '';
            
            outputVariables outputVar = new outputVariables(); 
            
            try {               
                if (inputVariable.contactId != null ) {
                    con = [SELECT Id, AccountId, Web_Entry__c, SSN__c, Title, MailingState, MailingStreet, Married__c, Ownership__c, MailingPostalCode, MailingCountry, MailingCity, Life_Insurance_Policy_Limit__c, Date_of_Birth__c, Do_you_have_a_life_insurance_policy__c, FirstName, LastName, Email 
                                      FROM Contact WHERE Id = :inputVariable.contactId LIMIT 1];

                    acc = [SELECT Id, Name, of_Owners__c, Year_Founded__c, Website, Signed_App__c, Phone, NumberOfEmployees, Email__c, EIN__c, Description, BillingStreet, BillingState, BillingPostalCode, BillingCity 
                                       FROM Account WHERE Id = :con.AccountId LIMIT 1];

                    if (acc != null) {
                        accountDetails += '<br/><h2>Business Information</h2><br/>';
                        //for (Account acc : accountRecords) {
                        if (acc.Name != null) {
                            accountDetails += '<p>Business Name: ' + acc.Name + '</p>';
                        } else {
                            accountDetails += '<p>Business Name: </p>';
                        }
                            
                            //accountDetails += '<p>Business Name: ' + acc.Name + '</p>';
                        //}
                    }
                }
                
                if (inputVariable.oppId != null ) {
                    opp = [SELECT Id, Name, AccountId, App_Signature__c,Signed_App__c, Signature_Picture__c, Amount, of_active_contracts_POs__c, UCC_Filings__c, Supporting_Docs__c, StageName, Overhead_Debt_Schedule__c, Loan_Amount_Requested__c, Confirmation_Email__c, Current_Lawsuits__c, Bankruptcy__c, Bad_Debt__c, CloseDate 
                                          FROM Opportunity WHERE Id = :inputVariable.oppId LIMIT 1];
                    
                    if (opp.Name != null) {
                        opportunityDetails += '<p>Project Name: ' + opp.Name + '</p>';
                    } else {
                        opportunityDetails += '<p>Project Name: </p>';
                    }
                    opportunityDetails += '<br/><h2>Additional Information</h2><br/>';
                    if (opp.UCC_Filings__c != null) {
                        opportunityDetails += '<p>UCC Filings: ' + opp.UCC_Filings__c + '</p>';
                    } else {
                        opportunityDetails += '<p>UCC Filings: </p>';
                    }
                    if (opp.Bad_Debt__c != null) {
                        opportunityDetails += '<p>Delinquent on Debt: ' + opp.Bad_Debt__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Delinquent on Debt: </p>';
                    }
                    if (opp.Bankruptcy__c != null) {
                        opportunityDetails += '<p>Bankruptcy History: ' + opp.Bankruptcy__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Bankruptcy History: </p>';
                    }
                    if (opp.Current_Lawsuits__c != null) {
                        opportunityDetails += '<p>Current Lawsuits: ' + opp.Current_Lawsuits__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Current Lawsuits: </p>';
                    }

                    opportunityDetails += '<br/><h2>Uploading Your Documents</h2><br/>';
                    if (opp.Supporting_Docs__c != null) {
                        opportunityDetails += '<p>Supporting Documents: ' + opp.Supporting_Docs__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Supporting Documents: </p>';
                    }

                    opportunityDetails += '<br/><h2>Verify and Submit</h2><br/>';                   
                    if (opp.Signed_App__c != null) {
                        opportunityDetails += '<p>Date: ' + opp.Signed_App__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Date: </p>';
                    }
                    if (opp.Confirmation_Email__c != null) {
                        opportunityDetails += '<p>Confirmation Email: ' + opp.Confirmation_Email__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Confirmation Email: </p>';
                    }
                    if (opp.Signed_App__c != null) {
                        opportunityDetails += '<p>Signature: ' + opp.App_Signature__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Signature: </p>';
                    }
                    /*if (opp.Signature_Picture__c != null) {
                        opportunityDetails += '<p>Signature: ' + '<img src="https://computing-agility-5311-dev-ed.scratch.file.force.com/sfc/dist/version/download/?oid=00DE100000C1Ijx&ids=068E1000007cR6P&d=%2Fa%2FE1000000QkiL%2FpNpi1RLWHP4hE0_t8hV1LeJvMv0_ci8ULAniMcUYC9Q&asPdf=false">' + '</p>';
                    } else {
                        opportunityDetails += '<p>Signature: </p>';
                    }*/
                    /*if (opp.Signature_Picture__c != null) {
                        String signatureImageUrl;
                        
                        // Extract image source from the rich text field
                        Pattern p = Pattern.compile('src="(.*?)"');
                        Matcher m = p.matcher(opp.Signature_Picture__c);
                        if (m.find()) {
                            signatureImageUrl = m.group(1);
                            
                            // Ensure the URL is fully qualified
                            if (!signatureImageUrl.startsWith('https')) {
                                signatureImageUrl = URL.getSalesforceBaseUrl().toExternalForm() + signatureImageUrl;
                            }
                        }
                    
                        if (signatureImageUrl != null) {
                            opportunityDetails += '<p>Signature:</p><img src="' + signatureImageUrl + '" style="max-width:200px;"/>';
                        } else {
                            opportunityDetails += '<p>Signature: Not Available</p>';
                        }
                    } else {
                        opportunityDetails += '<p>Signature: Not Available</p>';
                    }*/

                    //}
                }

                String emailBody = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title></title></head><body>';
                //Business Loan Application Form
                emailBody += '<h1>New Projects Form</h1><br/>';
                emailBody += accountDetails;
                emailBody += opportunityDetails;
               
                emailBody += '</body></html>';
                
                String loggerTransactionId;
                loggerTransactionId = Nebula.Logger.getTransactionId();

                Nebula.Logger.info('Starting BatchableLogger');
                Nebula.Logger.saveLog();
				
				
                System.enqueueJob(new PDFGeneratorQueueable(inputVariable.oppId, emailBody, inputVariable.contentDownloadUrl, 'New Loan Request as Existing Form Details',loggerTransactionId));
                //callPDFGenerator(inputVariable.oppId, emailBody, inputVariable.contentDownloadUrl);
                //createAndLinkPdfDocument(emailBody, inputVariable.oppId);

                Organization orgInfo = [SELECT Id, Name, OrganizationType, IsSandbox FROM Organization LIMIT 1];
                String orgWideDisplayName;
                String senderAddress;
                if (orgInfo.IsSandbox) {
                    orgWideDisplayName = System.Label.Form_Details;
                    //senderAddress = System.Label.Form_Submission_Receiver_Email;
                    senderAddress = System.Label.Sender_Address_New_Project_Form_Sandbox;
                } else {
                    orgWideDisplayName = System.Label.Form_Details_Prod;
                    //senderAddress = System.Label.Form_Submission_Receiver_Email_Prod;
                    senderAddress = System.Label.Sender_Address_New_Project_Form;
                }

                List<String> emailAddressList = senderAddress.split(',');

                for (Integer i = 0; i < emailAddressList.size(); i++) {
                    emailAddressList[i] = emailAddressList[i].trim();
                }

                List<OrgWideEmailAddress> owealist = [SELECT Id, Address, DisplayName FROM OrgWideEmailAddress WHERE Address =: orgWideDisplayName];

                List<Messaging.SingleEmailMessage> emailMessages = new List<Messaging.SingleEmailMessage>();
                Messaging.SingleEmailMessage formDetails = new Messaging.SingleEmailMessage();
                //formDetails.setToAddresses(new String[]{senderAddress});
                formDetails.setToAddresses(emailAddressList);
                
                if (!owealist.isEmpty()) formDetails.setOrgWideEmailAddressId(owealist[0].Id);
                formDetails.setSubject('New Projects Form Details');

                System.debug('Content Download URL:172 ' + inputVariable.contentDownloadUrl);
                
                if (emailBody.contains('</body>')) {
                    emailBody = emailBody.replace('</body>', 
                                                    '<p>Signature Picture: <br/><img src="' + inputVariable.contentDownloadUrl + '" alt="Signature" width="250" height=auto;/></p></body></html>');
                } 

                EmailTemplate emailTemplate = [SELECT Id, Name, Body, Subject, HtmlValue FROM EmailTemplate WHERE Name = 'Form Submission Data Email' LIMIT 1];
                String emailBody2 = emailTemplate.Body;
                String htmlBody = emailTemplate.HtmlValue;
                List<String> bodies = new List<String>();
                String body = (htmlBody != null) ? htmlBody : emailBody2;
                body = body.replace('&lt;&lt;&lt;Form Data&gt;&gt;&gt;', emailBody );

                formDetails.setHtmlBody(body);
                emailMessages.add(formDetails);   

                if (!emailMessages.isEmpty() || Test.isRunningTest()) {
                    try {
                        if (!Test.isRunningTest()) {
                            Map<String, System.orgLimit> limitsMap = orgLimits.getMap();
                            System.orgLimit objSingleEMailLimit = limitsMap.get('SingleEmail');

                            if(objSingleEMailLimit.getValue() < objSingleEMailLimit.getLimit()) {
                        	    Messaging.sendEmail(emailMessages);
                            }
                        }
                    } catch (Exception e) {
                        System.debug('An exception occurred while sending the email: ' + e.getMessage() + ' --> ' + e.getStackTraceString());
                        throw new AuraHandledException(e.getMessage());
                    }
                }   

                outputVar.result = true;
            } catch (Exception e) {
                System.debug('An exception occurred: ' + e.getMessage() + ' --> ' + e.getStackTraceString());
                outputVar.result = false;
                throw new AuraHandledException(e.getMessage());
            }

            outputVariablesList.add(outputVar);
        }
       
        return outputVariablesList;
    }
    
     /*@future(callout=true)
     public static void callPDFGenerator(String oppId, String emailBody, String contentDownloadUrl){
         String encodedUrl = EncodingUtil.urlEncode(emailBody, 'UTF-8');
         String encodedPublicUrl = EncodingUtil.urlEncode(contentDownloadUrl, 'UTF-8');
        // PageReference formDocument = new PageReference('/apex/FormPDFGeneratorController?Id=' + oppId + '&contentDownloadUrl=' + encodedUrl);


        PageReference formDocument = new PageReference('/apex/FormPDFGenerator?Id=' +oppId + '&emailBody=' + encodedUrl+ '&contentDownloadUrl=' + encodedPublicUrl);
        formDocument.setRedirect(false);
         
        Blob documentBlob = null;
         
        try {
            documentBlob = formDocument.getContentAsPDF();
        } catch (Exception e) {
            System.debug('Error Generating PDF: ' + e.getMessage() + ' ' +e.getStackTraceString());
            throw e;
        }
         //EncodingUtil.base64Encode(documentBlob);
         System.debug('documentBlob '+EncodingUtil.base64Encode(documentBlob));
        System.debug('documentBlob '+documentBlob);
     
          ContentVersion contentVersion = new ContentVersion(
            Title = 'New Loan Request as Existing Form Details', //'rty2', //
            PathOnClient = 'NewLoanRequestAsExistingFormDetails.pdf',  //'rty2' + '.pdf', //
            VersionData = documentBlob,
            IsMajorVersion = true,
            ContentLocation = 'S'
            //NetworkId = netId
        );
         
        //if (Test.isRunningTest()) { 
            try {
			Id netId = [SELECT Id FROM Network LIMIT 1].Id;
                contentVersion.NetworkId = netId;
            } catch (Exception e) {
                System.debug('Not in a network context: ' + e.getMessage());
            }
        //}
        insert contentVersion;
         System.debug('contentVersion '+contentVersion);

        contentVersion = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id];
        
        ContentDocumentLink contentDocLink = new ContentDocumentLink(
            ContentDocumentId = contentVersion.ContentDocumentId,
            LinkedEntityId = oppId, 
            Visibility = 'AllUsers'
        );
        insert as system contentDocLink;   
         System.debug('contentDocLink '+contentDocLink);
         
    }*/

    //public static void createAndLinkPdfDocument(String emailBody, Id linkedEntityId) {
        //FormUtility.createAndLinkPdfDocument(emailBody, linkedEntityId, 'New Loan Request as Existing Form Details', 'NewLoanRequestAsExistingFormDetails.pdf');     
        /*Blob pdfBlob = Blob.toPdf(emailBody);
        
        ContentVersion contentVersion = new ContentVersion(
            Title = 'New Project Form Details',
            PathOnClient = 'New Project Form Details.pdf',
            VersionData = pdfBlob,
            IsMajorVersion = true,
            ContentLocation = 'S'
        );
        if (Test.isRunningTest()) { 
            try {
			Id netId = [SELECT Id FROM Network LIMIT 1].Id;
                contentVersion.NetworkId = netId;
            } catch (Exception e) {
                System.debug('Not in a network context: ' + e.getMessage());
            }
        }
        insert contentVersion;

        contentVersion = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id];
        
        ContentDocumentLink contentDocLink = new ContentDocumentLink(
            ContentDocumentId = contentVersion.ContentDocumentId,
            LinkedEntityId = linkedEntityId, 
            Visibility = 'AllUsers'
        );
        insert contentDocLink;*/
    //}

    public class inputVariables {
        @InvocableVariable 
        public Id contactId;
        // @InvocableVariable 
        // public Id accountId;
        @InvocableVariable 
        public Id oppId;
        @InvocableVariable(label='Content Download URL')
        public String contentDownloadUrl;
    }

    public class outputVariables {
        @InvocableVariable 
        public Boolean result;
    }
}