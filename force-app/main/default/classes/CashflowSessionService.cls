@SuppressWarnings('PMD')
public class CashflowSessionService {
    public static void processCompletedSessions(List<Cash_Flow_Session__c> sessions) {
        System.debug('>> processCompletedSessions ENTER. sessions: ' + sessions);
        
        // Build a map of sessionId to session for later lookups
        Map<Id, Cash_Flow_Session__c> sessionById = new Map<Id, Cash_Flow_Session__c>();
        for (Cash_Flow_Session__c s : sessions) {
            sessionById.put(s.Id, s);
        }
        
        // A) Collect non-blank project IDs to check
        Set<String> projIdsToCheck = new Set<String>();
        for (Cash_Flow_Session__c s : sessions) {
            if (String.isNotBlank(s.Project__c)) {
                projIdsToCheck.add(s.Project__c);
            }
        }
        System.debug('Collected projIdsToCheck: ' + projIdsToCheck);
        
        // B) Query existing Projects by Id
        Map<String, Project__c> existingProjects = new Map<String, Project__c>();
        if (!projIdsToCheck.isEmpty()) {
            for (Project__c p : [
                SELECT Id, Name
                  FROM Project__c
                 WHERE Id IN :projIdsToCheck
            ]) {
                existingProjects.put(p.Id, p);
            }
        }
        System.debug('Existing projects found: ' + existingProjects.keySet());
        
        // C) Prepare list for new projects and map session -> new project name
        List<Project__c> projsToInsert = new List<Project__c>();
        Map<Id, String> sessionToNewProjName = new Map<Id, String>();
        
        // D) Process each session
        for (Cash_Flow_Session__c s : sessions) {
            if (String.isBlank(s.Project__c)) {
                // Session has no project -> create a new one
                String genName = 'Auto-' + s.Id;
                System.debug('Session ' + s.Id + ' has NULL Project__c; queuing new Project Name=' + genName);
                Project__c np = new Project__c(Name = genName);
                projsToInsert.add(np);
                sessionToNewProjName.put(s.Id, genName);
            } else {
                // Session specifies a project Id
                System.debug('Session ' + s.Id + ' has Project__c=' + s.Project__c);
                if (existingProjects.containsKey(s.Project__c)) {
                    // Project exists
                    System.debug('Project exists for session ' + s.Id + '; calling controller with projectId=' + s.Project__c);
                    CashFlowSessionController.createRecordsFromDynamoData(
                        s.AccountId__c,
                        s.Processed_JSON__c,
                        s.Project__c
                    );
                } else {
                    // Project Id not found -> create new one
                    String genName = 'Auto-' + s.Project__c;
                    System.debug('Project ' + s.Project__c + ' not found; queuing new Project Name=' + genName + ' for session ' + s.Id);
                    Project__c np = new Project__c(Name = genName);
                    projsToInsert.add(np);
                    sessionToNewProjName.put(s.Id, genName);
                }
            }
        }
        
        // E) Insert any new projects
        if (!projsToInsert.isEmpty()) {
            System.debug('Inserting new Projects: ' + projsToInsert);
            insert projsToInsert;
            System.debug('Inserted Projects: ' + projsToInsert);
        } else {
            System.debug('No new Projects to insert');
        }
        
        // F) Build map of generated name -> new Project Id
        Map<String, Id> newProjNameToId = new Map<String, Id>();
        for (Project__c p : projsToInsert) {
            System.debug('Mapping new Project Name=' + p.Name + ' to Id=' + p.Id);
            newProjNameToId.put(p.Name, p.Id);
        }
        
        // G) For each session that needed a new project, call controller
        for (Id sessId : sessionToNewProjName.keySet()) {
            String projName = sessionToNewProjName.get(sessId);
            Id newProjId   = newProjNameToId.get(projName);
            Cash_Flow_Session__c sess = sessionById.get(sessId);
            System.debug('Now calling controller for session ' + sessId +
                         ' with AccountId=' + sess.AccountId__c +
                         ', JSON=' + sess.Processed_JSON__c +
                         ', Project__c=' + newProjId);
            CashFlowSessionController.createRecordsFromDynamoData(
                sess.AccountId__c,
                sess.Processed_JSON__c,
                newProjId
            );
        }
        
        System.debug('<< processCompletedSessions EXIT');
    }
}