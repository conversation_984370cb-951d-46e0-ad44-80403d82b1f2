@SuppressWarnings('PMD')
public without sharing class sendEmailOfApplicationFormDetails {

    @InvocableMethod(label='Send Email Of Application Detail Form')
    public static List<outputVariables> sendEmailOfApplicationDetailForm(List<inputVariables> inputVariables) {

        List<outputVariables> outputVariablesList = new List<outputVariables>();

        for(inputVariables inputVariable : inputVariables) {
            Opportunity opp = new Opportunity();
            Account acc = new Account();
            Contact con = new Contact();
            Contact con2 = new Contact();

            String accountDetails = '';
            String contactDetails = '';
            String contactDetails2 = '';
            String opportunityDetails = '';
            String accDetailsInOpp = '';
            
            outputVariables outputVar = new outputVariables();
            
            //try {
                if (inputVariable.accountId != null) {
                    acc = [SELECT Id, Name, of_Owners__c, Year_Founded__c, Website, Phone, NumberOfEmployees, Email__c, EIN__c, Description, BillingStreet, BillingState, BillingPostalCode, BillingCity 
                                      FROM Account WHERE Id = :inputVariable.accountId LIMIT 1];

                    accountDetails += '<br/><h2>Business Information</h2><br/>';
                    if (acc.Name != null) {
                        accountDetails += '<p>Business Name: ' + acc.Name + '</p>';
                    } else {
                        accountDetails += '<p>Business Name: </p>';
                    }
                    accountDetails += '<h5>Business Address</h5>';
                    if (acc.BillingStreet != null) {
                        accountDetails += '<p>Street Address: ' + acc.BillingStreet + '</p>';
                    } else {
                        accountDetails += '<p>Street Address: </p>';
                    }
                    if (acc.BillingCity != null) {
                        accountDetails += '<p>City: ' + acc.BillingCity + '</p>';
                    } else {
                        accountDetails += '<p>City: </p>';
                    }
                    if (acc.BillingState != null) {
                        accountDetails += '<p>State: ' + acc.BillingState + '</p>';
                    } else {
                        accountDetails += '<p>State: </p>';
                    }
                    if (acc.BillingPostalCode != null) {
                        accountDetails += '<p>Zip Code: ' + acc.BillingPostalCode + '</p>';
                    } else {
                        accountDetails += '<p>Zip Code: </p>';
                    }
                    if (acc.Website != null) {
                        accountDetails += '<p>Website: ' + acc.Website + '</p>';
                    } else {
                        accountDetails += '<p>Website: </p>';
                    }
                    if (acc.Year_Founded__c != null) {
                        accountDetails += '<p>Year Business Was Founded: ' + acc.Year_Founded__c + '</p>';
                    } else {
                        accountDetails += '<p>Year Business Was Founded: </p>';
                    }
                    if (acc.Description != null) {
                        accountDetails += '<p>Type of Work: ' + acc.Description + '</p>';
                    } else {
                        accountDetails += '<p>Type of Work: </p>';
                    }
                    if (acc.Phone != null) {
                        accountDetails += '<p>Business Phone: ' + acc.Phone + '</p>';
                    } else {
                        accountDetails += '<p>Business Phone: </p>';
                    }
                    if (acc.NumberOfEmployees != null) {
                        accountDetails += '<p>Number of Employees: ' + acc.NumberOfEmployees + '</p>';
                    } else {
                        accountDetails += '<p>Number of Employees: </p>';
                    }
                    if (acc.EIN__c != null) {
                        accountDetails += '<p>Federal Tax ID Number: ' + acc.EIN__c + '</p>';
                    } else {
                        accountDetails += '<p>Federal Tax ID Number: </p>';
                    }
                    if (acc.of_Owners__c != null) {
                        accountDetails += '<p>Number of Owners: ' + acc.of_Owners__c + '</p>';
                    } else {
                        accountDetails += '<p>Number of Owners: </p>';
                    }
                }
                
                if (inputVariable.contactOwnerId != null ) {
                    con = [SELECT Id, Web_Entry__c, SSN__c, Phone, Title, MailingState, MailingStreet, Married__c, Ownership__c, MailingPostalCode, MailingCountry, MailingCity, Life_Insurance_Policy_Limit__c, Date_of_Birth__c, Do_you_have_a_life_insurance_policy__c, FirstName, LastName, Email 
                                      FROM Contact WHERE AccountId = :inputVariable.accountId LIMIT 1];

                    contactDetails += '<br/><h2>Owner Information</h2><br/>';
                    if (con.FirstName != null || con.LastName != null) {
                        contactDetails += '<p>Name: ' + (con.FirstName != null ? con.FirstName : '') + ' ' + (con.LastName != null ? con.LastName : '') + '</p>';
                    } else {
                        contactDetails += '<p>Name: </p>';
                    }
                    
                    contactDetails += '<br/><h5>Home Address</h5>';
                    if (con.MailingStreet != null) {
                        contactDetails += '<p>Street: ' + con.MailingStreet + '</p>';
                    } else {
                        contactDetails += '<p>Street: </p>';
                    }
                    if (con.MailingCity != null) {
                        contactDetails += '<p>City: ' + con.MailingCity + '</p>';
                    } else {
                        contactDetails += '<p>City: </p>';
                    }
                    if (con.MailingState != null) {
                        contactDetails += '<p>State/Province: ' + con.MailingState + '</p>';
                    } else {
                        contactDetails += '<p>State/Province: </p>';
                    }
                    if (con.MailingPostalCode != null) {
                        contactDetails += '<p>Zip/Postal Code: ' + con.MailingPostalCode + '</p>';
                    } else {
                        contactDetails += '<p>Zip/Postal Code: </p>';
                    }
                    if (con.MailingCountry != null) {
                        contactDetails += '<p>Country: ' + con.MailingCountry + '</p>';
                    } else {
                        contactDetails += '<p>Country: </p>';
                    }

                    if (con.Phone != null) {
                        contactDetails += '<p>Cell Phone: ' + con.Phone + '</p>';
                    } else {
                        contactDetails += '<p>Cell Phone: </p>';
                    }
                    if (con.Email != null) {
                        contactDetails += '<p>Email: ' + con.Email + '</p>';
                    } else {
                        contactDetails += '<p>Email: </p>';
                    }
                    if (con.SSN__c != null) {
                        contactDetails += '<p>Social Security Number: ' + con.SSN__c + '</p>';
                    } else {
                        contactDetails += '<p>Social Security Number: </p>';
                    }
                    if (con.Married__c != null) {
                        contactDetails += '<p>Married: ' + con.Married__c + '</p>';
                    } else {
                        contactDetails += '<p>Married: </p>';
                    }
                    if (con.Date_of_Birth__c != null) {
                        contactDetails += '<p>Date of Birth: ' + con.Date_of_Birth__c + '</p>';
                    } else {
                        contactDetails += '<p>Date of Birth: </p>';
                    }
                    if (con.Title != null) {
                        contactDetails += '<p>Title: ' + con.Title + '</p>';
                    } else {
                        contactDetails += '<p>Title: </p>';
                    }
                    if (con.Ownership__c != null) {
                        contactDetails += '<p>Percent Ownership: ' + con.Ownership__c + '</p>';
                    } else {
                        contactDetails += '<p>Percent Ownership: </p>';
                    }
                    if (con.Do_you_have_a_life_insurance_policy__c != null) {
                        contactDetails += '<p>Do you have a Life Insurance Policy: ' + con.Do_you_have_a_life_insurance_policy__c + '</p>';
                    } else {
                        contactDetails += '<p>Do you have a Life Insurance Policy: </p>';
                    }
                    if (con.Life_Insurance_Policy_Limit__c != null) {
                        contactDetails += '<p>If Yes, what is policy limit?: ' + con.Life_Insurance_Policy_Limit__c + '</p>';
                    } else {
                        contactDetails += '<p>If Yes, what is policy limit?: </p>';
                    }
                }
                if (inputVariable.contactOwnerId2 != null ) {
                    con2 = [SELECT Id, Web_Entry__c, SSN__c, Phone, Title, MailingState, MailingStreet, Married__c, Ownership__c, MailingPostalCode, MailingCountry, MailingCity, Life_Insurance_Policy_Limit__c, Date_of_Birth__c, Do_you_have_a_life_insurance_policy__c, FirstName, LastName, Email 
                                      FROM Contact WHERE Id = :inputVariable.contactOwnerId2 LIMIT 1];

                    contactDetails2 += '<br/><h2>Owner Information #2</h2><br/>';
                    if (con2.FirstName != null || con2.LastName != null) {
                        contactDetails2 += '<p>Name: ' + (con2.FirstName != null ? con2.FirstName : '') + ' ' + (con2.LastName != null ? con2.LastName : '') + '</p>';
                    } else {
                        contactDetails2 += '<p>Name: </p>';
                    }

                    contactDetails2 += '<br/><h5>Home Address</h5><br/>';
                    if (con2.MailingStreet != null) {
                        contactDetails2 += '<p>Street: ' + con2.MailingStreet + '</p>';
                    } else {
                        contactDetails2 += '<p>Street: </p>';
                    }
                    if (con2.MailingCity != null) {
                        contactDetails2 += '<p>City: ' + con2.MailingCity + '</p>';
                    } else {
                        contactDetails2 += '<p>City: </p>';
                    }
                    if (con2.MailingState != null) {
                        contactDetails2 += '<p>State/Province: ' + con2.MailingState + '</p>';
                    } else {
                        contactDetails2 += '<p>State/Province: </p>';
                    }
                    if (con2.MailingPostalCode != null) {
                        contactDetails2 += '<p>Zip/Postal Code: ' + con2.MailingPostalCode + '</p>';
                    } else {
                        contactDetails2 += '<p>Zip/Postal Code: </p>';
                    }
                    if (con2.MailingCountry != null) {
                        contactDetails2 += '<p>Country: ' + con2.MailingCountry + '</p>';
                    } else {
                        contactDetails2 += '<p>Country: </p>';
                    }

                    if (con2.Phone != null) {
                        contactDetails2 += '<p>Cell Phone: ' + con2.Phone + '</p>';
                    } else {
                        contactDetails2 += '<p>Cell Phone: </p>';
                    }
                    if (con2.Email != null) {
                        contactDetails2 += '<p>Email: ' + con2.Email + '</p>';
                    } else {
                        contactDetails2 += '<p>Email: </p>';
                    }
                    if (con2.SSN__c != null) {
                        contactDetails2 += '<p>Social Security Number: ' + con2.SSN__c + '</p>';
                    } else {
                        contactDetails2 += '<p>Social Security Number: </p>';
                    }
                    if (con2.Married__c != null) {
                        contactDetails2 += '<p>Married: ' + con2.Married__c + '</p>';
                    } else {
                        contactDetails2 += '<p>Married: </p>';
                    }
                    if (con2.Date_of_Birth__c != null) {
                        contactDetails2 += '<p>Date of Birth: ' + con2.Date_of_Birth__c + '</p>';
                    } else {
                        contactDetails2 += '<p>Date of Birth: </p>';
                    }
                    if (con2.Title != null) {
                        contactDetails2 += '<p>Title: ' + con2.Title + '</p>';
                    } else {
                        contactDetails2 += '<p>Title: </p>';
                    }
                    if (con2.Ownership__c != null) {
                        contactDetails2 += '<p>Percent Ownership: ' + con2.Ownership__c + '</p>';
                    } else {
                        contactDetails2 += '<p>Percent Ownership: </p>';
                    }
                    if (con2.Do_you_have_a_life_insurance_policy__c != null) {
                        contactDetails2 += '<p>Do you have a Life Insurance Policy: ' + con2.Do_you_have_a_life_insurance_policy__c + '</p>';
                    } else {
                        contactDetails2 += '<p>Do you have a Life Insurance Policy: </p>';
                    }
                    if (con2.Life_Insurance_Policy_Limit__c != null) {
                        contactDetails2 += '<p>If Yes, what is policy limit?: ' + con2.Life_Insurance_Policy_Limit__c + '</p>';
                    } else {
                        contactDetails2 += '<p>If Yes, what is policy limit?: </p>';
                    }
                }
                
                if (inputVariable.oppId != null ) {
                    opp = [SELECT Id, Name, Amount, App_Signature__c, Signed_App__c, of_active_contracts_POs__c, UCC_Filings__c, Supporting_Docs__c, StageName, Overhead_Debt_Schedule__c, Loan_Amount_Requested__c, Confirmation_Email__c, Current_Lawsuits__c, Bankruptcy__c, Bad_Debt__c, CloseDate 
                                          FROM Opportunity WHERE Id = :inputVariable.oppId LIMIT 1];
                    
                    if (opp.Loan_Amount_Requested__c != null) {
                        accDetailsInOpp += '<p>Loan Amount Requested: ' + opp.Loan_Amount_Requested__c + '</p>';
                    } else {
                        accDetailsInOpp += '<p>Loan Amount Requested: </p>';
                    }

                    if (opp.of_active_contracts_POs__c != null) {
                        accDetailsInOpp += '<p>Number of Active Contracts: ' + opp.of_active_contracts_POs__c + '</p>';
                    } else {
                        accDetailsInOpp += '<p>Number of Active Contractss: </p>';
                    }
                    //opportunityDetails += '<p>Loan Amount Requested: ' + (opp.Loan_Amount_Requested__c != null ? opp.Loan_Amount_Requested__c : '') + '</p>';
                    //opportunityDetails += '<p>Number of Active Contracts: ' + (opp.of_active_contracts_POs__c != null ? opp.of_active_contracts_POs__c : '') + '</p>';
                    opportunityDetails += '<br/><h2>Additional Information</h2><br/>';
                    if (opp.UCC_Filings__c != null) {
                        opportunityDetails += '<p>UCC Filings: ' + opp.UCC_Filings__c + '</p>';
                    } else {
                        opportunityDetails += '<p>UCC Filings: </p>';
                    }
                    if (opp.Bad_Debt__c != null) {
                        opportunityDetails += '<p>Delinquent on Debt: ' + opp.Bad_Debt__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Delinquent on Debt: </p>';
                    }
                    if (opp.Bankruptcy__c != null) {
                        opportunityDetails += '<p>Bankruptcy History: ' + opp.Bankruptcy__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Bankruptcy History: </p>';
                    }
                    if (opp.Current_Lawsuits__c != null) {
                        opportunityDetails += '<p>Current Lawsuits: ' + opp.Current_Lawsuits__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Current Lawsuits: </p>';
                    }
                    opportunityDetails += '<br/><h2>Debt Schedule</h2><br/>';
                    if (opp.Overhead_Debt_Schedule__c != null) {
                        opportunityDetails += '<p>Overhead Debt Schedule: ' + opp.Overhead_Debt_Schedule__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Overhead Debt Schedule: </p>';
                    }
                    opportunityDetails += '<br/><h2>Uploading Your Documents</h2><br/>';
                    if (opp.Supporting_Docs__c != null) {
                        opportunityDetails += '<p>Uploaded Documents: ' + opp.Supporting_Docs__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Uploaded Documents: </p>';
                    }
                    opportunityDetails += '<br/><h2>Verify and Submit</h2><br/>';
                    if (opp.CloseDate != null) {
                        opportunityDetails += '<p>Date: ' + opp.CloseDate + '</p>';
                    } else {
                        opportunityDetails += '<p>Date: </p>';
                    }
                    if (opp.Confirmation_Email__c != null) {
                        opportunityDetails += '<p>Email For Confirmation: ' + opp.Confirmation_Email__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Email For Confirmation: </p>';
                    }
                    if (opp.Signed_App__c != null) {
                        opportunityDetails += '<p>Signature: ' + opp.App_Signature__c + '</p>';
                    } else {
                        opportunityDetails += '<p>Signature: </p>';
                    }
                }

                String emailBody = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><title></title></head><body>';
                emailBody += '<h1>Submit Application Form</h1><br/>';
                emailBody += accountDetails;
                emailBody += accDetailsInOpp;
                emailBody += contactDetails;
                emailBody += contactDetails2;
                emailBody += opportunityDetails;
                emailBody += '</body></html>';
            
            	String loggerTransactionId;
                loggerTransactionId = Nebula.Logger.getTransactionId();

                Nebula.Logger.info('Starting BatchableLogger');
                Nebula.Logger.saveLog();

                System.enqueueJob(new PDFGeneratorQueueable(inputVariable.oppId, emailBody, inputVariable.contentDownloadUrl, 'Application Form Details',loggerTransactionId));
                //createAndLinkPdfDocument(emailBody, inputVariable.oppId);

                Organization orgInfo = [SELECT Id, Name, OrganizationType, IsSandbox FROM Organization LIMIT 1];
                String orgWideDisplayName;
                String senderAddress;
                if (orgInfo.IsSandbox) {
                    orgWideDisplayName = System.Label.Form_Details;
                    //senderAddress = System.Label.Form_Submission_Receiver_Email;
                    senderAddress = System.Label.Sender_Address_Application_Form_Sandbox;
                } else {
                    orgWideDisplayName = System.Label.Form_Details_Prod;
                    //senderAddress = System.Label.Form_Submission_Receiver_Email_Prod;
                    senderAddress = System.Label.Sender_Address_Application_Form;
                }

                
                List<String> emailAddressList = senderAddress.split(',');

                for (Integer i = 0; i < emailAddressList.size(); i++) {
                    emailAddressList[i] = emailAddressList[i].trim();
                }
  
                List<OrgWideEmailAddress> owealist = [SELECT Id, Address, DisplayName FROM OrgWideEmailAddress WHERE Address =: orgWideDisplayName];

                List<Messaging.SingleEmailMessage> emailMessages = new List<Messaging.SingleEmailMessage>();
                Messaging.SingleEmailMessage formDetails = new Messaging.SingleEmailMessage();
                //formDetails.setToAddresses(new String[]{senderAddress});
                formDetails.setToAddresses(emailAddressList);
                
                if (!owealist.isEmpty()) formDetails.setOrgWideEmailAddressId(owealist[0].Id);
                formDetails.setSubject('Submit Application Form Details');

                System.debug('Content Download URL:172 ' + inputVariable.contentDownloadUrl);
                
                if (emailBody.contains('</body>')) {
                    emailBody = emailBody.replace('</body>', 
                                                    '<p>Signature Picture: <br/><img src="' + inputVariable.contentDownloadUrl + '" alt="Signature" width="250" height=auto;/></p></body></html>');
                }

                EmailTemplate emailTemplate = [SELECT Id, Name, Body, Subject, HtmlValue FROM EmailTemplate WHERE Name = 'Form Submission Data Email' LIMIT 1];
                String emailBody2 = emailTemplate.Body;
                String htmlBody = emailTemplate.HtmlValue;
                List<String> bodies = new List<String>();
                String body = (htmlBody != null) ? htmlBody : emailBody2;
                body = body.replace('&lt;&lt;&lt;Form Data&gt;&gt;&gt;', emailBody );
                
                formDetails.setHtmlBody(body);
                emailMessages.add(formDetails);   

            	if (!emailMessages.isEmpty() || Test.isRunningTest()) {
                    if (!Test.isRunningTest()) {
                        Map<String, System.orgLimit> limitsMap = orgLimits.getMap();
                        System.orgLimit objSingleEMailLimit = limitsMap.get('SingleEmail');

                        if(objSingleEMailLimit.getValue() < objSingleEMailLimit.getLimit()) {
                            Messaging.sendEmail(emailMessages);
                        }
                    }
            	}

                outputVar.success = true;
                outputVar.message = 'Email sent successfully.';
                outputVariablesList.add(outputVar);
            /*} catch (Exception ex) {
                System.debug('Exception '+ex);
                outputVar.success = false;
                outputVar.message = ex.getMessage();
                outputVariablesList.add(outputVar);
                throw new AuraHandledException(ex.getMessage() + ' ' +ex.getStackTraceString());
            }*/
        }

        return outputVariablesList;
    }

    //public static void createAndLinkPdfDocument(String emailBody, Id linkedEntityId) {


        //FormUtility.createAndLinkPdfDocument(emailBody, linkedEntityId, 'Application Form Details', 'ApplicationFormDetails.pdf');     
        /*
        ContentVersion contentVersion = new ContentVersion(
            Title = 'Application Form Details',
            PathOnClient = 'ApplicationFormDetails.pdf',
            VersionData = pdfBlob,
            IsMajorVersion = true,
            ContentLocation = 'S'
        );
        insert as system contentVersion;

        contentVersion = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id];
        
        ContentDocumentLink contentDocLink = new ContentDocumentLink(
            ContentDocumentId = contentVersion.ContentDocumentId,
            LinkedEntityId = linkedEntityId, 
            Visibility = 'AllUsers'
        );
        insert as system contentDocLink; */
    //}

    public class inputVariables {
        @InvocableVariable(label='Account Id' required=true)
        public Id accountId;
        @InvocableVariable(label='Contact Owner Id' required=true)
        public Id contactOwnerId;
        @InvocableVariable(label='Contact Owner Id2' required=false)
        public Id contactOwnerId2;
        @InvocableVariable(label='Opportunity Id' required=true)
        public Id oppId;
        @InvocableVariable(label='Content Download URL')
        public String contentDownloadUrl;
    }

    public class outputVariables {
        @InvocableVariable
        public Boolean success;
        @InvocableVariable
        public String message;
    }
}