@isTest
public class FlinksAuthorizeAsyncBatchTest {

    @isTest
    static void testFlinksAuthorizeAsyncBatch_AllScenarios() {
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Base_Url__c = 'https://api.test.com/',
            Customer_Id__c = '12345',
            Bearer_Token__c = 'testBearerToken'
        );
        insert config;

        User loggedInUser = [SELECT Id, ContactId FROM User WHERE Id = :UserInfo.getUserId()];

        Bank_Account__c bankAcc1 = new Bank_Account__c(
            Name = 'Test Account 1',
            Institution_Name__c = 'Test Bank 1',
            Login_ID__c = 'Login123',
            Is_Active__c = true,
            Contact__c = loggedInUser.ContactId,
            Request_Id__c = 'mockRequestId123'
        );
        insert bankAcc1;

        Test.setMock(HttpCalloutMock.class, new FlinksAuthorizeAsyncBatchMock());

        Test.startTest();
        Set<Id> retryBankAccIds = new Set<Id>{bankAcc1.Id};
        FlinksAuthorizeAsyncBatch batch = new FlinksAuthorizeAsyncBatch(retryBankAccIds);
        Database.executeBatch(batch, 1);
        Test.stopTest();

        Bank_Account__c updatedBankAcc1 = [SELECT Authorize_Async_Request_Id__c, Name FROM Bank_Account__c WHERE Id = :bankAcc1.Id];

        
    }

    private class FlinksAuthorizeAsyncBatchMock implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();

            if (req.getEndpoint().contains('/BankingServices/AuthorizeAsync')) {
                if (req.getBody().contains('mockRequestId123')) {
                    res.setStatusCode(203);
                    res.setBody('{"RequestId": "newMockRequestId124"}');
                } else if (req.getBody().contains('mockRequestId125')) {
                    res.setStatusCode(202);
                    res.setBody('{}');
                } else {
                    res.setStatusCode(400);
                    res.setBody('{"error": "Bad Request"}');
                }
            } else {
                res.setStatusCode(404);
                res.setBody('{"error": "Not Found"}');
            }

            return res;
        }
    }
}