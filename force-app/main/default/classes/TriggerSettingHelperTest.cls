/**
 * @description Test class for TriggerSettingHelper
 * <AUTHOR>
 */
@SuppressWarnings('PMD')  
@IsTest
private class TriggerSettingHelperTest {
    
    @IsTest
    static void testIsTriggerActive_ExistingActiveTrigger() {
        // Clear cache to ensure fresh test
        TriggerSettingHelper.clearCache();
        
        // Test with existing active trigger setting
        Boolean isActive = TriggerSettingHelper.isTriggerActive('AccountTriggerBeforeInsert');
        
    }
    
    @IsTest
    static void testIsTriggerActive_ExistingInactiveTrigger() {
        // Clear cache to ensure fresh test
        TriggerSettingHelper.clearCache();
        
        // Test with existing inactive trigger setting
        Boolean isActive = TriggerSettingHelper.isTriggerActive('CashflowTriggerAfterInsert');
        
    }
    
    @IsTest
    static void testIsTriggerActive_NonExistentTrigger() {
        // Clear cache to ensure fresh test
        TriggerSettingHelper.clearCache();
        
        // Test with non-existent trigger setting
        Boolean isActive = TriggerSettingHelper.isTriggerActive('NonExistentTrigger');
        
    }
    
    @IsTest
    static void testCaching() {
        // Clear cache to ensure fresh test
        TriggerSettingHelper.clearCache();
        
        // First call should query the database
        Boolean firstCall = TriggerSettingHelper.isTriggerActive('AccountTriggerBeforeInsert');
        
        // Second call should use cache
        Boolean secondCall = TriggerSettingHelper.isTriggerActive('AccountTriggerBeforeInsert');
        
    }
    
    @IsTest
    static void testGetAllTriggerSettings() {
        // Test getting all trigger settings
        Map<String, Boolean> allSettings = TriggerSettingHelper.getAllTriggerSettings();
        
    }
}