global class CheckDropboxJobStatusBatch implements Schedulable, Database.Batchable<SObject>, Database.AllowsCallouts {

    public void execute(SchedulableContext context) {
        Database.executeBatch(new CheckDropboxJobStatusBatch(), 10);
    }

    global Database.QueryLocator start(Database.BatchableContext BC) {
        return Database.getQueryLocator([
			SELECT Id, Dropbox_Uploaded_Date__c, Dropbox_Sync_Status__c, Dropbox_Async_Job_Id__c 
            FROM ContentVersion 
            WHERE Dropbox_Sync_Status__c = 'Processing - Waiting Dropbox Confirmation' 
            AND Dropbox_Async_Job_Id__c != null
        ]);
    }

    global void execute(Database.BatchableContext BC, List<ContentVersion> scope) {
        List<ContentVersion> cvToUpdate = new List<ContentVersion>();
        List<Custom_Exception__c> customExceptions = new List<Custom_Exception__c>();
        
        try {
            for (ContentVersion cvObj : scope) {
                

                Map<String,Object> returnMap = new Map<String,Object>();
                returnMap = DropboxController.checkDBJobStatus(cvObj.Dropbox_Async_Job_Id__c, cvObj.Id);
                HttpRequest req = (HttpRequest) returnMap.get('req');
                HttpResponse res = (HttpResponse) returnMap.get('res');
                
                //Logging the request/response
                Custom_Exception__c ceObj = (Custom_Exception__c) returnMap.get('log');
                customExceptions.add(ceObj);
                
                if (res.getStatusCode() == 200) {
                    Map<String, Object> resMap = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                    System.debug('resMap ' + resMap);
                    
                    // Handling the response based on the status
                    String tag = (String) resMap.get('.tag');
                    if (tag == 'complete') {
                        cvObj.Dropbox_Uploaded_Date__c = System.now();
                        cvObj.Dropbox_Sync_Status__c = 'Synced';
                        cvObj.Dropbox_Sync_Message__c = ''; // Empty message on success
                        cvToUpdate.add(cvObj);
                    } else if (tag == 'in_progress') {
                        // The job is still in progress; do not update status
                    } else if (tag == 'failed') {
                        cvObj.Dropbox_Uploaded_Date__c = null;
                        cvObj.Dropbox_Sync_Status__c = 'Failed';
                        cvObj.Dropbox_Sync_Message__c = 'Failed to sync with Dropbox';
                        cvToUpdate.add(cvObj);
                    }
                }  else {
                    // Handle non-200 responses, e.g., error responses
                    Map<String, Object> errorMap = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                    if (errorMap.containsKey('error')) {
                        Map<String, Object> errorDetails = (Map<String, Object>) errorMap.get('error');
                        String errorTag = (String) errorDetails.get('.tag');
                        String errorSummary = (String) errorMap.get('error_summary');
                        
                        if (errorTag != null) {
                            if (errorTag == 'invalid_async_job_id') {
                                cvObj.Dropbox_Sync_Message__c = 'Invalid async job ID: ' + errorSummary;
                            } else if (errorTag == 'internal_error') {
                                cvObj.Dropbox_Sync_Message__c = 'Internal error occurred: ' + errorSummary;
                            } else if (errorTag == 'other') {
                                cvObj.Dropbox_Sync_Message__c = 'Other error occurred: ' + errorSummary;
                            } else {
                                cvObj.Dropbox_Sync_Message__c = 'Unknown error: ' + errorSummary;
                            }
                        }
                        cvObj.Dropbox_Sync_Status__c = 'Failed';
                        cvObj.Dropbox_Uploaded_Date__c = null;
                        cvToUpdate.add(cvObj);
                    }
                }
                
            }
        } catch(Exception e) {
               customExceptions.add(UIT_Utility.LogExceptionStub(null, e, 'CheckDropboxJobStatusBatch'));
        }
        
        if(!cvToUpdate.isEmpty()) {
             Database.SaveResult[] updateResults = Database.update(cvToUpdate, false);
            for (Database.SaveResult result : updateResults) {
                if (!result.isSuccess()) {
                    System.debug('Failed to update record ID: ' + result.getId() + ', Error: ' + result.getErrors()[0].getMessage());
                }
            }
        }
        
        if(!customExceptions.isEmpty()) {
            Database.SaveResult[] insertResults = Database.insert(customExceptions, false);
            for (Database.SaveResult result : insertResults) {
                if (!result.isSuccess()) {
                    System.debug('Failed to insert record ID: ' + result.getId() + ', Error: ' + result.getErrors()[0].getMessage());
                }
            }
        } 
    }
    
    global void finish(Database.BatchableContext BC) {

        String jobName = 'CheckDropboxJobStatusBatch_' + System.currentTimeMillis();
        
        // Schedule the job to run in 15 minutes 
        DateTime futureTime = System.now().addMinutes(15);
        // Construct the cron expression for 15 minutes from now
        String cronExpression = '0 ' + futureTime.minute() + ' ' + futureTime.hour() + ' ' + futureTime.day() + ' ' + futureTime.month() + ' ? ' + futureTime.year();
        
        System.schedule(jobName, cronExpression, new CheckDropboxJobStatusBatch());
    }

}