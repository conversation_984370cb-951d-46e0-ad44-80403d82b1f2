@SuppressWarnings('PMD')
global class BankTransactionBatchProcessor implements Schedulable, Database.AllowsCallouts {

    private static final Integer BATCH_SIZE = 20;
    private static final Integer DELAY_SECONDS = 75; 

    private Id bankAccountId;
    private String filterType;
    private Id requestingUserId;

    public BankTransactionBatchProcessor(Id bankAccountId, String filterType, Id userId) {
        this.bankAccountId = bankAccountId;
        this.filterType = filterType;
        this.requestingUserId = userId;
    }

    global void execute(SchedulableContext sc) {
       
        if (isAlreadyRunningOrScheduled()) {
            System.debug('Processor for Bank Account ' + bankAccountId + ' is already scheduled or running. Skipping.');
            return;
        }

        processSingleBatch();
    }

    private void processSingleBatch() {
        Id lastProcessedId = getLastProcessedId();
        System.debug('Processing batch for Bank Account: ' + bankAccountId + ' starting after ID: ' + lastProcessedId);

        List<Bank_Transaction__c> transactionsToProcess = fetchTransactions(lastProcessedId);

        if (transactionsToProcess.isEmpty()) {
            System.debug('No more transactions found for Bank Account: ' + bankAccountId + '. Completing process.');
            handleCompletion();
            return;
        }

        try {
            System.enqueueJob(new BankTransactionCalloutQueueable(transactionsToProcess));

            Id lastIdInBatch = transactionsToProcess[transactionsToProcess.size() - 1].Id;
            updateLastProcessedId(lastIdInBatch);

            if (hasMoreRecords(lastIdInBatch)) {
                scheduleNextBatch();
            } else {
                handleCompletion();
            }

        } catch (Exception e) {
            sendErrorEmail(e);
        }
    }

    
    private class BankTransactionCalloutQueueable implements Queueable, Database.AllowsCallouts {
        private List<Bank_Transaction__c> transactionsToProcess;

        public BankTransactionCalloutQueueable(List<Bank_Transaction__c> transactions) {
            this.transactionsToProcess = transactions;
        }

        public void execute(QueueableContext context) {
            FetchCategoriesByOpenAIHandler.categorizeTransactions(transactionsToProcess);
            System.debug('Callout successful for batch.');
        }
    }

    // --- Fetch Transactions ---
    private List<Bank_Transaction__c> fetchTransactions(Id lastProcessedId) {
        String query = 'SELECT Id, Description__c, Debit__c, Credit__c, AI_Category__c, Category__c, Synced_Status__c, MF_Category__c FROM Bank_Transaction__c WHERE Bank_Account__c = :bankAccountId';

        if (filterType != null && filterType != 'All') {
            if (filterType == 'Uncategorized') query += ' AND (AI_Category__c = NULL OR AI_Category__c = \'\')';
            else if (filterType == 'NoFlinksCategory') query += ' AND (Category__c = NULL OR Category__c = \'\')';
            else if (filterType == 'NoMFCategory') query += ' AND (MF_Category__c = NULL OR MF_Category__c = \'\')';
        }

        if (lastProcessedId != null) {
            query += ' AND Id > :lastProcessedId';
        }

        query += ' ORDER BY Id ASC LIMIT ' + BATCH_SIZE;

        System.debug('Fetching transactions with query: ' + query);
        return Database.query(query);
    }

    private Boolean hasMoreRecords(Id currentLastProcessedId) {
        String checkQuery = 'SELECT Id FROM Bank_Transaction__c WHERE Bank_Account__c = :bankAccountId';

        if (filterType != null && filterType != 'All') {
            if (filterType == 'Uncategorized') checkQuery += ' AND (AI_Category__c = NULL OR AI_Category__c = \'\')';
            else if (filterType == 'NoFlinksCategory') checkQuery += ' AND (Category__c = NULL OR Category__c = \'\')';
            else if (filterType == 'NoMFCategory') checkQuery += ' AND (MF_Category__c = NULL OR MF_Category__c = \'\')';
        }

        if (currentLastProcessedId != null) {
            checkQuery += ' AND Id > :currentLastProcessedId';
        }

        checkQuery += ' ORDER BY Id ASC LIMIT 1';

        return !Database.query(checkQuery).isEmpty();
    }

    private void scheduleNextBatch() {
        Datetime nextRunTime = System.now().addSeconds(DELAY_SECONDS);
        String cronExpression = formatCron(nextRunTime);
        String jobName = 'BankTxnProc_' + this.bankAccountId + '_' + nextRunTime.getTime();
        System.schedule(jobName, cronExpression, new BankTransactionBatchProcessor(this.bankAccountId, this.filterType, this.requestingUserId));
        System.debug('Scheduled next batch job: ' + jobName + ' to run at: ' + nextRunTime);
    }

    // --- State Management ---
    private Id getLastProcessedId() {
        List<LastProcessedRecord__c> records = [SELECT LastProcessedId__c FROM LastProcessedRecord__c WHERE BankAccountId__c = :bankAccountId LIMIT 1];
        return records.isEmpty() ? null : records[0].LastProcessedId__c;
    }

    private void updateLastProcessedId(Id lastId) {
        List<LastProcessedRecord__c> existingRecords = [SELECT Id, LastProcessedId__c FROM LastProcessedRecord__c WHERE BankAccountId__c = :bankAccountId LIMIT 1];

        if (!existingRecords.isEmpty()) {
            existingRecords[0].LastProcessedId__c = lastId;
            update existingRecords[0];
        } else {
            insert new LastProcessedRecord__c(BankAccountId__c = this.bankAccountId, LastProcessedId__c = lastId, Name = 'State for ' + this.bankAccountId);
        }
    }

    private void deleteLastProcessedRecord() {
        List<LastProcessedRecord__c> recordsToDelete = [SELECT Id FROM LastProcessedRecord__c WHERE BankAccountId__c = :bankAccountId];
        if (!recordsToDelete.isEmpty()) delete recordsToDelete;
    }

    private void handleCompletion() {
        deleteLastProcessedRecord();
        sendCompletionEmail();
        deleteScheduledJobs();  
    }

    private void sendCompletionEmail() {
        System.debug('Sending completion email to user: ' + requestingUserId);
        // Email sending logic...
    }

    private void sendErrorEmail(Exception error) {
        System.debug('Sending error email to user: ' + requestingUserId);
        // Email sending logic...
    }

    public static String formatCron(Datetime dt) {
        return String.format('{0} {1} {2} {3} {4} ? {5}', new String[]{
            String.valueOf(dt.second()), String.valueOf(dt.minute()), String.valueOf(dt.hour()),
            String.valueOf(dt.day()), String.valueOf(dt.month()), String.valueOf(dt.year())
        });
    }

    private Boolean isAlreadyRunningOrScheduled() {
        String jobNamePrefix = 'BankTxnProc_' + this.bankAccountId + '_%';
        return ![
            SELECT Id FROM CronTrigger
            WHERE CronJobDetail.Name LIKE :jobNamePrefix
            AND State IN ('WAITING', 'QUEUED', 'PROCESSING', 'ACQUIRED')
            LIMIT 1
        ].isEmpty();
    }
    
    private void deleteScheduledJobs() {
        List<CronTrigger> jobs = [SELECT Id FROM CronTrigger WHERE CronJobDetail.Name LIKE :('BankTxnProc_' + this.bankAccountId + '_%')];
        if (!jobs.isEmpty()) {
            for (CronTrigger job : jobs) {
                System.abortJob(job.Id);
            }
            System.debug('Deleted all scheduled jobs for Bank Account: ' + bankAccountId);
        }
    }
}