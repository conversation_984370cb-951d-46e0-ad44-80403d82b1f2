@isTest
public class ContentDocumentLinkTriggerHandlerTest {
    
    @testSetup
    static void setupTestData() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity',
            StageName = 'Prospecting',
            CloseDate = Date.today(),
            AccountId = testAccount.Id
        );
        insert testOpportunity;

        Project__c testProject = new Project__c(
            Name = 'Test Project',
            Account_Name__c = testAccount.Id,
            Loan_Opportunity__c = testOpportunity.Id
        );
        insert testProject;

        Disbursement_Request__c testDisbursement = new Disbursement_Request__c(
            Project_lookup__c = testProject.Id
        );
        insert testDisbursement;
    }

    @isTest
    static void testContentDocumentLinkTrigger() {
        ContentVersion contentVersion = new ContentVersion(
            Title = 'Test File',
            PathOnClient = 'Test_File.pdf',
            VersionData = Blob.valueOf('Test content')
        );
        insert contentVersion;

        Id contentDocumentId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id LIMIT 1].ContentDocumentId;

        Opportunity testOpportunity = [SELECT Id FROM Opportunity LIMIT 1];
        ContentDocumentLink contentDocLink = new ContentDocumentLink(
            LinkedEntityId = testOpportunity.Id,
            ContentDocumentId = contentDocumentId,
            ShareType = 'I'
        );

        Test.startTest();
        insert contentDocLink;
        Test.stopTest();
    }

    @isTest
    static void testContentDocumentLinkTriggerInvalidFileType() {
        ContentVersion contentVersion = new ContentVersion(
            Title = 'Invalid File',
            PathOnClient = 'Invalid_File.pdf',
            VersionData = Blob.valueOf('Invalid content')
        );
        insert contentVersion;

        Id contentDocumentId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id LIMIT 1].ContentDocumentId;

        Opportunity testOpportunity = [SELECT Id FROM Opportunity LIMIT 1];
        ContentDocumentLink contentDocLink = new ContentDocumentLink(
            LinkedEntityId = testOpportunity.Id,
            ContentDocumentId = contentDocumentId,
            ShareType = 'I'
        );

        Test.startTest();
        insert contentDocLink;
        Test.stopTest();
    }
}