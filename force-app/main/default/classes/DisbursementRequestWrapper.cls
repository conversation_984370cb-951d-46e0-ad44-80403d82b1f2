public class DisbursementRequestWrapper {
    
    public DisbursementRequest disbursementRequest;
    public List<ItemDescription> itemDescriptions;
    public UploadDocuments uploadDocuments;
    public VerifyAndSubmit verifyAndSubmit;

    public DisbursementRequestWrapper() {
        disbursementRequest = new DisbursementRequest();
        itemDescriptions = new List<ItemDescription>();
        uploadDocuments = new UploadDocuments();
        verifyAndSubmit = new VerifyAndSubmit();
    }

    public class DisbursementRequest {
        public String loanNumberLookup;
        public String expenseType;
        public String projectLookup;
        public String payeeName;
        
        public PayeeContactName payeeContactName;
        
        public String payeeContactEmail;
        public Decimal Disbursement_Number;
        public String paymentMethod;
        
        public PayeeAddress payeeAddress;
        
        public String payeePhone;
        public String Mail_Check_To;
        public String Account_Name;
        public String Bank_Routing_Number;
        public String Bank_Account_Number;
        public String Bank_Name;
        
        public String payeeId;
    }
    
    public class PayeeContactName {
        public String firstName;
        public String lastName;
    }
    
    public class PayeeAddress {
        public String street;
        public string city;
        public string state;
        public string country;
        public string postalCode;
    }

    public class ItemDescription {
        public String Item;
        public String Description_Work;
        public Date invoiceDate;
        public String invoiceAmount;
        public String invoice;
        public Date invoiceDueDate;
    }

    public class UploadDocuments {
        public String file;
    }

    public class VerifyAndSubmit {
        public String Name;
        public String ClientLookup;
        public String ClientLookup2;
        public String PROJECTOWNER_Lookup;
        public String ProjNameLookup;
        public Date submitDate1;
        public String signature;
        public String requesterEmail;
        public String additionalCommentsSpecialInstructions;
    }
}