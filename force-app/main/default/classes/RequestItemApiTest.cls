@isTest
public class RequestItemApiTest {

    @testSetup
    static void setupTestData() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        
        Opportunity opp1 = new Opportunity(Name = 'Test Opportunity 1', StageName = 'Prospecting', CloseDate = System.today().addDays(10), AccountId = testAccount.Id, Amount = 10000);
        Opportunity opp2 = new Opportunity(Name = 'Test Opportunity 2', StageName = 'Closed Won', CloseDate = System.today().addDays(5), AccountId = testAccount.Id, Amount = 20000);
        insert new List<Opportunity> { opp1, opp2 };
            
        Project__c proj = new Project__c(Name = '43546', MF_Loan_Amount__c = 3243, Date_to_Funded__c = System.today(), Loan_Opportunity__c = opp1.Id);
        insert proj;
        
        Disbursement_Request__c dr = new Disbursement_Request__c(Disbursement__c = 344);
        insert dr;
        
        Requested_Item__c reqItem = new Requested_Item__c(name = 'test req', Disbursement_Request__c = dr.Id);
        insert reqItem;
    }

    @isTest
    static void testWithOpportunityId() {
        Disbursement_Request__c disReq = [SELECT Id FROM Disbursement_Request__c WHERE Disbursement__c = 344 LIMIT 1];

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/opportunity/' + disReq.Id;
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = new RestResponse();

        Test.startTest();
        RequestItemAPI.getrequestItem();
        Test.stopTest();
    }

    @isTest
    static void testWithProjectId() {
        Requested_Item__c reqItem = [SELECT Id FROM Requested_Item__c WHERE Name = 'test req' LIMIT 1];

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/opportunity/' + reqItem.Id;
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = new RestResponse();

        Test.startTest();
        RequestItemAPI.getrequestItem();
        Test.stopTest();
    }
}