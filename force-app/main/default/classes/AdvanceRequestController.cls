@SuppressWarnings('PMD')
public without sharing class AdvanceRequestController {
    
    public String agreementDate { get; private set; }
    public String borrowerName { get; private set; }
    public String advanceDate { get; private set; }
    public String advanceAmount { get; private set; }
    public String purpose { get; private set; }
    public String borrowerEntityName { get; private set; }
    public String signatoryName { get; private set; }
    public String signatoryTitle { get; private set; }
    public String signatureBase64 { get; private set; }
    public String contentDownloadUrl { get; private set; }
    public String dynamicHtmlContent { get; private set; }
    
    public AdvanceRequestController() {
        Map<String, String> params = ApexPages.currentPage().getParameters();
        agreementDate = params.get('agreementDate');
        borrowerName = params.get('borrowerName');
        advanceDate = params.get('advanceDate');
        advanceAmount = params.get('advanceAmount');
        purpose = params.get('purpose');
        borrowerEntityName = params.get('borrowerEntityName');
        signatoryName = params.get('signatoryName');
        signatoryTitle = params.get('signatoryTitle');
        contentDownloadUrl = params.get('contentDownloadUrlParam');
        System.debug('--- Controller Constructor (Dynamic HTML) ---');
        System.debug('Received contentDownloadUrlParam: ' + contentDownloadUrl);
        Id signatureCvId = null;
        if (String.isNotBlank(contentDownloadUrl) && contentDownloadUrl.contains('?')) {
            String queryString = contentDownloadUrl.substringAfter('?');
            for (String pair : queryString.split('&')) {
                if (String.isNotBlank(pair) && pair.contains('=')) {
                    List<String> parts = pair.split('=', 2);
                    String key = parts[0]; String value = parts[1];
                    if (key == 'ids') {
                        try { signatureCvId = (Id)value; } catch (Exception e) { signatureCvId = null; }
                        System.debug('Extracted ContentVersionId (ids): ' + signatureCvId);
                        break;
                    }
                }
            }
            if(signatureCvId == null) { System.debug('Could not find "ids" parameter.'); }
        } else { System.debug('contentDownloadUrlParam parameter is blank or no query string.'); }
        if (signatureCvId != null) {
            try {
                ContentVersion cv = [SELECT VersionData FROM ContentVersion WHERE Id = :signatureCvId WITH SYSTEM_MODE LIMIT 1];
                signatureBase64 = EncodingUtil.base64Encode(cv.VersionData);
                System.debug('Successfully queried ContentVersion and generated Base64.');
            } catch (Exception e) {
                System.debug('Error querying ContentVersion ID ' + signatureCvId + ': ' + e.getMessage());
                signatureBase64 = null;
            }
        } else { System.debug('Cannot query ContentVersion, signatureCvId is null.'); signatureBase64 = null; }
        System.debug('signatureBase64 populated: ' + (signatureBase64 != null));
        dynamicHtmlContent = buildHtmlDocument();
        System.debug('-------------------------------------------');
    }
    
    private String buildHtmlDocument() {

        String html = '';
        String todayDateStr = Date.today().format();
        
        html += '<!DOCTYPE html>';
        html += '<html><head>';
        html += '<style type="text/css" media="print">';
        html += '@page { margin: 1in; }';
        html += 'body { font-family: Arial, sans-serif; font-size: 10pt; line-height: 1.4; }';
        html += '.address-block { margin-bottom: 20px; }';
        html += '.section { margin-bottom: 15px; text-align: justify; }';
        html += '.value { font-weight: bold; padding: 0 3px; display: inline-block; text-align: left; border-bottom: none; }';
        html += '.value-long { padding: 0 3px; font-weight: bold; display: inline-block; text-align: left; }';
        html += '.signature-block { margin-top: 40px; }';
        html += '.signature-line { border-bottom: 1px solid black; width: 250px; margin-bottom: 5px; display: block; margin-top: 10px; }'; // Adjusted width
        html += '.request-details { margin-left: 20px; width: 100%; border-collapse: collapse; }'; // Added collapse
        html += '.request-details td { padding-bottom: 10px; vertical-align: top; }';
        html += '.request-details .number { padding-right: 10px; width: 20px; }';
        html += '</style>';
        html += '</head><body>';
        
        // Body Content
        html += '<div class="section">Date: <span class="value">' + todayDateStr + '</span></div>';
        html += '<div class="address-block">To: MOBILIZATION FUNDING II, LLC, Lender<br/>Ladies and Gentlemen:</div>';
        
        // Use helper to safely get property values, handling nulls
        html += '<div class="section">';
        html += 'Reference is made to that certain Loan Agreement, dated as of <span class="value">' + safeValue(agreementDate) + '</span> ';
        html += '(as amended, restated, extended, supplemented or otherwise modified in writing from time to time, the "Agreement;" the terms defined therein being used herein as therein defined), between ';
        html += '<span class="value">' + safeValue(borrowerName) + '</span> (the "Borrower"), and Lender. Capitalized terms used but not defined in this Advance Request ';
        html += 'and defined in the Agreement, will have the same meaning assigned to such terms in the Agreement.';
        html += '</div>';
        
        html += '<div class="section">Borrower hereby makes the following Advance Request:</div>';
        
        html += '<table class="request-details">';
        html += '<tr><td class="number">1.</td><td>On <span class="value">' + safeValue(advanceDate) + '</span> (a Business Day);</td></tr>';
        html += '<tr><td class="number">2.</td><td>An Advance in the amount of $<span class="value">' + safeValue(advanceAmount) + '</span> and</td></tr>';
        html += '<tr><td class="number">3.</td><td>The proceeds of which shall be used for the following purposes <span class="value value-long">' + safeValue(purpose) + '</span> ';
        html += 'in connection with Borrower\'s business and in support thereof, Borrower has attached the following supporting documentation and information which are true and correct in all respects.</td></tr>';
        html += '</table>';
        
        html += '<div class="section">4. Borrower hereby represents and warrants that the conditions specified in Section 2 of the Agreement shall be satisfied on and as of the date of such Advance.</div>';
        html += '<div class="section">5. The representations and warranties of the Borrower contained in the Loan Agreement and all representations and warranties of any Borrower that are contained in any document furnished at any time under or in connection with the Loan Documents, are true and correct on and as of the date hereof.</div>';
        html += '<div class="section">6. As an inducement to Lender to make an Advance in connection with this Advance Request, Borrower on behalf of itself and each Guarantor hereby acknowledges that the provisions of Section 8(g) of the Loan Agreement shall be effective as of the date hereof in favor of Lender and the provisions of such Section are hereby incorporated hereby this reference.</div>';
        
        // Signature Block with Base64 Image
        html += '<div class="signature-block">';
        if (String.isNotBlank(signatureBase64)) {
            html += '<p><img src="' + contentDownloadUrl + '" alt="Signature" width="250" height=auto;/></p>';
        } else {
            html += '[Signature Placeholder]<br/>';
        }
        html += 'By: <div class="signature-line" style="margin-top:0; margin-bottom:5px; width: 200px;"></div>';
        html += 'Name: <span class="value">' + safeValue(signatoryName) + '</span><br/>';
        html += 'Title: <span class="value">' + safeValue(signatoryTitle) + '</span>';
        html += '</div>'; // End signature-block
        
        // End HTML
        html += '</body></html>';        
        return html;
    }
    
    private String safeValue(String input) {
        return String.isBlank(input) ? '' : input.escapeHtml4();
    }
    
    
    // --- Invocable Method (Updated) ---
    @InvocableMethod(label='Generate Advance Request PDF and Attach to Record asynchronously')
    public static void generateAndAttachPDF(List<PDFRequest> requests) {
        System.debug('generateAndAttachPDF Invocable method started. Processing ' + requests.size() + ' requests.');
        
        for (PDFRequest request : requests) {
            // Basic validation
            if (String.isBlank(request.recordId)) {
                System.debug('Skipping request due to missing recordId.');
                continue;
            }
            
            System.debug('Preparing Queueable job for Record ID: ' + request.recordId);
            System.debug('Content Download URL to pass: ' + request.contentDownloadUrl);
            
            // Create an instance of the Queueable class with necessary data
            ProcessPdfGenerationQueueable job = new ProcessPdfGenerationQueueable(
                request.recordId,
                request.agreementDate,
                request.borrowerName,
                request.advanceDate,
                request.advanceAmount,
                request.purpose,
                request.borrowerEntityName,
                request.signatoryName,
                request.signatoryTitle,
                request.contentDownloadUrl
                // Pass UserInfo.getUserId() here if needed by Queueable for permissions
                // UserInfo.getUserId()
            );
            
            // Enqueue the job for asynchronous processing

                System.enqueueJob(job);
                System.debug('Enqueued ProcessPdfGenerationQueueable job for Record ID: ' + request.recordId);
            
        }
        System.debug('generateAndAttachPDF Invocable method finished launching Queueable jobs.');
    }
    
    // --- Inner Class PDFRequest (Unchanged, unless originalUserId was added) ---
    public class PDFRequest {
        @InvocableVariable(required=true) public String recordId;
        @InvocableVariable public String agreementDate;
        @InvocableVariable public String borrowerName;
        @InvocableVariable public String advanceDate;
        @InvocableVariable public String advanceAmount;
        @InvocableVariable public String purpose;
        @InvocableVariable public String borrowerEntityName;
        @InvocableVariable public String signatoryName;
        @InvocableVariable public String signatoryTitle;
        @InvocableVariable(label='Content Download URL')
        public String contentDownloadUrl;
        // @InvocableVariable public Id originalUserId; // Remove if not passing to Queueable
    }
}