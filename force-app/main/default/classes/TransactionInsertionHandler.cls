@SuppressWarnings('PMD')
public class TransactionInsertionHandler {

    public static void setRunTransactionFlowOnInsert(List<Transaction__c> newTxns) {
        Set<Id> projectIds = new Set<Id>();
        Map<Id, List<Transaction__c>> projectToNewTxns = new Map<Id, List<Transaction__c>>();

        for (Transaction__c txn : newTxns) {
            if (txn.Project__c != null) {
                projectIds.add(txn.Project__c);
                if (!projectToNewTxns.containsKey(txn.Project__c)) {
                    projectToNewTxns.put(txn.Project__c, new List<Transaction__c>());
                }
                projectToNewTxns.get(txn.Project__c).add(txn);
            }
        }

        if (projectIds.isEmpty()) {
            return; // nothing to do
        }

        // Find projects that already have a flagged transaction
        Set<Id> projectsWithFlaggedTxn = new Set<Id>();
        for (AggregateResult ar : [
            SELECT Project__c proj
            FROM Transaction__c
            WHERE Project__c IN :projectIds
			AND Run_Transactions_Flow__c = false
            AND Transaction_Date__c != null
            GROUP BY Project__c
        ]) {
            projectsWithFlaggedTxn.add((Id)ar.get('proj'));
        }

        // For projects without a flagged txn, set Run_Transactions_Flow__c = true on *any one* new transaction
        for (Id projId : projectToNewTxns.keySet()) {
            if (projectsWithFlaggedTxn.contains(projId)) {
                continue; // already flagged, skip
            }

            // Get any one transaction from new batch for this project
            List<Transaction__c> txns = projectToNewTxns.get(projId);
            if (!txns.isEmpty()) {
                txns[0].Run_Transactions_Flow__c = true; // set flag on the first one found
            }
        }
    }
}