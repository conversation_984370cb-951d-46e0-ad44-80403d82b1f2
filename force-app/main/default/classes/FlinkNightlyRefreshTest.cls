@isTest
public class FlinkNightlyRefreshTest {

    @isTest
    static void testFlinkNightlyRefresh() {
        // Step 1: Set up mock HTTP response
        Test.setMock(HttpCalloutMock.class, new FlinkNightlyRefreshMock());

        // Step 2: Create a Flinks Configuration record
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Base_Url__c = 'https://api.test.com/',
            Customer_Id__c = '12345',
            Bearer_Token__c = 'testBearerToken'
        );
        insert config;

        // Step 3: Create sample Bank Account records
        Bank_Account__c account1 = new Bank_Account__c(Login_ID__c = 'Login1', Is_Active__c = true);
        //Bank_Account__c account2 = new Bank_Account__c(Login_ID__c = 'Login2', Is_Active__c = true);
        insert new List<Bank_Account__c>{ account1 };

        // Step 4: Call the method under test
        Test.startTest();
        FlinkNightlyRefresh.getIneligibleCards();
        Test.stopTest();

        // Step 5: Assert the updates to Bank Account records
        Bank_Account__c updatedAccount1 = [SELECT Is_Active__c, Connection_Error_Code__c, Connection_Error_Message__c FROM Bank_Account__c WHERE Id = :account1.Id];

        System.assertEquals(false, updatedAccount1.Is_Active__c, 'Account 1 should be inactive');
        System.assertEquals('INVALID_PASSWORD', updatedAccount1.Connection_Error_Code__c, 'Account 1 should have the correct error code');
        System.assertEquals('The password provided was different from what the bank expected', updatedAccount1.Connection_Error_Message__c, 'Account 1 should have the correct error message');
	}

    // Mock HTTP Callout
    private class FlinkNightlyRefreshMock implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setStatusCode(200);
            res.setBody('{"IneligibleCards":[{"LoginId":"Login1","LastRefreshErrorCode":"INVALID_PASSWORD"},{"LoginId":"Login2","LastRefreshErrorCode":"INVALID_USERNAME"}]}');
            return res;
        }
    }
}