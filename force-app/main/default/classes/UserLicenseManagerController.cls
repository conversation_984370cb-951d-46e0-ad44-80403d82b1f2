@SuppressWarnings('PMD')
public without sharing class UserLicenseManagerController {
    @AuraEnabled(cacheable=true)
    public static List<UserLicense> totalUserData() {
        List<UserLicense> uls = [SELECT TotalLicenses, Status, UsedLicenses, Name FROM UserLicense Where Name = 'Customer Community Login' Or Name = 'Customer Community'];
        return uls;
    }

    @AuraEnabled(cacheable=true)
    public static List<UserLicenseWrapper> getUserLicenses() {
        List<UserLicenseWrapper> userLicenses = new List<UserLicenseWrapper>();
        List<UserLicenseWrapper> loginUsers = new List<UserLicenseWrapper>();
        List<UserLicenseWrapper> nonLoginUsers = new List<UserLicenseWrapper>();

        List<User> users = [
            SELECT Id, Name, Account.Name, Account.MF_Assigned_Servicer__r.Name, UserName, LastLoginDate, Last_Funded_Opportunity_Date__c, Profile.Name, AccountId, isActive
            FROM User
            WHERE Profile.Name like '%Community%' AND IsActive = TRUE
            ORDER BY LastLoginDate DESC  NULLS LAST LIMIT 50000
            //AND Recent_Login_Date__c >= LAST_N_DAYS:90
            //AND Last_Funded_Opportunity_Date__c >= LAST_N_DAYS:180
        ];

        Set<Id> accountIds = new Set<Id>();
        for (User u : users) {
            if (u.AccountId != null) {
                accountIds.add(u.AccountId);
            }
        }

        Map<Id, Opportunity> accountOpportunities = new Map<Id, Opportunity>();
        if (!accountIds.isEmpty()) {
            for (Opportunity opp : [
                SELECT Id, Name, CloseDate, AccountId 
                FROM Opportunity 
                WHERE AccountId IN :accountIds AND StageName = 'Closed Won' AND Status_Update_for_Client__c = 'Approved' 
                ORDER BY CloseDate DESC
            ]) {
                accountOpportunities.put(opp.AccountId, opp);
            }
        }

        Map<Id, Integer> userLoginCounts = new Map<Id, Integer>();
        for (AggregateResult result : [
            SELECT UserId, COUNT(Id) numberLogins
            FROM LoginHistory
            WHERE UserId IN :users
            AND LoginTime >= LAST_N_DAYS:30
            GROUP BY UserId
        ]) {
            userLoginCounts.put((Id)result.get('UserId'), (Integer)result.get('numberLogins'));
        }

        for (User u : users) {
            Opportunity recentOpportunity = accountOpportunities.get(u.AccountId);
            Integer loginCount = userLoginCounts.get(u.Id) != null ? userLoginCounts.get(u.Id) : 0;

            List<Opportunity> mostRecentModifiedOppr= [
                SELECT LastModifiedDate, StageName
                FROM Opportunity 
                WHERE AccountId = :u.AccountId
                ORDER BY LastModifiedDate DESC
                LIMIT 1
            ];
            DateTime mostRecentModifiedDate = (mostRecentModifiedOppr.size() > 0) ? mostRecentModifiedOppr[0].LastModifiedDate : null;
            String mostRecentStageName = (mostRecentModifiedOppr.size() > 0) ? mostRecentModifiedOppr[0].StageName : null;
            Boolean promotable = false;
            if(!mostRecentModifiedOppr.isEmpty()){
                if (mostRecentStageName != null && !('Application, Initial Underwriting, Final Underwriting, Closed Lost').contains(mostRecentStageName)) {
                    promotable = true;
                }
            }

            // Create the UserLicenseWrapper for the current user
            UserLicenseWrapper wrapper = new UserLicenseWrapper(
                u.Id, u.Name, mostRecentModifiedDate, mostRecentStageName, promotable, u.LastLoginDate, u.Last_Funded_Opportunity_Date__c, u.Profile.Name, u.IsActive, loginCount, u.Account.MF_Assigned_Servicer__r.Name, u.Account.Name
            );

            // Add to different lists based on Profile.Name containing 'Login'
            if (u.Profile.Name.contains('Login')) {
                loginUsers.add(wrapper);
            } else {
                nonLoginUsers.add(wrapper);
            }
        }

        // Add the loginUsers list (normal order)
        userLicenses.addAll(loginUsers);

        // Reverse the nonLoginUsers manually
        for (Integer i = nonLoginUsers.size() - 1; i >= 0; i--) {
            userLicenses.add(nonLoginUsers[i]);
        }

        return userLicenses;
    }


    @AuraEnabled(cacheable=true)
    public static List<UserProfileChangeLogWrapper> getUserProfileChangeLogs() {
        System.debug('in getUserProfileChangeLogs ');
        List<UserProfileChangeLogWrapper> changeLogs = new List<UserProfileChangeLogWrapper>();

        // Query for User Profile Change Log records
        List<User_Profile_Change_Log__c> logs = [
            SELECT Id, UserId__r.Name, UserId__r.Account.Name, UserId__r.Account.MF_Assigned_Servicer__r.Name, OldProfileName__c, NewProfileName__c, ChangeDate__c, isActive__c 
            FROM User_Profile_Change_Log__c
            WHERE ChangeDate__c >= LAST_N_DAYS:15
            ORDER BY ChangeDate__c DESC
        ];
        System.debug('logs '+logs);

        for (User_Profile_Change_Log__c log : logs) {
            changeLogs.add(new UserProfileChangeLogWrapper(log.Id, log.UserId__r.Name, log.OldProfileName__c, log.NewProfileName__c, log.ChangeDate__c, log.isActive__c, log.UserId__r.Account.MF_Assigned_Servicer__r.Name, log.UserId__r.Account.Name));
        }

        System.debug('changeLogs '+changeLogs);
        return changeLogs;
    }

    // Wrapper class for User Profile Change Log
    public class UserProfileChangeLogWrapper {
        @AuraEnabled public Id logId;
        @AuraEnabled public String userName;
        @AuraEnabled public String oldProfile;
        @AuraEnabled public String newProfile;
        @AuraEnabled public DateTime changeDate;
        @AuraEnabled public Boolean isActive;
        @AuraEnabled public String assignedServicerName;
        @AuraEnabled public String accountName;

        public UserProfileChangeLogWrapper(Id logId, String userName, String oldProfile, String newProfile, DateTime changeDate, Boolean isActive, String assignedServicerName, String accountName) {
            this.logId = logId;
            this.userName = userName;
            this.oldProfile = oldProfile;
            this.newProfile = newProfile;
            this.changeDate = changeDate;
            this.isActive = isActive;
            this.assignedServicerName = assignedServicerName;
            this.accountName = accountName;
        }
    }

    public class UserLicenseWrapper {
        @AuraEnabled public Id userId;
        @AuraEnabled public String userName;
        @AuraEnabled public DateTime mostRecentModifiedDate;
        @AuraEnabled public String mostRecentStageName;
        @AuraEnabled public Boolean promotable;
        @AuraEnabled public DateTime lastLoginDate;
        @AuraEnabled public DateTime recentLoginDate;
        @AuraEnabled public DateTime lastFundedOpportunityDate;
        @AuraEnabled public String profileName;
        @AuraEnabled public Boolean isActive;
        @AuraEnabled public Integer loginCount;
        @AuraEnabled public String assignedServicerName;
        @AuraEnabled public String accountName;

        public UserLicenseWrapper(Id userId, String userName, DateTime mostRecentModifiedDate, String mostRecentStageName, Boolean promotable, DateTime lastLoginDate, DateTime lastFundedOpportunityDate, String profileName, Boolean isActive, Integer loginCount, String assignedServicerName, String accountName) {
            this.userId = userId;
            this.userName = userName;
            this.mostRecentModifiedDate = mostRecentModifiedDate;
            this.mostRecentStageName = mostRecentStageName;
            this.promotable = promotable;
            this.lastLoginDate = lastLoginDate;
            //this.recentLoginDate = recentLoginDate;
            this.lastFundedOpportunityDate = lastFundedOpportunityDate;
            this.profileName = profileName;
            this.isActive = isActive;
            this.loginCount = loginCount;
            this.assignedServicerName = assignedServicerName;
            this.accountName = accountName;
        }
    }
}