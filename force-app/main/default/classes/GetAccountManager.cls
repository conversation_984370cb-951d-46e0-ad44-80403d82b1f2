@SuppressWarnings('PMD')
@RestResource(urlMapping='/GetAccountManager/*')
global without sharing class GetAccountManager {

    @HttpGet
    global static void getAccountDetailsAPI() {
		RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        String accountId = req.requestURI.substring(req.requestURI.lastIndexOf('/') + 1);
        
        Map<String, Object> resultMap = new Map<String, Object>();

        try {
			if (!String.isBlank(accountId)) {
				List<Opportunity> opportunities = [
					SELECT Id, Name, StageName, Date_to_Funded__c, CreatedDate, 
							Assigned_MF_CRM__c, Assigned_MF_Servicer__c, Assigned_MF_UW__c, Assigned_MF_Servicer__r.FirstName,Assigned_MF_Servicer__r.Email
					FROM Opportunity
					WHERE AccountId = :accountId
					AND StageName IN ('Ready to fund', 'Holding Pattern', 'Closed Won','Funded')
					ORDER BY Date_to_Funded__c NULLS LAST, CreatedDate ASC
					LIMIT 1
				];

				String userId;
				if(!opportunities.isEmpty() && opportunities[0].Assigned_MF_Servicer__c != null){
					Opportunity matchedOpportunity = opportunities[0];
					userId = matchedOpportunity.Assigned_MF_Servicer__c;
              	}
				else{
					// No matching opportunities, fetch details from Account
					System.debug('No matching opportunities found. Fetching from Account.');
					Account account = [
						SELECT Assigned_MF_CRM__c, MF_Assigned_Servicer__c, MF_Assigned_UW__c,MF_Assigned_UW__r.FirstName,MF_Assigned_Servicer__r.FirstName,Assigned_MF_CRM__r.FirstName,  MF_Assigned_UW__r.Email,MF_Assigned_Servicer__r.Email,Assigned_MF_CRM__r.Email
						FROM Account
						WHERE Id = :accountId
						LIMIT 1
					];

					if(!String.isBlank(account.Assigned_MF_CRM__c)) {
						userId = account.Assigned_MF_CRM__c;
					}
					else if(!String.isBlank(account.MF_Assigned_Servicer__c)) {
						userId = account.MF_Assigned_Servicer__c;
					}
					else if(!String.isBlank(account.MF_Assigned_UW__c)) {
						userId = account.MF_Assigned_UW__c;
					}
				}

				if(!String.isBlank(userId)) {
					User user = [SELECT Id, FirstName, username,LastName, Email, Phone FROM User WHERE Id = :userId LIMIT 1];
					/*Map<String, Object> userInfo = new Map<String, Object>();*/
                    resultMap.put('username', user.username);
                    resultMap.put('Id', user.Id);
                    resultMap.put('FirstName', user.FirstName);
                    resultMap.put('LastName', user.LastName);
                    resultMap.put('Email', user.Email);
                    resultMap.put('Phone', user.Phone);
				}else {
                    // Use default values if no userId is found
                    resultMap.put('Name', System.Label.Default_Account_Manager_Name);
                    resultMap.put('Email', System.Label.Default_Account_Manager_Email);
                }
				res.statusCode = 200;
                res.responseBody = Blob.valueOf(JSON.serialize(resultMap));
			}
			else {
                // Invalid AccountId
                res.statusCode = 400;
                res.responseBody = Blob.valueOf('Invalid AccountId provided');
            }
        } catch (Exception ex) {
            res.statusCode = 500;
            res.responseBody = Blob.valueOf('Error retrieving projects: ' + ex.getMessage());
        }
	}
}