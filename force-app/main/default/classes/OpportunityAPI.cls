@RestResource(urlMapping='/opportunity/*')
global without sharing class OpportunityAPI{

    @HttpGet 
    global static void getOpportunities() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        String accountId = req.requestURI.substring(req.requestURI.lastIndexOf('/') + 1);

        OpportunityResponseWrapper opportunityWrapper = new OpportunityResponseWrapper();

        try {
            List<Opportunity> opptys;

            if (!String.isBlank(accountId) && accountId.startsWith('001')) {
             
                opptys = [
                    SELECT Id, Name, Status_Update_for_Client__c, Loan_Amount_Requested__c,StageName, Amount, CloseDate, Assigned_MF_Servicer__r.FirstName,  Assigned_MF_Servicer__r.LastName, 
                    Assigned_MF_UW__r.FirstName, Assigned_MF_UW__r.LastName,
                    Assigned_MF_CRM__r.FirstName, Assigned_MF_CRM__r.LastName, Project__c, Project__r.Name,
                    Account.Name, Borrower_Name__c, Valley_Account_Open__c, Effective_Date_of_Loan_Docs__c,
                    (SELECT Id, Name, Date_Loan_Paid_Off__c, MF_Loan_Amount__c, Loan_Maturity_Date__c, 
                            Loan_Principal__c, Loan_Status__c, Project_Start_Date__c, Project_Name_Comment__c , Date_to_Funded__c, Date_Funded__c, Project_Number__c, 
                            Assigned_MF_Servicer__c, Assigned_MF_Servicer__r.Name, Account_Name__c, Account_Name__r.Name, Current_Pay_Off_Balance__c, Money_Available_to_Draw_on_the_Loan__c 
                        FROM Projects__r)
                    FROM Opportunity
                    WHERE AccountId = :accountId
                ];
                
                opportunityWrapper.totalSize = opptys.size();
            	opportunityWrapper.done = true;
                opportunityWrapper.records = opptys;

            	res.responseBody = Blob.valueOf(JSON.serialize(opportunityWrapper));
                
            } else {
                
                opptys = [
                    SELECT Id, Name, StageName, Amount, CloseDate, AccountId, App_Signature__c, Loan_Amount_Requested__c, Bad_Debt__c, Bankruptcy__c, Confirmation_Email__c, Current_Lawsuits__c, Status_Update_for_Client__c, UCC_Filings__c, of_active_contracts_POs__c, Signed_App__c ,
                    Assigned_MF_Servicer__r.FirstName,  Assigned_MF_Servicer__r.LastName, 
                    Assigned_MF_UW__r.FirstName, Assigned_MF_UW__r.LastName,
                    Assigned_MF_CRM__r.FirstName, Assigned_MF_CRM__r.LastName, Project__c, Project__r.Name,
                    Account.Name, Borrower_Name__c, Valley_Account_Open__c, Effective_Date_of_Loan_Docs__c,
                    (SELECT Id, Name, Date_Loan_Paid_Off__c, MF_Loan_Amount__c, Loan_Maturity_Date__c, 
                            Loan_Principal__c, Loan_Status__c, Project_Start_Date__c, Project_Name_Comment__c , Date_to_Funded__c, Date_Funded__c, Project_Number__c, 
                            Assigned_MF_Servicer__c, Assigned_MF_Servicer__r.Name, Account_Name__c, Account_Name__r.Name, Money_Available_to_Draw_on_the_Loan__c
                        FROM Projects__r)

                    From Opportunity Where Id= :accountId
                ];
                
            	res.responseBody = Blob.valueOf(JSON.serialize(opptys));
            }

        } catch (Exception ex) {
            res.statusCode = 500;
            res.responseBody = Blob.valueOf('Error retrieving projects: ' + ex.getMessage());
        }
    }

    public class OpportunityResponseWrapper {
        public Integer totalSize;
        public Boolean done;
        public List<Opportunity> records;

        public OpportunityResponseWrapper() {
            this.records = new List<Opportunity>();
        }
    }
}