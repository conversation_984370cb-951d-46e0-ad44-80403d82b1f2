@isTest
private class ContactOpportunitySelectorTest {
    @isTest
    static void testGetAccountDetails() {
        Account testAccount = new Account(
            Name = 'Test Account'
        );
        insert testAccount;

        Contact testContact = new Contact(
            LastName = 'Test Contact',
            AccountId = testAccount.Id
        );
        insert testContact;
        
        Opportunity opp = new Opportunity(
            Name = 'Test Opportunity',
            AccountId = testAccount.Id,
            StageName = 'Ready to fund',
            CloseDate = System.today()
        );
        insert opp;
        
        Project__c testProject = new Project__c(
            Name = 'Test Project',
            Loan_Opportunity__c = opp.Id 
        );
        insert testProject;
        
        Disbursement_Request__c dr = new Disbursement_Request__c(Project_lookup__c = testProject.Id);
        insert dr;

        Id profileId = [SELECT Id FROM Profile WHERE Name = 'Customer Community User' LIMIT 1].Id;
        User testUser = new User(
            Alias = 'tuser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'TestUser',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = profileId,
            Country = 'United States',
            TimeZoneSidKey = 'America/Los_Angeles',
            Username = '<EMAIL>',
            IsActive = true,
            ContactId = testContact.Id
        );
        insert testUser;

        System.runAs(testUser) {
            Map<String, String> result = ContactOpportunitySelector.getAccountDetails();
        }
    }
    
    @isTest
    static void testSelectOpportunity() {
        
        Id p = [select id from profile where name='Customer Community User']?.id;

		Account ac = new Account(name ='Grazitti') ;
		insert ac;

		Contact con = new Contact(LastName ='testCon',AccountId = ac.Id);
		insert con;

		User user = new User(alias = 'test123', email='<EMAIL>',
		emailencodingkey='UTF-8', lastname='Testing', languagelocalekey='en_US',
		localesidkey='en_US', profileid = p, country='United States',IsActive =true,
		ContactId = con.Id,
		timezonesidkey='America/Los_Angeles', username='<EMAIL>');

		insert user;
		

        System.runAs(user) {
    		
    		Map<String, String> resultMap = ContactOpportunitySelector.selectOpportunity();
    		
		}
        
        Opportunity unawardedOpp = new Opportunity(Name = 'Test Opportunity 2', AccountId = ac.Id, StageName = 'Funded', CloseDate = System.Today());
        insert unawardedOpp;

        System.runAs(user) {
            Map<String, String> resultMap = ContactOpportunitySelector.selectOpportunity();
           
        }

    }
}