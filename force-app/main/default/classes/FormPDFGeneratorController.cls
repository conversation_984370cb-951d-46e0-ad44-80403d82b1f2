@SuppressWarnings('PMD.ApexXSSFromURLParam')
public without sharing class FormPDFGeneratorController {
        public String formData{get; set;}
        public String contentDownloadUrl { get; set; }
        
        public FormPDFGeneratorController() {
            
            Opportunity opp = new Opportunity();
            String formDetails = '';
            
            String rawBody = ApexPages.currentPage().getParameters().get('emailBody');
            if (rawBody != null) {
                // Decode the URL-encoded HTML
                formDetails = EncodingUtil.urlDecode(rawBody, 'UTF-8');
            } else {
                formDetails = '';
            }
    
            
            String contentDownloadUrl2 = ApexPages.currentPage().getParameters().get('contentDownloadUrl');
            System.debug('Content Download URL: ' + contentDownloadUrl2);
            //formDetails = emailBody;
            
            if (formDetails.contains('</body>')) {
                formDetails = formDetails.replace('</body>', 
                                                  '<p>Signature Picture: <br/><img src="' + contentDownloadUrl2 + '" alt="Signature" width="250" height=auto;/></p></body></html>');
            } 
            
            
            
            formData = formDetails;
            System.debug('formDetails 104 '+formData);
            
        }
    }