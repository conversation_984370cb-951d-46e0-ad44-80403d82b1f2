@isTest
private class MFDynamoMappingServiceTest {

    @isTest
    static void testGetMappingRulesAsJson() {
        Test.startTest();
        String jsonResult = MFDynamoMappingService.getMappingRulesAsJson();
        Test.stopTest();

        System.assertNotEquals(null, jsonResult, 'JSON result should not be null');
        System.assert(!String.isBlank(jsonResult), 'JSON result should not be blank');

        try {
            List<cashFlowSessionController.MappingRule> rules =
                (List<cashFlowSessionController.MappingRule>) JSON.deserialize(
                    jsonResult, List<cashFlowSessionController.MappingRule>.class
                );
           // System.assertNotEquals(null, rules, 'Deserialized rules should not be null');
        } catch (Exception e) {
            System.assert(false, 'JSON deserialization failed: ' + e.getMessage());
        }
    }

    @isTest
    static void testGetMappingRulesForObject() {
        Test.startTest();
        List<MF_Dynamo_SF_Mapping__mdt> mappings = MFDynamoMappingService.getMappingRulesForObject('Account');
        Test.stopTest();

        System.assertNotEquals(null, mappings, 'Mapping list should not be null');
        for (MF_Dynamo_SF_Mapping__mdt rule : mappings) {
            System.assertEquals('Account', rule.Target_Salesforce_Object_API_Name__c,
                'Expected rule for Account object');
        }
    }

    @isTest
    static void testGetMappingRulesForIntakeType() {
        Test.startTest();
        List<MF_Dynamo_SF_Mapping__mdt> results = MFDynamoMappingService.getMappingRulesForIntakeType('Construction');
        Test.stopTest();

        System.assertNotEquals(null, results, 'Mapping rules should not be null');
        for (MF_Dynamo_SF_Mapping__mdt rule : results) {
            if (rule.Intake_Context_Type__c != null) {
                System.assert(rule.Intake_Context_Type__c.contains('Construction'),
                    'Expected intake type to contain Construction');
            }
        }
    }
}