@isTest
public class GetActivitiesTest {

    @isTest
    static void testDoGetWithRelatedId() {
        User testUser = [SELECT Id, AccountId FROM User WHERE Id = :UserInfo.getUserId()];

        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        Activity_Logger__c testActivity = new Activity_Logger__c(
            Item__c = 'Test Item',
            Account__c = testAccount.Id,
            Contact__c = null,
            Activity_Time__c = DateTime.now(),
            Related_Record__c = testAccount.Id,
            User__c = testUser.Id
        );
        insert testActivity;

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/GetActivities/';
        req.addParameter('relatedId', testAccount.Id);
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestResponse res = new RestResponse();
        RestContext.response = res;

        Test.startTest();
        GetActivities.ResponseWrapper response = GetActivities.doGet();
        Test.stopTest();
    }

    @isTest
    static void testDoGetWithoutRelatedId() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Email = '<EMAIL>',
            Username = 'testuser' + Math.random() + '@example.com',
            Alias = 'tuser',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User'].Id,
            LanguageLocaleKey = 'en_US'
        );
        insert testUser;

        System.runAs(testUser) {
            Activity_Logger__c testActivity = new Activity_Logger__c(
                Item__c = 'Test Item',
                Account__c = testAccount.Id,
                Contact__c = null,
                Activity_Time__c = DateTime.now(),
                Related_Record__c = null,
                User__c = testUser.Id
            );
            insert testActivity;

            RestRequest req = new RestRequest();
            req.requestURI = '/services/apexrest/GetActivities/';
            req.httpMethod = 'GET';
            RestContext.request = req;
            RestResponse res = new RestResponse();
            RestContext.response = res;

            Test.startTest();
            GetActivities.ResponseWrapper response = GetActivities.doGet();
            Test.stopTest();
        }
    }

    @isTest
    static void testDoGetErrorNoAccount() {
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/GetActivities/';
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestResponse res = new RestResponse();
        RestContext.response = res;

        Test.startTest();
        GetActivities.ResponseWrapper response = GetActivities.doGet();
        Test.stopTest();

    }
}