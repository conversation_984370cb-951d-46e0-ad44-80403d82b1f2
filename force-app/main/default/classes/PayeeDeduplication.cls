@SuppressWarnings('PMD')
public with sharing class PayeeDeduplication {
    @AuraEnabled
    public static void removeDuplicatePayees() {
        // Map to track the first record (to keep) by payee name
        Map<String, Payee_Information__c> uniquePayees = new Map<String, Payee_Information__c>();
        List<Payee_Information__c> duplicatesToDelete = new List<Payee_Information__c>();

        // Query all records (ordered by oldest first so we keep the first one)
        for (Payee_Information__c record : [
            SELECT Id, Payee_Name__c, CreatedDate, Project__r.Account_Name__c
            FROM Payee_Information__c 
            WHERE Payee_Name__c != null 
            ORDER BY CreatedDate ASC
        ]) {
            String name = record.Payee_Name__c.trim().toLowerCase(); // Normalize key
			String actName = record.Project__r.Account_Name__c;
            String uniqueName = name + '_' + actName;
            if (!uniquePayees.containsKey(uniqueName)) {
                uniquePayees.put(uniqueName, record); // Keep the first occurrence
            } else {
                duplicatesToDelete.add(record); // Everything else is a duplicate
            }
        }

        // Bulk delete
        System.debug('duplicatesToDelete -> ' + duplicatesToDelete.size());
        if (!duplicatesToDelete.isEmpty()) {
            delete duplicatesToDelete;
        }
    }
}