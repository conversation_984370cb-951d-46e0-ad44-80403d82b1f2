@SuppressWarnings('PMD')
@RestResource(urlMapping='/FlinksCallBack/*')
global without sharing class FlinksCallBack {

     @HttpGet
     global static string doGet() {
        System.debug('params-'+JSON.serialize(RestContext.request.params));
        System.debug('request-'+JSON.serialize(RestContext.request));
        System.debug('body-'+JSON.serialize(RestContext.request.requestbody));
         return 'Get Result';
     }
 
     @HttpPost
     global static String doPost() {
      try{
            System.debug('request-'+JSON.serialize(RestContext.request));
            System.debug('params-'+JSON.serialize(RestContext.request.params));
            System.debug('body-'+RestContext.request.requestbody.toString());

            Map<String,Object> reqMap = (Map<String,Object>)JSON.deserializeUntyped(RestContext.request.requestbody.toString());
            UIT_Utility.LogFlinksCallout(null,RestContext.request.requestbody.toString(), null, (String)reqMap.get('ResponseType'), null,(Integer)reqMap.get('HttpStatusCode'),null,false);

            // if(reqMap.get('Accounts') != null && reqMap.get('DocumentUploadInstitution') != null){
            if(reqMap.get('RequestId') != null){
               system.debug('aborted cron job');
               String reqId = 'FlinksAsyncCallout-'+(String)reqMap.get('RequestId');
               List<CronTrigger> cronJobs = new List<CronTrigger>();
               cronJobs = [SELECT Id, CronJobDetail.Name, CronJobDetail.Id,State FROM CronTrigger where CronJobDetail.Name =: reqId];
               system.debug('cronJobs='+cronJobs);
               if(!cronJobs.isEmpty()){
                  System.abortJob(cronJobs[0].Id);
               }
            }

            if(reqMap.get('Accounts') != null){
               FlinksAsyncCallout.parseSuccessResponse(reqMap);
            }
            return 'success';

         } catch (Exception e) {

            UIT_Utility.LogException(UserInfo.getUserId(),e,'FlinksCallBack.doPost');
            return 'Error- '+e.getMessage() + ' : ' + e.getCause();

         }
     }
}