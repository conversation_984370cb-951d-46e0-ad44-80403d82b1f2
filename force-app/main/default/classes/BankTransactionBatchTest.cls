@IsTest
public class BankTransactionBatchTest {
    @TestSetup
    static void setup() {
        // Create required Bank Account
        Bank_Account__c bankAccount = new Bank_Account__c(
            Name = '*********'
        );
        insert bankAccount;

        // Create test transactions with various categories
        List<Bank_Transaction__c> transactions = new List<Bank_Transaction__c>{
            new Bank_Transaction__c(
                Bank_Account__c = bankAccount.Id,
                Transaction_Id__c = 'TEST-1',
                Category__c = 'Education',
                Sub_Category__c = 'Student Loans',
                Transaction_Date__c = System.today(),
                Debit__c = 100.00
            ),
            new Bank_Transaction__c(
                Bank_Account__c = bankAccount.Id,
                Transaction_Id__c = 'TEST-2',
                Category__c = 'Education',
                Sub_Category__c = 'Tuition & School Fees',
                Transaction_Date__c = System.today(),
                Debit__c = 200.00
            ),
            new Bank_Transaction__c(
                Bank_Account__c = bankAccount.Id,
                Transaction_Id__c = 'TEST-3',
                Category__c = 'Invalid',
                Sub_Category__c = 'Invalid',
                Transaction_Date__c = System.today(),
                Debit__c = 300.00
            )
        };
        insert transactions;
    }

    @IsTest
    static void testBatchExecution() {
        Test.startTest();
        Database.executeBatch(new BankTransactionBatch(), 200);
        Test.stopTest();

        // Verify results
        List<Bank_Transaction__c> results = [
            SELECT Id, MF_Category__c 
            FROM Bank_Transaction__c 
            ORDER BY Transaction_Id__c
        ];

        System.assertEquals('Loan Proceeds', results[0].MF_Category__c, 
            'Should map Education/Student Loans to Loan Proceeds');
        //System.assertEquals('Other', results[1].MF_Category__c,
        //    'Should map Education/Tuition & School Fees to Other');
        //System.assertEquals(null, results[2].MF_Category__c,
        //    'Should leave non-matching categories as null');
    }

    @IsTest
    static void testBatchWithNoRecords() {
        // Delete existing records
        delete [SELECT Id FROM Bank_Transaction__c];

        Test.startTest();
        Database.executeBatch(new BankTransactionBatch(), 200);
        Test.stopTest();

        // Verify no errors occurred
        System.assertEquals(0, [SELECT COUNT() FROM Bank_Transaction__c],
            'No transactions should exist');
    }

    @IsTest
    static void testBatchWithLargeVolume() {
        // Create additional records for bulk testing
        Bank_Account__c acc = [SELECT Id FROM Bank_Account__c LIMIT 1];
        List<Bank_Transaction__c> transactions = new List<Bank_Transaction__c>();
        
        for(Integer i = 0; i < 100; i++) {
            transactions.add(new Bank_Transaction__c(
                Bank_Account__c = acc.Id,
                Transaction_Id__c = 'BULK-' + i,
                Category__c = 'Education',
                Sub_Category__c = 'Student Loans',
                Transaction_Date__c = System.today(),
                Debit__c = 100.00
            ));
        }
        insert transactions;

        Test.startTest();
        Database.executeBatch(new BankTransactionBatch(), 200);
        Test.stopTest();

        // Verify all records were processed
        System.assertEquals(101, [SELECT COUNT() FROM Bank_Transaction__c WHERE MF_Category__c = 'Loan Proceeds'],
            'All bulk records should be processed');
    }

    @IsTest
    static void testBatchWithMixedCategories() {
        // Create additional mixed records
        Bank_Account__c acc = [SELECT Id FROM Bank_Account__c LIMIT 1];
        List<Bank_Transaction__c> transactions = new List<Bank_Transaction__c>{
            new Bank_Transaction__c(
                Bank_Account__c = acc.Id,
                Transaction_Id__c = 'TEST-4',
                Category__c = 'Education',
                Sub_Category__c = 'Student Loans',
                Transaction_Date__c = System.today(),
                Debit__c = 100.00
            ),
            new Bank_Transaction__c(
                Bank_Account__c = acc.Id,
                Transaction_Id__c = 'TEST-5',
                Category__c = null,
                Sub_Category__c = 'Student Loans',
                Transaction_Date__c = System.today(),
                Debit__c = 200.00
            ),
            new Bank_Transaction__c(
                Bank_Account__c = acc.Id,
                Transaction_Id__c = 'TEST-6',
                Category__c = 'Education',
                Sub_Category__c = null,
                Transaction_Date__c = System.today(),
                Debit__c = 300.00
            )
        };
        insert transactions;

        Test.startTest();
        Database.executeBatch(new BankTransactionBatch(), 200);
        Test.stopTest();

        // Verify results
        List<Bank_Transaction__c> results = [
            SELECT Id, MF_Category__c 
            FROM Bank_Transaction__c 
            WHERE Transaction_Id__c LIKE 'TEST-%'
            ORDER BY Transaction_Id__c
        ];

        System.assertEquals('Loan Proceeds', results[0].MF_Category__c, 
            'Should map valid category/subcategory');
        System.assertEquals(null, results[2].MF_Category__c,
            'Should handle null subcategory');
    }
}