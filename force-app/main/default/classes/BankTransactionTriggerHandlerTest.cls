@IsTest
public class BankTransactionTriggerHandlerTest {
    @TestSetup
    static void setup() {
        // Create required Bank Account
        Bank_Account__c bankAccount = new Bank_Account__c(
            Name = '*********'
        );
        insert bankAccount;
    }

    private static Bank_Transaction__c createTestTransaction(String category, String subCategory) {
        return new Bank_Transaction__c(
            Bank_Account__c = [SELECT Id FROM Bank_Account__c LIMIT 1].Id,
            Transaction_Id__c = 'TEST-' + System.now().getTime() + Math.random(),
            Category__c = category,
            Sub_Category__c = subCategory,
            Transaction_Date__c = System.today(),
            Debit__c = 100.00
        );
    }

    @IsTest
    static void testEducationStudentLoanMapping() {
        Bank_Transaction__c bt = createTestTransaction('Education', 'Student Loans');

        Test.startTest();
        insert bt;
        Test.stopTest();

        Bank_Transaction__c result = [SELECT MF_Category__c FROM Bank_Transaction__c];
        System.assertEquals('Loan Proceeds', result.MF_Category__c, 
            'Should map Education/Student Loans to Loan Proceeds');
    }

    @IsTest
    static void testEducationTuitionFeesMapping() {
        Bank_Transaction__c bt = createTestTransaction('Education', 'Tuition & School Fees');

        Test.startTest();
        insert bt;
        Test.stopTest();

        Bank_Transaction__c result = [SELECT MF_Category__c FROM Bank_Transaction__c];
        //System.assertEquals('Other', result.MF_Category__c,
        //    'Should map Education/Tuition & School Fees to Other : Overhead / Personal');
    }

    @IsTest
    static void testCaseInsensitivity() {
        Bank_Transaction__c bt = createTestTransaction('EDUCATION', 'student loans');
        
        Test.startTest();
        insert bt;
        Test.stopTest();

        Bank_Transaction__c result = [SELECT MF_Category__c FROM Bank_Transaction__c];
        System.assertEquals('Loan Proceeds', result.MF_Category__c,
            'Should handle case insensitivity');
    }

    @IsTest
    static void testBulkProcessing() {
        List<Bank_Transaction__c> transactions = new List<Bank_Transaction__c>();
        Bank_Account__c acc = [SELECT Id FROM Bank_Account__c LIMIT 1];
        
        for(Integer i = 0; i < 200; i++) {
            transactions.add(new Bank_Transaction__c(
                Bank_Account__c = acc.Id,
                Transaction_Id__c = 'BULK-' + i + '-' + System.now().getTime(),
                Category__c = 'Education',
                Sub_Category__c = 'Student Loans',
                Transaction_Date__c = System.today(),
                Debit__c = 100.00
            ));
        }

        Test.startTest();
        insert transactions;
        Test.stopTest();

        List<Bank_Transaction__c> results = [
            SELECT MF_Category__c 
            FROM Bank_Transaction__c 
            WHERE MF_Category__c = 'Loan Proceeds'
        ];
        System.assertEquals(200, results.size(), 
            'Should process bulk records efficiently');
    }

	@IsTest
    static void testUpdateNoChanges() {
        Bank_Transaction__c bt = createTestTransaction('Education', 'Tuition & School Fees');
        insert bt;

        // No changes to Category or Sub-Category
        Test.startTest();
        update bt;
        Test.stopTest();

        // Verify MF Category remains unchanged
        Bank_Transaction__c result = [SELECT MF_Category__c FROM Bank_Transaction__c WHERE Id = :bt.Id];
        //System.assertEquals('Other', result.MF_Category__c, 'MF Category should not change when no relevant fields are updated');
    }
    
    @IsTest
    static void testUpdateChanges() {
        Bank_Transaction__c bt = createTestTransaction('Education', 'Tuition & School Fees');
        insert bt;
		bt.Category__c = 'Earned Income';
        bt.Sub_Category__c = 'Wages & Salary';
        Test.startTest();
        update bt;
        Test.stopTest();

        // Verify MF Category remains unchanged
        Bank_Transaction__c result = [SELECT MF_Category__c FROM Bank_Transaction__c WHERE Id = :bt.Id];
        System.assertEquals('Payroll', result.MF_Category__c, 'MF Category should change');
    }
}