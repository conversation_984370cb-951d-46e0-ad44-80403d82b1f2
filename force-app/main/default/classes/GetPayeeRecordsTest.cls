/**
* @File Name : GetPayRecordsTest.cls
* @Description :
* <AUTHOR>
* @Last Modified By :
* @Last Modified On : May 6, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | May 6, 2025 |   | Initial Version
**/

@isTest
private class GetPayeeRecordsTest {
    
    @testSetup
    static void setupTestData() {
        // Create two Payee records for testing
        List<Payee_Information__c> payees = new List<Payee_Information__c>();
        payees.add(new Payee_Information__c(
            Payee_Name__c            = 'Alpha Corp',
            Payee_Contact_Email__c   = '<EMAIL>',
            Payment_Method__c        = 'Wire',
            Payee_Phone__c           = '************',
            City__c                  = 'Mumbai',
            Country__c               = 'India',
            State_Province__c        = 'Maharashtra',
            Street__c                = '123 Main St',
            Zip_Postal_Code__c       = '400001',
            Mail_Check_To__c         = 'Alpha HQ',
            Account_Name__c          = 'AlphaBank',
            Bank_Account_Number__c   = '*********',
            Bank_Name__c             = 'State Bank',
            Bank_Routing_Number__c   = 'SBIN0000001'
        ));
        payees.add(new Payee_Information__c(
            Payee_Name__c            = 'Beta LLC',
            Payee_Contact_Email__c   = '<EMAIL>',
            Payment_Method__c        = 'Check',
            Payee_Phone__c           = '************',
            City__c                  = 'Delhi',
            Country__c               = 'India',
            State_Province__c        = 'Delhi',
            Street__c                = '456 Side Rd',
            Zip_Postal_Code__c       = '110001',
            Mail_Check_To__c         = 'Beta HQ',
            Account_Name__c          = 'BetaBank',
            Bank_Account_Number__c   = '*********',
            Bank_Name__c             = 'HDFC Bank',
            Bank_Routing_Number__c   = 'HDFC0001234'
        ));
        insert payees;
    }
    
    static testMethod void testGetPayeeOptions() {
        Test.startTest();
            List<Payee_Information__c> results = GetPayeeRecords.getPayeeOptions();
        Test.stopTest();
        
        // Expect at least 2 records, ordered by Name (Alpha before Beta)
        System.assertEquals(2, results.size(), 'Number of payees returned');
        System.assertEquals('Alpha Corp', results[0].Payee_Name__c, 'First payee should be Alpha Corp');
        System.assertEquals('Beta LLC',  results[1].Payee_Name__c, 'Second payee should be Beta LLC');
    }
    
    static testMethod void testGetPayeeRecord() {
        // Fetch an existing record Id
        Id sampleId = [SELECT Id FROM Payee_Information__c WHERE Payee_Name__c = 'Alpha Corp' LIMIT 1].Id;
        
        Test.startTest();
            Payee_Information__c pi = GetPayeeRecords.getPayeeRecord(sampleId);
        Test.stopTest();
        
        // Verify all fields are returned correctly
        System.assertEquals('Alpha Corp',          pi.Payee_Name__c,          'Name matches');
        System.assertEquals('<EMAIL>',   pi.Payee_Contact_Email__c, 'Email matches');
        System.assertEquals('Wire',                pi.Payment_Method__c,      'Payment method matches');
        System.assertEquals('************',        pi.Payee_Phone__c,         'Phone matches');
        System.assertEquals('Mumbai',              pi.City__c,                'City matches');
        System.assertEquals('India',               pi.Country__c,             'Country matches');
        System.assertEquals('Maharashtra',         pi.State_Province__c,      'State matches');
        System.assertEquals('123 Main St',         pi.Street__c,              'Street matches');
        System.assertEquals('400001',              pi.Zip_Postal_Code__c,     'Zip matches');
        System.assertEquals('Alpha HQ',            pi.Mail_Check_To__c,       'Mail_Check_To matches');
        System.assertEquals('AlphaBank',           pi.Account_Name__c,        'Account Name matches');
        System.assertEquals('*********',           pi.Bank_Account_Number__c, 'Bank Account matches');
        System.assertEquals('State Bank',          pi.Bank_Name__c,           'Bank Name matches');
        System.assertEquals('SBIN0000001',         pi.Bank_Routing_Number__c, 'Routing Number matches');
    }
}