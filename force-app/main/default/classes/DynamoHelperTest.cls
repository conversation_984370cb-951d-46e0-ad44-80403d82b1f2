@isTest
public class DynamoHelperTest {


    // --- Positive Scenarios ---

    @isTest
    static void testParseAllTypes() {
        // Arrange: Create JSON with all supported types
        String dynamoJson = '{' +
            '"stringField": {"S": "Hello DynamoDB"},' +
            '"numberField": {"N": "987.65"},' +
            '"booleanField": {"BOOL": true},' +
            '"falseBooleanField": {"BOOL": false},' +
            '"binaryField": {"B": "SGVsbG8="},' + // "Hello" in Base64
            '"stringSetField": {"SS": ["value1", "value2", null, "value1"]},' +
            '"numberSetField": {"NS": ["10", "-20.5", "3.14e1", null]},' +
            '"binarySetField": {"BS": ["AQID", null, "BAUG"]},' +
            '"listField": {' +
            '  "L": [' +
            '    {"S": "In List"},' +
            '    {"N": "50"},' +
            '    {"BOOL": false},' +
            '    null,' +
            '    {"M": {"nestedInList": {"N": "11"}}}' +
            '  ]' +
            '},' +
            '"mapField": {' +
            '  "M": {' +
            '    "nestedString": {"S": "Nested Value"},' +
            '    "nestedNumber": {"N": "2000"},' +
            '    "nestedBool": {"BOOL": true}' +
            '  }' +
            '},' +
            '"explicitNullField": null,' +
            '"emptyStringField": {"S": ""},' +
            '"zeroNumberField": {"N": "0"}' +
        '}';

        // Act
        Test.startTest();
        Map<String, Object> result = DynamoHelper.parse(dynamoJson);
        Test.stopTest();

        // Assert: Check if the result is not null and has the expected number of keys
        System.assertNotEquals(null, result, 'Result map should not be null after parsing various types.');
        // Optional alternative (counts as a second assert, but more useful):
        // System.assertEquals(13, result.size(), 'Result map should have 13 keys.');
    }

    @isTest
    static void testNestedStructures() {
        // Arrange
        String dynamoJson = '{' +
            '"level1Map": {' +
            '  "M": {' +
            '    "level2String": {"S": "L2 String"},' +
            '    "level2List": {' +
            '      "L": [' +
            '        {"S": "L2 List Item 1"},' +
            '        {"M": {' +
            '             "level3Num": {"N": "333"},' +
            '             "level3Bool": {"BOOL": false}' +
            '             }' +
            '        }' +
            '      ]' +
            '    }' +
            '  }' +
            '}' +
        '}';

        // Act
        Test.startTest();
        Map<String, Object> result = DynamoHelper.parse(dynamoJson);
        Test.stopTest();

        // Assert: Check if the main key exists and its value is a map
        System.assert(result.get('level1Map') instanceof Map<String, Object>, 'Nested structure parsing failed at level 1.');
    }

     @isTest
    static void testEmptyItem() {
        // Arrange
        String dynamoJson = '{}';

        // Act
        Test.startTest();
        Map<String, Object> result = DynamoHelper.parse(dynamoJson);
        Test.stopTest();

        // Assert: Check if the result is not null (implicitly checks for no exceptions)
        System.assertNotEquals(null, result, 'Result should not be null for empty item.');
    }

    @isTest
    static void testMapWithNonDynamoStructure() {
        // Arrange: A map that doesn't use the { "TYPE": value } structure internally
         String json = '{ "regularMap": { "key1": "value1", "key2": 123.4, "key3": true } }';

         // Act
         Test.startTest();
         Map<String, Object> result = DynamoHelper.parse(json);
         Test.stopTest();

         // Assert: Check that the parsing didn't throw an error and the key exists
         System.assert(result.containsKey('regularMap'), 'Parsing a regular map structure failed.');
    }


    // --- Negative Scenarios ---

     @isTest
    static void testNullInput() {
        // Act
        Test.startTest();
        Map<String, Object> result = DynamoHelper.parse(null);
        Test.stopTest();

        // Assert: Check the result map is not null (it should be empty)
        System.assertNotEquals(null, result, 'Result should be an empty map, not null, for null input.');
    }

    @isTest
    static void testBlankInput() {
         // Act
        Test.startTest();
        Map<String, Object> result = DynamoHelper.parse('   '); // Whitespace only
        Test.stopTest();

        // Assert: Check the result map is not null (it should be empty)
        System.assertNotEquals(null, result, 'Result should be an empty map, not null, for blank input.');
    }

    @isTest
    static void testMalformedJson_MissingBrace() {
        // Arrange
        String malformedJson = '{"key": {"S": "value"}'; // Missing closing brace

        // Act & Assert
        Test.startTest();
        AuraHandledException caughtEx = null;
        try {
            DynamoHelper.parse(malformedJson);
        } catch (AuraHandledException e) {
            caughtEx = e; // Exception caught as expected
        } catch (Exception e) {
            // Fail if wrong exception type is caught
             System.assert(false, 'Caught wrong exception type: ' + e.getTypeName());
        }
        Test.stopTest();

        // Assert: Check that the expected exception was caught
        System.assertNotEquals(null, caughtEx, 'Expected AuraHandledException was not caught for malformed JSON.');
    }

     @isTest
    static void testUnsupportedDynamoType() {
        // Arrange: Use a token not in DYNAMO_TYPE_TOKENS
        String jsonWithUnsupportedType = '{"key": {"X": "some value"}}';

        // Act & Assert
        Test.startTest();
        AuraHandledException caughtEx = null;
         Map<String, Object> result = null;
        try {
            // NOTE: Current logic treats {"X": "value"} as a regular map, *not* an error.
             result = DynamoHelper.parse(jsonWithUnsupportedType);
        } catch (AuraHandledException e) {
             caughtEx = e; // Should NOT be caught
        } catch (Exception e) {
             System.assert(false, 'Unexpected exception type ' + e.getTypeName());
        }
        Test.stopTest();

        // Assert: Check that NO exception was thrown (validating current behavior)
        System.assertEquals(null, caughtEx, 'No exception should be thrown for unknown type token with current logic.');
    }


     @isTest
    static void testInvalidNumberFormat() {
        // Arrange
        String jsonWithInvalidNumber = '{"numberField": {"N": "not-a-valid-number"}}';

        // Act & Assert
        Test.startTest();
        AuraHandledException caughtEx = null;
        try {
            DynamoHelper.parse(jsonWithInvalidNumber);
        } catch (AuraHandledException e) {
            caughtEx = e;
        } catch (Exception e) {
             System.assert(false, 'Caught wrong exception type: ' + e.getTypeName());
        }
        Test.stopTest();

        // Assert: Check that the expected exception was caught
        System.assertNotEquals(null, caughtEx, 'Expected AuraHandledException was not caught for invalid number format.');
    }

    @isTest
    static void testInvalidBooleanFormat() {
        // Arrange: BOOL value is a string, not a boolean literal
        String jsonWithInvalidBool = '{"boolField": {"BOOL": "true"}}';

        // Act & Assert
        Test.startTest();
         AuraHandledException caughtEx = null;
        try {
            DynamoHelper.parse(jsonWithInvalidBool);
        } catch (AuraHandledException e) {
             caughtEx = e;
        } catch (Exception e) {
              System.assert(false, 'Caught wrong exception type: ' + e.getTypeName());
        }
        Test.stopTest();

        // Assert: Check that the expected exception was caught
        System.assertNotEquals(null, caughtEx, 'Expected AuraHandledException was not caught for invalid boolean format.');
    }

     @isTest
    static void testInvalidBinaryFormat() {
        // Arrange: B value is not valid Base64
        String jsonWithInvalidBinary = '{"binaryField": {"B": "This is not base64!"}}';

        // Act & Assert
        Test.startTest();
         AuraHandledException caughtEx = null;
        try {
            DynamoHelper.parse(jsonWithInvalidBinary);
        } catch (AuraHandledException e) {
             caughtEx = e;
        } catch (Exception e) {
              System.assert(false, 'Caught wrong exception type: ' + e.getTypeName());
        }
        Test.stopTest();

        // Assert: Check that the expected exception was caught
        System.assertNotEquals(null, caughtEx, 'Expected AuraHandledException was not caught for invalid binary format.');
    }

    // --- Tests for incorrect structures within type wrappers ---

    @isTest
    static void testInvalidListValueForTypeL() {
        // Arrange: L value is a map, not a list
        String json = '{"listField": {"L": {"item": "not a list"}}}';
        // Act & Assert
        Test.startTest();
        AuraHandledException caughtEx = null;
        try {
            DynamoHelper.parse(json);
        } catch (AuraHandledException e) { caughtEx = e; }
          catch (Exception e) { System.assert(false, 'Caught wrong exception type: ' + e.getTypeName()); }
        Test.stopTest();

        // Assert: Check that the expected exception was caught
        System.assertNotEquals(null, caughtEx, 'Expected exception for invalid structure for L type.');
    }

    @isTest
    static void testInvalidMapValueForTypeM() {
        // Arrange: M value is a list, not a map
        String json = '{"mapField": {"M": ["item1", "item2"]}}';
         // Act & Assert
        Test.startTest();
        AuraHandledException caughtEx = null;
        try {
            DynamoHelper.parse(json);
        } catch (AuraHandledException e) { caughtEx = e; }
          catch (Exception e) { System.assert(false, 'Caught wrong exception type: ' + e.getTypeName()); }
        Test.stopTest();

        // Assert: Check that the expected exception was caught
        System.assertNotEquals(null, caughtEx, 'Expected exception for invalid structure for M type.');
    }

    @isTest
    static void testInvalidListValueForTypeSS() {
        // Arrange: SS value is a string, not a list
        String json = '{"ssField": {"SS": "not a list"}}';
         // Act & Assert
        Test.startTest();
        AuraHandledException caughtEx = null;
        try {
            DynamoHelper.parse(json);
        } catch (AuraHandledException e) { caughtEx = e; }
          catch (Exception e) { System.assert(false, 'Caught wrong exception type: ' + e.getTypeName()); }
        Test.stopTest();

        // Assert: Check that the expected exception was caught
        System.assertNotEquals(null, caughtEx, 'Expected exception for invalid structure for SS type.');
    }

     // --- Tests for invalid item types within Sets/Lists ---

     @isTest
    static void testInvalidNumberSetItem() {
        // Arrange: "two" is not a valid string representation of a number
        String json = '{"numberSetField": {"NS": ["1", "two", "3"]}}';
         // Act & Assert
        Test.startTest();
        AuraHandledException caughtEx = null;
        try {
            DynamoHelper.parse(json);
        } catch (AuraHandledException e) { caughtEx = e; }
          catch (Exception e) { System.assert(false, 'Caught wrong exception type: ' + e.getTypeName()); }
        Test.stopTest();

        // Assert: Check that the expected exception was caught
        System.assertNotEquals(null, caughtEx, 'Expected exception for invalid item format in NS.');
    }

    @isTest
    static void testInvalidStringSetItemType() {
         // Arrange: Contains a number (123) instead of only strings
        String json = '{"stringSetField": {"SS": ["apple", 123]}}';
         // Act & Assert
        Test.startTest();
        AuraHandledException caughtEx = null;
        try {
            DynamoHelper.parse(json);
        } catch (AuraHandledException e) { caughtEx = e; }
          catch (Exception e) { System.assert(false, 'Caught wrong exception type: ' + e.getTypeName()); }
        Test.stopTest();

        // Assert: Check that the expected exception was caught
        System.assertNotEquals(null, caughtEx, 'Expected exception for invalid item type in SS.');
    }

    @isTest
    static void testInvalidBinarySetItemType() {
         // Arrange: Contains a boolean instead of only base64 strings
        String json = '{"binarySetField": {"BS": ["AQID", true]}}';
         // Act & Assert
        Test.startTest();
        AuraHandledException caughtEx = null;
        try {
            DynamoHelper.parse(json);
        } catch (AuraHandledException e) { caughtEx = e; }
          catch (Exception e) { System.assert(false, 'Caught wrong exception type: ' + e.getTypeName()); }
        Test.stopTest();

        // Assert: Check that the expected exception was caught
        System.assertNotEquals(null, caughtEx, 'Expected exception for invalid item type in BS.');
    }

     @isTest
    static void testInvalidBinarySetItemFormat() {
         // Arrange: Contains an invalid base64 string
        String json = '{"binarySetField": {"BS": ["AQID", "---"]}}'; // "---" is invalid base64
         // Act & Assert
        Test.startTest();
        AuraHandledException caughtEx = null;
        try {
            DynamoHelper.parse(json);
        } catch (AuraHandledException e) { caughtEx = e; }
          catch (Exception e) { System.assert(false, 'Caught wrong exception type: ' + e.getTypeName()); }
        Test.stopTest();

        // Assert: Check that the expected exception was caught
        System.assertNotEquals(null, caughtEx, 'Expected exception for invalid item format in BS.');
    }
}