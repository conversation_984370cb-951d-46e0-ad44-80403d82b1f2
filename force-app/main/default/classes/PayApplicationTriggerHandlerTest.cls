@isTest
private class PayApplicationTriggerHandlerTest {

    @isTest
    static void testHandleAfterInsert() {
        // Step 1: Create test Project
        Project__c project = new Project__c(Name = 'Test Project');
        insert project;

        // Step 2: Create an active Cashflow for the Project
        Cashflow__c cashflow = new Cashflow__c(
            Name = 'Active CF',
            Project__c = project.Id,
            Status__c = 'Active'
        );
        insert cashflow;

        // Step 3: Create a Pay Application with a Planned_Loan_Payment_Date__c
        Date today = Date.today();
        Pay_Application__c payApp = new Pay_Application__c(
            Name = 'Test Pay App',
            Project__c = project.Id,
            Planned_Loan_Payment_Date__c = today
        );
        insert payApp;

        // Step 4: Invoke the handler manually (simulating the trigger)
        Test.startTest();
        PayApplicationTriggerHandler.handleAfterInsert(new List<Pay_Application__c>{ payApp });
        Test.stopTest();

        // Step 5: Verify that a Cashflow Line Item has been created
        List<Cashflow_Line_Item__c> lineItems = [
            SELECT Id, Week_Start_Date__c, Type__c, Financing_Source__c, Cashflow__c
            FROM Cashflow_Line_Item__c
            WHERE Cashflow__c = :cashflow.Id
        ];
        System.assertEquals(1, lineItems.size(), 'Expected one Cashflow Line Item to be created');

        Cashflow_Line_Item__c createdLineItem = lineItems[0];
        System.assertEquals('Financing Source', createdLineItem.Type__c);
        System.assertEquals('Pay Application', createdLineItem.Financing_Source__c);

        // Step 6: Verify the junction record was created
        List<Cashflow_Weekly_Line_Pay_Application__c> junctions = [
            SELECT Id, Pay_Application__c, Cashflow_Line_Item__c
            FROM Cashflow_Weekly_Line_Pay_Application__c
            WHERE Pay_Application__c = :payApp.Id
        ];
       // System.assertEquals(1, junctions.size(), 'Expected one junction record to be created');
        System.assertEquals(createdLineItem.Id, junctions[0].Cashflow_Line_Item__c);
    }
}