@isTest
public class FeedItemTriggerHandlerTest {
    @isTest
    static void testHandleAfterInsert() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        System.debug('testAccount '+testAccount);

		Contact con = new Contact(FirstName = 'test', Lastname = 'flintoff',AccountId = testAccount.Id);
        insert con;

        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity',
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(30),
            AccountId = testAccount.Id
        );
        insert testOpportunity;

		Project__c proj = new Project__c(Name = 'test proj', Account_Name__c = testAccount.Id);
        insert proj;
        
        Disbursement_Request__c dr = new Disbursement_Request__c(Project_lookup__c = proj.Id, Bank_Name__c = 'test Bank n');
        insert dr;

        User communityUser = [SELECT Id FROM User WHERE Profile.Name = 'Customer Community User' LIMIT 1];
        User internalUser = [SELECT Id FROM User WHERE Profile.Name = 'System Administrator' LIMIT 1];
        
        FeedItem internalFeedItem3 = new FeedItem(
            ParentId = dr.Id,
            CreatedById = internalUser.Id,
            Body = 'Test dis req community feed item',
            Visibility = 'AllUsers'
        );
        FeedItem communityFeedItem = new FeedItem(
            ParentId = testOpportunity.Id,
            CreatedById = communityUser.Id,
            Body = 'Test community feed item',
            Visibility = 'AllUsers'
        );
        FeedItem internalFeedItem2 = new FeedItem(
            ParentId = proj.Id,
            CreatedById = internalUser.Id,
            Body = 'Test project internal feed item',
            Visibility = 'AllUsers'
        );
        FeedItem internalFeedItem = new FeedItem(
            ParentId = testOpportunity.Id,
            CreatedById = internalUser.Id,
            Body = 'Test internal feed item',
            Visibility = 'AllUsers'
        );
        insert new List<FeedItem>{ internalFeedItem, internalFeedItem2, internalFeedItem3};

        Test.startTest();
        FeedItemTriggerHandler.handleAfterInsert(new List<FeedItem>{internalFeedItem, internalFeedItem2,internalFeedItem3});
        Test.stopTest();       
    }

    @IsTest
    static void testPreventUnauthorizedDelete() {
        User communityUser = [SELECT Id FROM User WHERE Profile.Name = 'Customer Community User' LIMIT 1];
        User internalUser = [SELECT Id FROM User WHERE Profile.Name = 'System Administrator' LIMIT 1];
		Account acc = new Account(Name = 'Test Account');
    	insert acc;
        
        FeedItem feedItem = new FeedItem(
            ParentId = acc.Id, 
            CreatedById = internalUser.Id,
            Body = 'Test feed item'
        );
        insert feedItem;

        Test.startTest();
        try {
            FeedItemTriggerHandler.preventUnauthorizedDelete(new List<FeedItem>{feedItem});
            delete feedItem; 
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Delete prevented: User does not have permission to delete this Internal post'));
        }
        Test.stopTest();
    }
    
    @isTest
    private static void testIsDuplicateTracker() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        
        Opportunity testOpportunity = new Opportunity(Name = 'Test Opportunity', AccountId = testAccount.Id, StageName = 'Prospecting', CloseDate = Date.today());
        insert testOpportunity;
        
        Project__c testProject = new Project__c(Name = 'Test Project', Account_Name__c = testAccount.Id);
        insert testProject;
        
        Disbursement_Request__c testDisbursement = new Disbursement_Request__c(Bank_Name__c = 'test Bank n', Project_lookup__c = testProject.Id);
        insert testDisbursement;
        
        FeedItem testFeed = new FeedItem(ParentId = testOpportunity.Id, Body = 'Test feed item');
        insert testFeed;
        
        Chatter_Post_Tracker__c tracker = new Chatter_Post_Tracker__c(
            Related_Opportunity__c = testOpportunity.Id,
            Status__c = 'New'
        );
        insert tracker;
        Boolean resultWithTracker = FeedItemTriggerHandler.isDuplicateTracker(testFeed);
    }
	
	@isTest
    static void testCreateDynamicUrl() {
        String baseUrl = 'https://test.salesforce.com';
        Id postId = Id.valueOf('***************');
        Id orgId = Id.valueOf('00D000000000001');
        Id userId = Id.valueOf('***************');

        Test.startTest();
        String urlSandbox = FeedItemTriggerHandler.createDynamicUrl(baseUrl, postId, orgId, userId);
        Test.stopTest();

        
    }

    @isTest
    static void testConstructHtmlBody() {
        Id postId = Id.valueOf('***************');
        String userName = 'John Doe';
        String postBody = 'This is a test post body';
        String dynamicUrl = 'https://test.salesforce.com/mf/s/feed/***************?s1oid=00D000000000001';
        String userId = '***************';

        String htmlBody = FeedItemTriggerHandler.constructHtmlBody(postId, userName, postBody, dynamicUrl, userId);
    }

    @IsTest
    static void testExtractUsersFromPostBody() {
        String postBody = 'Hello @john.doe, please check this @jane_doe and @user123!';

        List<String> extractedUsers = FeedItemTriggerHandler.extractUsersFromPostBody(postBody);
    }    

	@IsTest
    static void testProcessMentionsAndSendEmails() {
        Account acc = new Account(Name = 'Test Account');
        insert acc;
    
        FeedItem feedItem = new FeedItem(
            ParentId = acc.Id,
            Body = 'Test mention feed item'
        );
        insert feedItem;
    
        Test.startTest();
        FeedItemTriggerHandler.processMentionsAndSendEmails(new List<FeedItem>{feedItem});
        Test.stopTest();
    
    }
}