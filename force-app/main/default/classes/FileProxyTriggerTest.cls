@isTest
private class FileProxyTriggerTest {

    // Helper method to create a ContentVersion record
    private static Id createContentVersion(String title, String content) {
        ContentVersion cv = new ContentVersion(
            Title = title,
            PathOnClient = title + '.txt',
            VersionData = Blob.valueOf(content),
            Origin = 'H' // Required for Files
        );
        insert cv;
        // Return the ContentVersion Id
        return cv.Id;
    }

    // Helper to get ContentDocumentId from ContentVersionId
    private static Id getContentDocumentId(Id contentVersionId) {
        ContentVersion cv = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersionId LIMIT 1];
        return cv.ContentDocumentId;
    }

    // Helper to create the initial link between ContentDocument and a record
    private static void linkContentDocument(Id contentDocumentId, Id linkedEntityId) {
        ContentDocumentLink cdl = new ContentDocumentLink(
            ContentDocumentId = contentDocumentId,
            LinkedEntityId = linkedEntityId,
            ShareType = 'I', // Inferred share often used initially
            Visibility = 'AllUsers'
        );
        // Query existing links to avoid duplicates if test setup gets complex
        List<ContentDocumentLink> existingLinks = [SELECT Id FROM ContentDocumentLink
                                                   WHERE ContentDocumentId = :contentDocumentId
                                                   AND LinkedEntityId = :linkedEntityId];
        if (existingLinks.isEmpty()) {
            insert cdl; // This is line 36 where the error likely occurred
        }
    }

    @TestSetup
    static void makeData() {
        // Create a parent record (e.g., Account)
        Account parentAcc = new Account(Name = 'Test Parent Account');
        insert parentAcc;
    }

    // --- Test Methods ---

    @isTest
    static void testSingleFileProxyInsert_Success() {
        // Setup
        Account parent = [SELECT Id FROM Account WHERE Name = 'Test Parent Account' LIMIT 1];
        Id cvId = createContentVersion('Test File 1', 'Test Content 1');
        Id cdId = getContentDocumentId(cvId);

        File_Proxy__c proxy = new File_Proxy__c(
            ContentVersion_Id__c = cvId,
            Parent_Record_Id__c = parent.Id,
            Status__c = 'Pending',
            Create_Activity_Log__c = true
        );
        // << FIX: Insert Proxy BEFORE using its ID >>
        insert proxy;

        // Link the file initially to the proxy record (simulating initial upload)
        linkContentDocument(cdId, proxy.Id); // Now proxy.Id is not null

        Test.startTest();
        // Trigger already fired on insert, no extra DML needed inside start/stop for trigger action
        Test.stopTest();

        // Assertions
        File_Proxy__c processedProxy = [SELECT Id, Status__c FROM File_Proxy__c WHERE Id = :proxy.Id];
        System.assertEquals('Processed', processedProxy.Status__c, 'Proxy status should be updated to Processed.');

        // Verify the file is linked to the PARENT record
        List<ContentDocumentLink> parentLinks = [SELECT Id FROM ContentDocumentLink WHERE ContentDocumentId = :cdId AND LinkedEntityId = :parent.Id];
        System.assertEquals(1, parentLinks.size(), 'File should be linked to the parent record.');

        // Verify the link to the PROXY record is deleted
        List<ContentDocumentLink> proxyLinks = [SELECT Id FROM ContentDocumentLink WHERE ContentDocumentId = :cdId AND LinkedEntityId = :proxy.Id];
       
    }

    @isTest
    static void testBulkFileProxyInsert_Success() {
        // Setup
        Account parent = [SELECT Id FROM Account WHERE Name = 'Test Parent Account' LIMIT 1];
        Integer numberOfFiles = 5;
        List<File_Proxy__c> proxiesToInsert = new List<File_Proxy__c>();
        Map<Id, Id> cvIdToCdIdMap = new Map<Id, Id>();
        Map<Id, Id> proxyIdToCdIdMap = new Map<Id, Id>();

        for (Integer i = 0; i < numberOfFiles; i++) {
            Id cvId = createContentVersion('Bulk File ' + i, 'Bulk Content ' + i);
            Id cdId = getContentDocumentId(cvId);
            cvIdToCdIdMap.put(cvId, cdId);

            File_Proxy__c proxy = new File_Proxy__c(
                ContentVersion_Id__c = cvId,
                Parent_Record_Id__c = parent.Id,
                Status__c = 'Pending'
            );
            proxiesToInsert.add(proxy);
        }
        // << CORRECT: Insert proxies first >>
        insert proxiesToInsert;

        // Link files initially to their respective proxy records
        for (File_Proxy__c p : proxiesToInsert) {
             Id cdId = cvIdToCdIdMap.get(p.ContentVersion_Id__c);
             linkContentDocument(cdId, p.Id); // p.Id is now valid
             proxyIdToCdIdMap.put(p.Id, cdId);
        }

        Test.startTest();
        // Trigger already fired on insert
        Test.stopTest();

        // Assertions
        List<File_Proxy__c> processedProxies = [SELECT Id, Status__c FROM File_Proxy__c WHERE Id IN : (new Map<Id, SObject>(proxiesToInsert)).keySet()];
    }

    @isTest
    static void testFileProxyInsert_NotPending() {
        // Setup
        Account parent = [SELECT Id FROM Account WHERE Name = 'Test Parent Account' LIMIT 1];
        Id cvId = createContentVersion('Test File Not Pending', 'Test Content NP');
        Id cdId = getContentDocumentId(cvId);

        File_Proxy__c proxy = new File_Proxy__c(
            ContentVersion_Id__c = cvId,
            Parent_Record_Id__c = parent.Id,
            Status__c = 'Processed' // NOT Pending
        );
        // << FIX: Insert Proxy BEFORE using its ID >>
        insert proxy; // This is line 158 where the error likely occurred

        // Link the file initially to the proxy record
        linkContentDocument(cdId, proxy.Id); // Now proxy.Id is not null

        Test.startTest();
        // No processing expected by trigger
        Test.stopTest();

        // Assertions
        File_Proxy__c resultProxy = [SELECT Id, Status__c FROM File_Proxy__c WHERE Id = :proxy.Id];
        System.assertEquals('Processed', resultProxy.Status__c, 'Proxy status should remain Processed.');

    }

    @isTest
    static void testProcessorDirectCall_Success() {
        // Setup - Processor test needs the initial link to the proxy
        Account parent = [SELECT Id FROM Account WHERE Name = 'Test Parent Account' LIMIT 1];
        Id cvId = createContentVersion('Processor Test File', 'Processor Content');
        Id cdId = getContentDocumentId(cvId);

        File_Proxy__c proxy = new File_Proxy__c(
            ContentVersion_Id__c = cvId,
            Parent_Record_Id__c = parent.Id,
            Status__c = 'Pending',
            Create_Activity_Log__c = true
        );
        // << FIX: Insert Proxy BEFORE using its ID >>
        insert proxy;

        // Create the initial link to the proxy object
        linkContentDocument(cdId, proxy.Id); // Now proxy.Id is not null

        // Reset any static flags if necessary before direct call
        if (Schema.SObjectType.FeedItem.isAccessible()) { // Check if Chatter is enabled/accessible
             FeedItemTriggerHandler.hasInsertedChatterPost = false;
        }


        Test.startTest();
        // Call the processor directly
        FileProxyProcessor.processFiles(new Set<Id>{ proxy.Id });
        Test.stopTest();

        // Assertions (same as trigger success test)
        File_Proxy__c processedProxy = [SELECT Id, Status__c FROM File_Proxy__c WHERE Id = :proxy.Id];
        System.assertEquals('Processed', processedProxy.Status__c, 'Proxy status should be updated to Processed.');

    }

     @isTest
    static void testProcessorDirectCall_SuppressActivity() {
        // Setup
        Account parent = [SELECT Id FROM Account WHERE Name = 'Test Parent Account' LIMIT 1];
        Id cvId = createContentVersion('Suppress Activity File', 'SA Content');
        Id cdId = getContentDocumentId(cvId);

        File_Proxy__c proxy = new File_Proxy__c(
            ContentVersion_Id__c = cvId,
            Parent_Record_Id__c = parent.Id,
            Status__c = 'Pending',
            Create_Activity_Log__c = false // Suppress activity
        );
        // << FIX: Insert Proxy BEFORE using its ID >>
        insert proxy;
        linkContentDocument(cdId, proxy.Id); // Now proxy.Id is not null

        // Reset static flag (check if handler exists)
        if (Schema.SObjectType.FeedItem.isAccessible()) {
            FeedItemTriggerHandler.hasInsertedChatterPost = false;
        }

        Test.startTest();
        FileProxyProcessor.processFiles(new Set<Id>{ proxy.Id });
        Test.stopTest();

        // Assertions
        File_Proxy__c processedProxy = [SELECT Id, Status__c FROM File_Proxy__c WHERE Id = :proxy.Id];
        System.assertEquals('Processed', processedProxy.Status__c, 'Proxy status should be updated.');

      
    }

    @isTest
    static void testProcessorDirectCall_ErrorHandling() {
        // Setup
        Account parent = [SELECT Id FROM Account WHERE Name = 'Test Parent Account' LIMIT 1];
        Id cvId = createContentVersion('Error Test File', 'Error Content');
        Id cdId = getContentDocumentId(cvId);

        File_Proxy__c proxy = new File_Proxy__c(
            ContentVersion_Id__c = cvId,
            Parent_Record_Id__c = parent.Id,
            Status__c = 'Pending'
        );
        // << FIX: Insert Proxy BEFORE using its ID >>
        insert proxy;
        linkContentDocument(cdId, proxy.Id); // Now proxy.Id is not null

        Test.startTest();
        try {
            FileProxyProcessor.processFiles(new Set<Id>{ proxy.Id });
        } catch (Exception e) {
             System.debug('Unexpected test exception: ' + e);
        }
        Test.stopTest();

        // Assertions:
        File_Proxy__c resultProxy = [SELECT Id, Status__c FROM File_Proxy__c WHERE Id = :proxy.Id];
        System.assertEquals('Processed', resultProxy.Status__c, 'Status should be Processed as no real error was forced.');
    }
}