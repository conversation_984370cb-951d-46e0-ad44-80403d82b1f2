@SuppressWarnings('PMD.AvoidDeeplyNestedIfStmts, PMD.CognitiveComplexity, PMD.ExcessiveMethodLength, PMD.NcssMethodCount, PMD.StdCyclomaticComplexity, PMD')
public without sharing class cashFlowSessionController {

    private static final String CLASS_NAME = 'cashFlowSessionController';
    private static final List<String> LOG_TAGS = new List<String>{CLASS_NAME, 'CashflowProcessing'};
    private static final List<String> DML_TAGS = new List<String>{CLASS_NAME, 'DML_OPERATION'};
    private static final List<String> API_TAGS = new List<String>{CLASS_NAME, 'API_CALL'};
    private static final List<String> PARSING_TAGS = new List<String>{CLASS_NAME, 'PARSING'};

    // Wrapper class to hold prepared SObjects for DML
    private class PreparedObjects {
        List<Cashflow_Line_Item_Child__c> childrenToUpsert = new List<Cashflow_Line_Item_Child__c>();
        List<Cashflow_Line_Item__c> parentsToInsert = new List<Cashflow_Line_Item__c>();
        List<Cashflow_Line_Item_Junction__c> junctionsToInsert = new List<Cashflow_Line_Item_Junction__c>();
    }

    @AuraEnabled
    public static Map<String, Object> createRecordsFromDynamoData(String accountId, String dynamoJsonString, String projectId) {
        Map<String, Object> result = new Map<String, Object>{'success' => false};
        if (String.isBlank(dynamoJsonString) || String.isBlank(projectId)) {
            result.put('errorMessage', 'JSON data and ProjectId are required.');
            return result;
        }

        Map<String, Object> dynamoData;
        try {
            dynamoData = (Map<String, Object>) JSON.deserializeUntyped(dynamoJsonString);
        } catch (Exception e) {
            result.put('errorMessage', 'Error parsing input JSON: ' + e.getMessage());
            return result;
        }

        SavePoint sp = Database.setSavepoint();
        try {
            Cashflow__c cashflow = createCashflowRecord(dynamoData, projectId);
            insert cashflow;
            result.put('cashflowId', cashflow.Id);
            result.put('projectId', projectId);

            // Only delete non-financing source line items
            List<Cashflow_Line_Item__c> oldParents = [SELECT Id FROM Cashflow_Line_Item__c WHERE Cashflow__c = :cashflow.Id AND Type__c != 'Financing Source'];
            if(!oldParents.isEmpty()){
                delete oldParents;
            }

            handleFinancingSources(cashflow.Id, projectId);
            PreparedObjects preparedData = prepareLineItems(dynamoData, cashflow.Id);
            
            if (preparedData.childrenToUpsert.isEmpty()) {
                result.put('success', true);
                return result;
            }

            upsert preparedData.childrenToUpsert Cashflow_Line_Item_Child__c.Fields.External_Node_Id__c;
            insert preparedData.parentsToInsert;
            
            // --- START: Definitive Junction Linking Fix ---
            
            // 1. Create a map of the real parent Salesforce IDs using their External IDs as keys.
            Map<String, Id> parentIdByExternalId = new Map<String, Id>();
            for(Cashflow_Line_Item__c p : [SELECT Id, External_Week_Id__c FROM Cashflow_Line_Item__c WHERE Id IN :preparedData.parentsToInsert]){
                parentIdByExternalId.put(p.External_Week_Id__c, p.Id);
            }
            
            // 2. Finalize the junction records in memory.
            for(Cashflow_Line_Item_Junction__c j : preparedData.junctionsToInsert){
                Cashflow_Line_Item__c parentPlaceholder = (Cashflow_Line_Item__c)j.getSObject('Cashflow_Line_Item__r');
                
                if(parentPlaceholder != null){
                    Id realParentId = parentIdByExternalId.get(parentPlaceholder.External_Week_Id__c);

                    // **THE FIX**: First, clear the temporary relationship reference.
                    j.putSObject('Cashflow_Line_Item__r', null);
                    
                    // **THE FIX**: Second, set the real Salesforce ID on the lookup field.
                    j.Cashflow_Line_Item__c = realParentId;
                }
            }
            
            // 3. Insert the junctions, which are now correctly and unambiguously linked.
            insert preparedData.junctionsToInsert;
            
            result.put('success', true);

        } catch (Exception e) {
            Database.rollback(sp);
            result.put('success', false);
            result.put('errorMessage', 'Error processing data: ' + e.getMessage() + ' Stacktrace: ' + e.getStackTraceString());
        }
        return result;
    }

    private static Cashflow__c createCashflowRecord(Map<String, Object> dynamoData, String projectId) {
        Map<String, Object> details = getMapValue(dynamoData, 'details');
        Cashflow__c cashflow = new Cashflow__c(Project__c = projectId);
        if(details != null){
            List<Cashflow__c> siblings = [SELECT Version_Number__c FROM Cashflow__c WHERE Project__c = :projectId ORDER BY Version_Number__c DESC LIMIT 1];
            if (!siblings.isEmpty() && siblings[0].Version_Number__c != null) {
                cashflow.Version_Number__c = siblings[0].Version_Number__c + 1;
            } else {
                cashflow.Version_Number__c = 1;
            }

            cashflow.Name = getStringValue(details, 'projectName');
            cashflow.Total_Project_Value__c = getDecimalValue(details, 'totalValue');
            cashflow.Estimated_Direct_Payroll__c = getDecimalValue(details, 'totalEstimatedDirectPayroll');
            cashflow.Estimated_Subcontract_Labor__c = getDecimalValue(details, 'totalEstimatedSubcontractLabor');
            cashflow.Estimated_Material_Cost__c = getDecimalValue(details, 'totalEstimatedMaterial');
            cashflow.Estimated_Equipment_Rental_Cost__c = getDecimalValue(details, 'totalEstimatedEquipmentRental');
            cashflow.Estimated_Bond_Premium_Cost__c = getDecimalValue(details, 'totalEstimatedBondPremium');
            cashflow.Estimated_Misc_Expense_Cost__c = getDecimalValue(details, 'totalEstimatedMiscExpenses');
            cashflow.Retainage_Percentage__c = getDecimalValue(details, 'retainagePercent');
            cashflow.Project_Duration_Weeks__c = getDecimalValue(details, 'projectLengthWeeks');
            cashflow.Pay_App_Frequency__c = getStringValue(details, 'payAppFrequency');
            cashflow.Payment_Delay_Days__c = getDecimalValue(details, 'paymentDelayDays');
            cashflow.General_Contractor_Contract_Owner__c = getStringValue(details, 'contractorName');
            cashflow.Estimated_Margin_Percent__c = getDecimalValue(details, 'marginPercent');
            cashflow.Initial_Amount_Received__c = getDecimalValue(details, 'moneyReceivedToDate');
            cashflow.Joint_Checks_Required__c = getBooleanValue(details, 'jointChecks');
            cashflow.Project_Start_Date__c = getDateValue(details, 'startDate');
            cashflow.Projected_Weeks_Outstanding__c = getDecimalValue(details, 'projectLengthWeeks');

            Map<String, Object> computed = getMapValue(details, 'computed');
            cashflow.Forecast_Start_Date__c = getDateValue(computed, 'weekStart');
            cashflow.Forecast_End_Date__c = getDateValue(computed, 'weekdEnd');
            cashflow.Forecast_Date__c = System.today();

            

        }
        return cashflow;
    }
    
    private static PreparedObjects prepareLineItems(Map<String, Object> dynamoData, Id cashflowId) {
        PreparedObjects po = new PreparedObjects();
        Map<String, Cashflow_Line_Item__c> parentMap = new Map<String, Cashflow_Line_Item__c>();

        Map<String, Object> incomeMap = getMapValue(dynamoData, 'income');
        if (incomeMap != null && incomeMap.get('lineItems') instanceof List<Object>) {
            for (Object itemObj : (List<Object>)incomeMap.get('lineItems')) {
                Cashflow_Line_Item_Child__c child = createChildItem((Map<String, Object>)itemObj, 'Project Revenue', (Map<String, Object>)itemObj);
                if(child != null) {
                    po.childrenToUpsert.add(child);
                    aggregateAndLink(child, parentMap, po.junctionsToInsert, cashflowId);
                }
            }
        }

        if (dynamoData.get('expenses') instanceof List<Object>) {
            for (Object expenseGroupObj : (List<Object>)dynamoData.get('expenses')) {
                Map<String, Object> groupData = (Map<String, Object>)expenseGroupObj;
                if (groupData.get('lineItems') instanceof List<Object>) {
                    for (Object itemObj : (List<Object>)groupData.get('lineItems')) {
                        Cashflow_Line_Item_Child__c child = createChildItem((Map<String, Object>)itemObj, 'Project Cost', groupData);
                        if(child != null) {
                             po.childrenToUpsert.add(child);
                             aggregateAndLink(child, parentMap, po.junctionsToInsert, cashflowId);
                        }
                    }
                }
            }
        }
        po.parentsToInsert = parentMap.values();
        return po;
    }

    private static Cashflow_Line_Item_Child__c createChildItem(Map<String, Object> itemData, String type, Map<String, Object> groupData) {
        String nodeId = getStringValue(itemData, 'id');
        if(String.isBlank(nodeId)) return null;
        System.debug('category '+getStringValue(groupData, 'category'));
        Cashflow_Line_Item_Child__c child = new Cashflow_Line_Item_Child__c(
            External_Node_Id__c = nodeId,
            Type__c = type,
            Line_Item_Category__c = normalizeLineItemCategory(getStringValue(groupData, 'category')),
            Planned_Amount__c = getDecimalValue(itemData, 'amount'),
            Week_Start_Date__c = getDateValue(itemData, 'week'),
            Planned_Date__c = getDateValue(itemData, 'weekDue')
        );
        
        if(type == 'Project Cost'){
            child.Parent_Expense_Id__c = getStringValue(groupData, 'id');
            String scheduleValue = getStringValue(groupData, 'schedule');
            child.Expense_Schedule__c = 'variable'.equalsIgnoreCase(scheduleValue) ? 'Variable' : 'Fixed';
            child.Payment_Frequency__c = getStringValue(groupData, 'paymentFrequency');
            child.Parent_Expense_Total_Amount__c = getDecimalValue(groupData, 'amount');
            child.Payment_Terms__c = getStringValue(groupData, 'terms');
            String category = child.Line_Item_Category__c;
            if(category == 'subContractors') child.Subcontractor_Name__c = getStringValue(groupData, 'name');
            else if (category == 'materialOrders') child.Material_Order_Name__c = getStringValue(groupData, 'name');
        }
        return child;
    }

    private static String normalizeLineItemCategory(String rawCategory) {
        if (String.isBlank(rawCategory)) return null;

        String lower = rawCategory.toLowerCase();

        if (lower.contains('payapp')) { rawCategory = 'Pay Application to be Submitted'; }
        if (lower.contains('retainage')) { rawCategory = 'Less Retainage'; }
        if (lower.contains('payrollexpense')) { rawCategory = 'Direct Payroll'; }
        if (lower.contains('subcontractors')) { rawCategory = 'Subcontract Labor'; }
        if (lower.contains('materialorders')) { rawCategory = 'Material'; }
        if (lower.contains('equipmentvendors')) { rawCategory = 'Equipment Rental'; }
        if (lower.contains('miscexpenses')) { rawCategory = 'Miscellaneous Expense'; }
        if (lower.contains('receiptpayapp')) { rawCategory = 'Receipt of Pay App'; }
        if (lower.contains('projectcosts')) { rawCategory = 'Project Costs Paid That Week'; }
        if (lower.contains('bondpremium')) { rawCategory = 'Bond Premium'; }
        
        return rawCategory; 
    }

    
    private static void aggregateAndLink(Cashflow_Line_Item_Child__c child, Map<String, Cashflow_Line_Item__c> parentMap, List<Cashflow_Line_Item_Junction__c> junctions, Id cashflowId) {
        if(child.Week_Start_Date__c == null || String.isBlank(child.Line_Item_Category__c)) return;
        String transactionKey = child.Line_Item_Category__c + '_' + child.Week_Start_Date__c.format();
        String uniqueExternalId = cashflowId + '_' + transactionKey;

        Cashflow_Line_Item__c parent = parentMap.get(transactionKey);
        if (parent == null) {
            parent = new Cashflow_Line_Item__c(
                Cashflow__c = cashflowId,
                External_Week_Id__c = uniqueExternalId,
                Line_Item_Category__c = child.Line_Item_Category__c, 
                Week_Start_Date__c = child.Week_Start_Date__c, 
                Type__c = child.Type__c, 
                Planned_Amount__c = child.Planned_Amount__c
            );
            parentMap.put(transactionKey, parent);
        } else {
            parent.Planned_Amount__c += (child.Planned_Amount__c == null ? 0 : child.Planned_Amount__c);
        }

        Cashflow_Line_Item_Junction__c junction = new Cashflow_Line_Item_Junction__c();
        junction.putSObject('Cashflow_Line_Item_Child__r', new Cashflow_Line_Item_Child__c(External_Node_Id__c = child.External_Node_Id__c));
        junction.putSObject('Cashflow_Line_Item__r', new Cashflow_Line_Item__c(External_Week_Id__c = uniqueExternalId));
        junctions.add(junction);
    }

    /**
    * @description Handles the creation of 'Financing Source' line items with detailed debugging.
    * If a previous cash flow version exists, it clones the items.
    * If not, it creates new items from the project's existing Disbursements and Pay Applications.
    * @param newCashflowId The ID of the newly created Cashflow__c record.
    * @param projectId The ID of the parent Project__c record.
    */
    private static void handleFinancingSources(Id newCashflowId, Id projectId) {
        System.debug('[FinancingSources] Starting process for new Cashflow ID: ' + newCashflowId + ' and Project ID: ' + projectId);

        // Check for a previous version of the cash flow for this project
        System.debug('[FinancingSources] Querying for previous versions with Project ID: ' + projectId + ' and excluding new Cashflow ID: ' + newCashflowId);
        
        // First check if any cashflows exist for this project
        List<Cashflow__c> allProjectCashflows = [
            SELECT Id, Version_Number__c FROM Cashflow__c 
            WHERE Project__c = :projectId
            ORDER BY Version_Number__c DESC
        ];
        System.debug('[FinancingSources] All cashflows for this project: ' + allProjectCashflows);
        
        List<Cashflow__c> previousVersions = [
            SELECT Id, Version_Number__c FROM Cashflow__c 
            WHERE Project__c = :projectId AND Id != :newCashflowId
            ORDER BY Version_Number__c DESC LIMIT 1
        ];
        System.debug('[FinancingSources] Project ID: ' + projectId);
        System.debug('[FinancingSources] Found ' + previousVersions.size() + ' previous version(s).');
        System.debug('[FinancingSources] Previous versions found: ' + previousVersions);

        if (!previousVersions.isEmpty()) {
            // --- SCENARIO 1: A previous version exists. Clone its 'Financing Source' items. ---
            Id previousCashflowId = previousVersions[0].Id;
            System.debug('[FinancingSources] CLONING MODE from Cashflow ID: ' + previousCashflowId);

            List<Cashflow_Line_Item__c> parentsToClone = [
                SELECT Id, Type__c, Line_Item_Category__c, Planned_Amount__c, Week_Start_Date__c, External_Week_Id__c, Financing_Source__c,
                    (SELECT Id, Disbursement__c FROM Cashflow_Weekly_Line_Disbursements__r),
                    (SELECT Id, Pay_Application__c FROM Cashflow_Weekly_Line_Pay_Applications__r)
                FROM Cashflow_Line_Item__c
                WHERE Cashflow__c = :previousCashflowId AND Type__c = 'Financing Source'
            ];
            
            // Also query for junction records directly to ensure we have them
            List<Cashflow_Weekly_Line_Disbursement__c> existingDisbursementJunctions = [
                SELECT Id, Cashflow_Line_Item__c, Disbursement__c 
                FROM Cashflow_Weekly_Line_Disbursement__c 
                WHERE Cashflow_Line_Item__c IN (SELECT Id FROM Cashflow_Line_Item__c WHERE Cashflow__c = :previousCashflowId AND Type__c = 'Financing Source')
            ];
            
            List<Cashflow_Weekly_Line_Pay_Application__c> existingPayAppJunctions = [
                SELECT Id, Cashflow_Line_Item__c, Pay_Application__c 
                FROM Cashflow_Weekly_Line_Pay_Application__c 
                WHERE Cashflow_Line_Item__c IN (SELECT Id FROM Cashflow_Line_Item__c WHERE Cashflow__c = :previousCashflowId AND Type__c = 'Financing Source')
            ];
            
            System.debug('[FinancingSources] Found ' + existingDisbursementJunctions.size() + ' disbursement junctions and ' + existingPayAppJunctions.size() + ' pay app junctions directly.');
            System.debug('[FinancingSources] Found ' + parentsToClone.size() + ' parent items to clone.');

            if (parentsToClone.isEmpty()) {
                System.debug('[FinancingSources] No parents to clone. Exiting.');
                return;
            }
            System.debug('[FinancingSources] Parents to clone (with children): ' + JSON.serializePretty(parentsToClone));

            List<Cashflow_Line_Item__c> newParents = new List<Cashflow_Line_Item__c>();
            Map<Id, Id> oldToNewParentIdMap = new Map<Id, Id>();

            // First, clone the parent records
            for (Cashflow_Line_Item__c oldParent : parentsToClone) {
                // Manual clone to ensure all fields are properly set
                Cashflow_Line_Item__c newParent = new Cashflow_Line_Item__c(
                    Cashflow__c = newCashflowId,
                    Type__c = oldParent.Type__c,
                    Line_Item_Category__c = oldParent.Line_Item_Category__c,
                    Planned_Amount__c = oldParent.Planned_Amount__c,
                    Week_Start_Date__c = oldParent.Week_Start_Date__c,
                    Financing_Source__c = oldParent.Financing_Source__c,
                    External_Week_Id__c = newCashflowId + '_' + oldParent.Line_Item_Category__c + '_' + (oldParent.Week_Start_Date__c != null ? oldParent.Week_Start_Date__c.format() : '')
                );
                newParents.add(newParent);
                System.debug('[FinancingSources] Created new parent: ' + newParent);
            }
            insert newParents;
            System.debug('[FinancingSources] Inserted ' + newParents.size() + ' new parent records.');

            // Map old parent IDs to new parent IDs
            for(Integer i = 0; i < parentsToClone.size(); i++) {
                oldToNewParentIdMap.put(parentsToClone[i].Id, newParents[i].Id);
            }
            System.debug('[FinancingSources] Mapped old to new parent IDs: ' + oldToNewParentIdMap);

            // Now, clone the child junction records
            List<Cashflow_Weekly_Line_Disbursement__c> newDisbursements = new List<Cashflow_Weekly_Line_Disbursement__c>();
            List<Cashflow_Weekly_Line_Pay_Application__c> newPayApplications = new List<Cashflow_Weekly_Line_Pay_Application__c>();

            // Create a map of old parent IDs to their related disbursement junctions
            Map<Id, List<Cashflow_Weekly_Line_Disbursement__c>> parentToDisbursementJunctions = new Map<Id, List<Cashflow_Weekly_Line_Disbursement__c>>();
            for (Cashflow_Weekly_Line_Disbursement__c junction : existingDisbursementJunctions) {
                if (!parentToDisbursementJunctions.containsKey(junction.Cashflow_Line_Item__c)) {
                    parentToDisbursementJunctions.put(junction.Cashflow_Line_Item__c, new List<Cashflow_Weekly_Line_Disbursement__c>());
                }
                parentToDisbursementJunctions.get(junction.Cashflow_Line_Item__c).add(junction);
            }
            
            // Create a map of old parent IDs to their related pay app junctions
            Map<Id, List<Cashflow_Weekly_Line_Pay_Application__c>> parentToPayAppJunctions = new Map<Id, List<Cashflow_Weekly_Line_Pay_Application__c>>();
            for (Cashflow_Weekly_Line_Pay_Application__c junction : existingPayAppJunctions) {
                if (!parentToPayAppJunctions.containsKey(junction.Cashflow_Line_Item__c)) {
                    parentToPayAppJunctions.put(junction.Cashflow_Line_Item__c, new List<Cashflow_Weekly_Line_Pay_Application__c>());
                }
                parentToPayAppJunctions.get(junction.Cashflow_Line_Item__c).add(junction);
            }

            for (Cashflow_Line_Item__c oldParent : parentsToClone) {
                Id newParentId = oldToNewParentIdMap.get(oldParent.Id);
                System.debug('[FinancingSources] Processing children for old parent ' + oldParent.Id + ', new parent is ' + newParentId);
                
                // Clone disbursement junctions - first try from relationship query
                Boolean disbursementsProcessed = false;
                if (oldParent.Cashflow_Weekly_Line_Disbursements__r != null && !oldParent.Cashflow_Weekly_Line_Disbursements__r.isEmpty()) {
                    System.debug('[FinancingSources] Found ' + oldParent.Cashflow_Weekly_Line_Disbursements__r.size() + ' disbursement junctions to clone from relationship.');
                    for (Cashflow_Weekly_Line_Disbursement__c oldJunction : oldParent.Cashflow_Weekly_Line_Disbursements__r) {
                        Cashflow_Weekly_Line_Disbursement__c newJunction = new Cashflow_Weekly_Line_Disbursement__c(
                            Cashflow_Line_Item__c = newParentId,
                            Disbursement__c = oldJunction.Disbursement__c
                        );
                        newDisbursements.add(newJunction);
                    }
                    disbursementsProcessed = true;
                }
                
                // If no disbursements from relationship, try from direct query
                if (!disbursementsProcessed && parentToDisbursementJunctions.containsKey(oldParent.Id)) {
                    List<Cashflow_Weekly_Line_Disbursement__c> junctions = parentToDisbursementJunctions.get(oldParent.Id);
                    System.debug('[FinancingSources] Found ' + junctions.size() + ' disbursement junctions to clone from direct query.');
                    for (Cashflow_Weekly_Line_Disbursement__c oldJunction : junctions) {
                        Cashflow_Weekly_Line_Disbursement__c newJunction = new Cashflow_Weekly_Line_Disbursement__c(
                            Cashflow_Line_Item__c = newParentId,
                            Disbursement__c = oldJunction.Disbursement__c
                        );
                        newDisbursements.add(newJunction);
                    }
                }

                // Clone pay application junctions - first try from relationship query
                Boolean payAppsProcessed = false;
                if (oldParent.Cashflow_Weekly_Line_Pay_Applications__r != null && !oldParent.Cashflow_Weekly_Line_Pay_Applications__r.isEmpty()) {
                    System.debug('[FinancingSources] Found ' + oldParent.Cashflow_Weekly_Line_Pay_Applications__r.size() + ' pay application junctions to clone from relationship.');
                    for (Cashflow_Weekly_Line_Pay_Application__c oldJunction : oldParent.Cashflow_Weekly_Line_Pay_Applications__r) {
                        Cashflow_Weekly_Line_Pay_Application__c newJunction = new Cashflow_Weekly_Line_Pay_Application__c(
                            Cashflow_Line_Item__c = newParentId,
                            Pay_Application__c = oldJunction.Pay_Application__c
                        );
                        newPayApplications.add(newJunction);
                    }
                    payAppsProcessed = true;
                }
                
                // If no pay apps from relationship, try from direct query
                if (!payAppsProcessed && parentToPayAppJunctions.containsKey(oldParent.Id)) {
                    List<Cashflow_Weekly_Line_Pay_Application__c> junctions = parentToPayAppJunctions.get(oldParent.Id);
                    System.debug('[FinancingSources] Found ' + junctions.size() + ' pay application junctions to clone from direct query.');
                    for (Cashflow_Weekly_Line_Pay_Application__c oldJunction : junctions) {
                        Cashflow_Weekly_Line_Pay_Application__c newJunction = new Cashflow_Weekly_Line_Pay_Application__c(
                            Cashflow_Line_Item__c = newParentId,
                            Pay_Application__c = oldJunction.Pay_Application__c
                        );
                        newPayApplications.add(newJunction);
                    }
                }
            }
            
            if (!newDisbursements.isEmpty()){
                System.debug('[FinancingSources] Attempting to insert ' + newDisbursements.size() + ' new disbursement junctions.');
                insert newDisbursements;
                System.debug('[FinancingSources] Successfully inserted disbursement junctions: ' + newDisbursements);
            } else {
                System.debug('[FinancingSources] WARNING: No disbursement junctions to insert!');
            }
            
            if (!newPayApplications.isEmpty()){
                System.debug('[FinancingSources] Attempting to insert ' + newPayApplications.size() + ' new pay application junctions.');
                insert newPayApplications;
                System.debug('[FinancingSources] Successfully inserted pay application junctions: ' + newPayApplications);
            } else {
                System.debug('[FinancingSources] WARNING: No pay application junctions to insert!');
            }
            
            // Verify what was created
            List<Cashflow_Line_Item__c> createdParents = [
                SELECT Id, Type__c, Line_Item_Category__c, 
                    (SELECT Id FROM Cashflow_Weekly_Line_Disbursements__r),
                    (SELECT Id FROM Cashflow_Weekly_Line_Pay_Applications__r)
                FROM Cashflow_Line_Item__c 
                WHERE Cashflow__c = :newCashflowId AND Type__c = 'Financing Source'
            ];
            
            System.debug('[FinancingSources] VERIFICATION: Found ' + createdParents.size() + ' financing source parents in the new cashflow.');
            for (Cashflow_Line_Item__c parent : createdParents) {
                Integer disbCount = (parent.Cashflow_Weekly_Line_Disbursements__r != null) ? parent.Cashflow_Weekly_Line_Disbursements__r.size() : 0;
                Integer payAppCount = (parent.Cashflow_Weekly_Line_Pay_Applications__r != null) ? parent.Cashflow_Weekly_Line_Pay_Applications__r.size() : 0;
                System.debug('[FinancingSources] VERIFICATION: Parent ' + parent.Id + ' has ' + disbCount + ' disbursement junctions and ' + payAppCount + ' pay app junctions.');
            }
            
            // If we cloned from a previous version, we're done - don't also create from scratch
            return;

        } else {
            // --- SCENARIO 2: No previous version. Create items from scratch. ---
            System.debug('[FinancingSources] CREATE FROM SCRATCH MODE.');
            
            // Check if we already created financing sources through cloning
            List<Cashflow_Line_Item__c> existingFinancingSources = [
                SELECT Id FROM Cashflow_Line_Item__c 
                WHERE Cashflow__c = :newCashflowId AND Type__c = 'Financing Source'
            ];
            
            // If we already have financing sources from cloning, don't create new ones
            if (!existingFinancingSources.isEmpty()) {
                System.debug('[FinancingSources] Found ' + existingFinancingSources.size() + ' existing financing sources from cloning. Skipping creation of new ones.');
                return;
            }
            
            List<Disbursement__c> projectDisbursements = [
                SELECT Id, Amount_Approved__c, CreatedDate FROM Disbursement__c WHERE Project__c = :projectId
            ];
            List<Pay_Application__c> projectPayApps = [
                SELECT Id, Amount_applied_to_loan__c, CreatedDate FROM Pay_Application__c WHERE Project__c = :projectId
            ];
            System.debug('[FinancingSources] Found ' + projectDisbursements.size() + ' disbursements and ' + projectPayApps.size() + ' pay applications for the project.');

            List<Cashflow_Line_Item__c> parentsToCreate = new List<Cashflow_Line_Item__c>();
            Map<Id, Cashflow_Line_Item__c> sourceIdToCliMap = new Map<Id, Cashflow_Line_Item__c>();

            // Prepare parent CLI for each Disbursement
            for(Disbursement__c disb : projectDisbursements) {
                Cashflow_Line_Item__c cli = new Cashflow_Line_Item__c(
                    Cashflow__c = newCashflowId, Type__c = 'Financing Source', Line_Item_Category__c = 'Disbursement', 
                    Financing_Source__c = 'Disbursement',
                    Planned_Amount__c = disb.Amount_Approved__c, Week_Start_Date__c = disb.CreatedDate.date().toStartOfWeek()
                );
                parentsToCreate.add(cli);
                sourceIdToCliMap.put(disb.Id, cli);
            }
            
            // Prepare parent CLI for each Pay Application
            for(Pay_Application__c pa : projectPayApps) {
                Cashflow_Line_Item__c cli = new Cashflow_Line_Item__c(
                    Cashflow__c = newCashflowId, Type__c = 'Financing Source', Line_Item_Category__c = 'Pay Application',
                    Financing_Source__c = 'Pay Application', Planned_Amount__c = pa.Amount_applied_to_loan__c, 
                    Week_Start_Date__c = pa.CreatedDate.date().toStartOfWeek()
                );
                parentsToCreate.add(cli);
                sourceIdToCliMap.put(pa.Id, cli);
            }

            if (parentsToCreate.isEmpty()){
                System.debug('[FinancingSources] No source disbursements or pay apps found. Exiting.');
                return;
            }

            System.debug('[FinancingSources] Prepared ' + parentsToCreate.size() + ' new parent line items to insert.');
            insert parentsToCreate;
            System.debug('[FinancingSources] Inserted new parents. Source ID to CLI Map after insert: ' + sourceIdToCliMap);


            // Prepare junction objects now that parents have IDs
            List<Cashflow_Weekly_Line_Disbursement__c> disbursementsToCreate = new List<Cashflow_Weekly_Line_Disbursement__c>();
            for (Disbursement__c disb : projectDisbursements) {
                Id parentCliId = sourceIdToCliMap.get(disb.Id).Id;
                disbursementsToCreate.add(new Cashflow_Weekly_Line_Disbursement__c(
                    Cashflow_Line_Item__c = parentCliId,
                    Disbursement__c = disb.Id
                ));
            }
            System.debug('disbursementsToCreate -> ' + disbursementsToCreate);

            List<Cashflow_Weekly_Line_Pay_Application__c> payAppsToCreate = new List<Cashflow_Weekly_Line_Pay_Application__c>();
            for (Pay_Application__c pa : projectPayApps) {
                Id parentCliId = sourceIdToCliMap.get(pa.Id).Id;
                payAppsToCreate.add(new Cashflow_Weekly_Line_Pay_Application__c(
                    Cashflow_Line_Item__c = parentCliId,
                    Pay_Application__c = pa.Id
                ));
            }
            System.debug('payAppsToCreate -> ' + payAppsToCreate);

            if(!disbursementsToCreate.isEmpty()){
                System.debug('[FinancingSources] Attempting to insert ' + disbursementsToCreate.size() + ' new disbursement junctions.');
                insert disbursementsToCreate;
            }
            if(!payAppsToCreate.isEmpty()){
                System.debug('[FinancingSources] Attempting to insert ' + payAppsToCreate.size() + ' new pay app junctions.');
                insert payAppsToCreate;
            }
        }
        System.debug('[FinancingSources] Process finished.');
    }
    
    // --- GENERIC & HELPER METHODS ---

    public static void assignFieldValue(SObject record, String fieldApiName, Object rawValue) {
        if (String.isBlank(fieldApiName) || rawValue == null) return;
        
        String stringValue = String.valueOf(rawValue);
        Schema.SObjectField field = SObjectType.Cashflow__c.fields.getMap().get(fieldApiName.toLowerCase());
        if(field == null) field = SObjectType.Cashflow_Line_Item_Child__c.fields.getMap().get(fieldApiName.toLowerCase());
        if(field == null) return; // Field not on expected objects

        Schema.DisplayType sfType = field.getDescribe().getType();

        try {
            if (String.isBlank(stringValue) && field.getDescribe().isNillable()) {
                 record.put(fieldApiName, null);
            } else if (sfType == Schema.DisplayType.BOOLEAN) {
                record.put(fieldApiName, 'yes'.equalsIgnoreCase(stringValue) || 'true'.equalsIgnoreCase(stringValue));
            } else if (sfType == Schema.DisplayType.DATE) {
                record.put(fieldApiName, Date.valueOf(stringValue));
            } else if (sfType == Schema.DisplayType.DATETIME) {
                record.put(fieldApiName, Datetime.valueOf(stringValue.replace(' ', 'T')));
            } else if (sfType == Schema.DisplayType.CURRENCY || sfType == Schema.DisplayType.DOUBLE || sfType == Schema.DisplayType.PERCENT || sfType == Schema.DisplayType.INTEGER || sfType == Schema.DisplayType.LONG) {
                record.put(fieldApiName, Decimal.valueOf(stringValue));
            } else {
                record.put(fieldApiName, stringValue);
            }
        } catch (Exception e) {
            DebugLogUtil.error('Failed to assign value "' + stringValue + '" to field ' + fieldApiName, e, LOG_TAGS);
        }
    }

    private static Map<String, Object> getMapValue(Map<String, Object> data, String key) {
        if (data != null && data.get(key) instanceof Map<String, Object>) {
            return (Map<String, Object>) data.get(key);
        }
        return null;
    }

    private static String getStringValue(Map<String, Object> data, String key) {
        return (data != null && data.containsKey(key) && data.get(key) != null) ? String.valueOf(data.get(key)) : null;
    }

    public static Decimal getDecimalValue(Map<String, Object> data, String key) {
        Object val = (data != null) ? data.get(key) : null;
        if (val instanceof Decimal || val instanceof Double || val instanceof Integer) { return (Decimal)val; }
        try { return String.isBlank(String.valueOf(val)) ? null : Decimal.valueOf(String.valueOf(val)); }
        catch (Exception e) { return null; }
    }

    private static Date getDateValue(Map<String, Object> data, String key) {
        Object val = (data != null) ? data.get(key) : null;
        try { return String.isBlank(String.valueOf(val)) ? null : Date.valueOf(String.valueOf(val)); }
        catch (Exception e) { return null; }
    }

    public static Boolean getBooleanValue(Map<String, Object> data, String key) {
        String val = getStringValue(data, key);
        return 'yes'.equalsIgnoreCase(val) || 'true'.equalsIgnoreCase(val);
    }
    
    // --- OTHER PUBLIC METHODS (UNCHANGED) ---
    @AuraEnabled(cacheable=true)
    public static List<Cash_Flow_Session__c> getRecords(){
        return [SELECT Id, Name, UpdatedAt__c, LatestStep__c, DeletedAt__c, AccountId__c, UserId__c, Configuration__c,
                CurrentStep__c, SessionId__c, VideosProgress__c, CompletedAt__c, CreatedAt__c, Processed_JSON__c, Processed_Configuration__c, Processed_Configurationv2__c
                FROM Cash_Flow_Session__c Order by CreatedAt__c DESC LIMIT 50000];
    }
    
    @AuraEnabled
    public static Map<String,Object> shareSessionWithApex(String endpointUrl) {
       final String METHOD_NAME = 'shareSessionWithApex';
        Map<String, Object> params = new Map<String, Object>{'endpointUrl' => endpointUrl};
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, params, API_TAGS);
        
        Map<String,Object> returnMap = new Map<String, Object>();
        try {
            HttpRequest req = new HttpRequest();
            req.setEndpoint(endpointUrl);
            req.setMethod('POST');
            Http http = new Http();
            
            HttpResponse res = http.send(req);
            
            returnMap.put('statusCode', res.getStatusCode());
            returnMap.put('status', res.getStatus());
            returnMap.put('body', res.getBody());
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Exception during callout.', e, API_TAGS);
            throw new AuraHandledException('Error in sharing session: ' + e.getMessage());
        }
        return returnMap;
    }
    
    public class MappingRule {
    public String dynamoDbId;
    public String salesforceObject;
    public String salesforceFieldApiName;
    public String fieldName;
    public String intakeType;
    public String intakeFormPageSection;
}
}