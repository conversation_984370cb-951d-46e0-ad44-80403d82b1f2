public class FlowRequestItemInput {
    @AuraEnabled public String Name;
    @AuraEnabled public String descriptionWork;
    @AuraEnabled public Date invoiceDate;
    @AuraEnabled public Decimal invoiceAmount;
    @AuraEnabled public String invoice;
    @AuraEnabled public Date invoiceDueDate;
    @AuraEnabled public List<String> contentDocumentIds;

    public FlowRequestItemInput() {
        this.contentDocumentIds = new List<String>();
    }
}