// Make this a top-level class (not inside DropboxControllerTest)
// Ensure API Version is set appropriately in the .xml meta file
@isTest // Allow test methods to construct it
public class MockHttpResponseGenerator implements HttpCalloutMock {
    protected Integer statusCode;
    protected String status;
    protected String body;
    protected Map<String, String> headers;
    protected Boolean expectNamespaceHeader = false; // Flag for header check

    public MockHttpResponseGenerator(Integer statusCode, String status, String body, Map<String, String> headers) {
        this.statusCode = statusCode;
        this.status = status;
        this.body = body;
        this.headers = headers;
    }

    // Setter for the header check flag
    public void setExpectNamespaceHeader(Boolean expect) {
        this.expectNamespaceHeader = expect;
    }

    public HTTPResponse respond(HTTPRequest req) {
        HttpResponse res = new HttpResponse();
        res.setStatusCode(this.statusCode);
        res.setStatus(this.status);
        res.setBody(this.body);
        if (this.headers != null) {
            for (String key : this.headers.keySet()) {
                res.setHeader(key, this.headers.get(key));
            }
        }

        // Verify Namespace Id header presence based on instance field
        String namespaceHeader = req.getHeader('Dropbox-API-Path-Root');
        String rootId = '11709235889'; // Get this dynamically if possible, or hardcode matching controller

        if (this.expectNamespaceHeader) {
            System.assertNotEquals(null, namespaceHeader, 'Expected Dropbox-API-Path-Root header but was missing. Endpoint: ' + req.getEndpoint());
            // Add null check for contains
             if(namespaceHeader != null){
                 System.assert(namespaceHeader.contains(rootId), 'Namespace header did not contain correct root ID. Header: ' + namespaceHeader);
             }
        } else {
            System.assertEquals(null, namespaceHeader, 'Did not expect Dropbox-API-Path-Root header but it was present. Endpoint: ' + req.getEndpoint());
        }

        return res;
    }
}