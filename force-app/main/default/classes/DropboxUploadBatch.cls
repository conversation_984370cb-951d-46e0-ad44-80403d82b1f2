global class DropboxUploadBatch implements Database.Batchable<SObject>, Database.Stateful, Database.AllowsCallouts {
        global final String parentId;
        global final String folderPath;
        global final String parentObjectType;
        public Boolean logSuccess = true;
        public List<Custom_Exception__c> customExceptions = new List<Custom_Exception__c>();
        public List<String> asyncJobIds = new List<String>();
        Set<Id> contentDocumentIds = new Set<Id>();
    
        global DropboxUploadBatch(String folderPath) {
            //this.parentId = parentId;
            this.folderPath = folderPath;
            //this.parentObjectType = parentId.startsWith('006') ? 'Opportunity' : parentId.startsWith('a09') ? 'Project__c' : 'Disbursement_Request__c';
    
        }
        
        global DropboxUploadBatch(String parentId, String folderPath) {
            this.parentId = parentId;
            this.folderPath = folderPath;
            this.parentObjectType = parentId.startsWith('006') ? 'Opportunity' : parentId.startsWith('a09') ? 'Project__c' : 'Disbursement_Request__c';
    
        }
    
        global Database.QueryLocator start(Database.BatchableContext BC) {
            String query = 'SELECT ContentDocumentId, LinkedEntityId ' +
                           'FROM ContentDocumentLink ' +
                           'WHERE LinkedEntityId = :parentId';
            return Database.getQueryLocator(query);
        }
    
        global void execute(Database.BatchableContext BC, List<ContentDocumentLink> scope) {
            List<ContentVersion> cvsToUpdate = new List<ContentVersion>();
            List<ContentVersion> filteredContentVersions = new List<ContentVersion>();
            
            try {
                for (ContentDocumentLink cdl : scope) {
                    contentDocumentIds.add(cdl.ContentDocumentId);
                }
        
                if (!contentDocumentIds.isEmpty()) {
                    List<ContentVersion> contentVersions = [
                        SELECT Id, Title, ContentDocumentId, FileExtension 
                        FROM ContentVersion 
                        WHERE ContentDocumentId IN :contentDocumentIds 
                        AND IsLatest = true
                        AND ContentDocument.ContentAssetId = null
                        AND Dropbox_Uploaded_Date__c = null
						AND ( Dropbox_Sync_Status__c = null OR Dropbox_Sync_Status__c = 'Not Started')
                    ];
                    
                     // Filter out records that should not be processed
                    for (ContentVersion cv : contentVersions) {
                        if (!shouldExclude(cv)) {
                            filteredContentVersions.add(cv);
                        }
                    }
        
                    Map<Id,String> cvAndCDLinkMap = getDownloadLinks(filteredContentVersions);
                    
                    for (ContentVersion cv : filteredContentVersions) {
                        String fileUrl = cvAndCDLinkMap.get(cv.Id);
                        String fullFileName = cv.Title + '.' + cv.FileExtension;
                        fullFileName = fullFileName.replace('/', '-');
                        if(fileUrl != null) {
                            Map<String,Object> returnMap = saveFileToDropbox(fullFileName, fileUrl, cv.Id, parentId);
                            if((Boolean)returnMap.get('success') == true) {
                                cv.Dropbox_Async_Job_Id__c = (String) returnMap.get('jobId');
                                cv.Dropbox_Sync_Status__c = 'Processing - Waiting Dropbox Confirmation';
                                cvsToUpdate.add(cv);
                            } else {
                                cv.Dropbox_Sync_Status__c = 'Failed';
                                cvsToUpdate.add(cv);
                            }
                        }
                    }
        
                    // Update the Most_Recent_Dropbox_Upload_Datetime__c field on the parent record
                    // SObject parentRecord = getParentRecord(parentId);
                    // parentRecord.put('Most_Recent_Dropbox_Upload_Datetime__c', System.now());
                    // update parentRecord;
                    
                    if(!cvsToUpdate.isEmpty()) {
                        update cvsToUpdate;
                    }
                    if(!customExceptions.isEmpty()) {
                        insert customExceptions;
                    }
                }
            } catch(Exception e) {
                
                if(!cvsToUpdate.isEmpty()) {
                    update cvsToUpdate;
                }
                
                if(!customExceptions.isEmpty()) {
                    insert customExceptions;
                }
                
            }
            
        }
    
    	private Boolean shouldExclude(ContentVersion cv) {
            return cv.FileExtension == 'snote';
        }
    
        public Map<Id,String> getDownloadLinks(List<ContentVersion> contentVersions ) {
            Map<Id,String> cvAndCDLinkMap = new Map<Id,String>();
            
            for(ContentDistribution cd : [SELECT Id, Name, ContentDocumentId, ContentVersionId, DistributionPublicUrl, ContentDownloadUrl FROM ContentDistribution WHERE ContentVersionId IN :contentVersions]) {
                cvAndCDLinkMap.put(cd.ContentVersionId, cd.ContentDownloadUrl);
            }
            
            return cvAndCDLinkMap;
                
        }
        
         public Map<Id,String> getDownloadLinks(List<String> contentVersions ) {
            Map<Id,String> cvAndCDLinkMap = new Map<Id,String>();
            
            for(ContentDistribution cd : [SELECT Id, Name, ContentDocumentId, ContentVersionId, DistributionPublicUrl, ContentDownloadUrl FROM ContentDistribution WHERE ContentVersionId IN :contentVersions]) {
                cvAndCDLinkMap.put(cd.ContentVersionId, cd.ContentDownloadUrl);
            }
            
            return cvAndCDLinkMap;
                
        }
        
        global void finish(Database.BatchableContext BC) {
           
        }
    
        private String generateFileUrl(Id contentVersionId) {
            String baseUrl = URL.getSalesforceBaseUrl().toExternalForm();
            return baseUrl + '/sfc/servlet.shepherd/version/download/' + contentVersionId;
        }

        public Map<String,Object> saveFileToDropbox(String fileName, String fileUrl, Id contentVersionId) {
			return saveFileToDropbox(fileName, fileUrl, contentVersionId, null);
        }
        
        public Map<String,Object> saveFileToDropbox(String fileName, String fileUrl, Id contentVersionId, String parentId) {
    
            Map<String,Object> returnMap = new Map<String,Object>();
            
            returnMap = DropboxController.uploadFileToDropboxByUrl(folderPath, fileName, fileUrl, contentVersionId, parentId);
            
            HttpResponse res = (HttpResponse) returnMap.get('res');
            Custom_Exception__c ceObj = (Custom_Exception__c) returnMap.get('log');
            customExceptions.add(ceObj);
            
            if (res.getStatusCode() == 200) {
                Map<String, Object> resMap = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                if (resMap.containsKey('async_job_id')) {
                    returnMap.put('jobId', (String) resMap.get('async_job_id'));
                }
                
                returnMap.put('success', true);            
            }
            
            if(res.getStatusCode() != 200) {
                returnMap.put('success', false);
            }
            
            return returnMap;
            
        }
    
        private SObject getParentRecord(Id parentId) { 
            
            if (parentObjectType == 'Opportunity') {
                return [SELECT Id FROM Opportunity WHERE Id = :parentId LIMIT 1];
            } else if (parentObjectType == 'Project__c') {
                return [SELECT Id FROM Project__c WHERE Id = :parentId LIMIT 1];
            } else {
                return [SELECT Id FROM Disbursement_Request__c WHERE Id = :parentId LIMIT 1];
            }
        }
        
    }