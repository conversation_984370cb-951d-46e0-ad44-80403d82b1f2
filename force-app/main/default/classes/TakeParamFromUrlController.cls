@SuppressWarnings('PMD')
public class TakeParamFromUrlController {
    
    @AuraEnabled
    public static ProjectWrapper getProjectObj(String projectId) {
        ProjectWrapper response = new ProjectWrapper();
        String currentUserId = UserInfo.getUserId();
        // Fetch the current user's ContactId
        User currentUser = [SELECT ContactId FROM User WHERE Id = :currentUserId LIMIT 1];


        Project__c projectRecord = [SELECT Loan_Opportunity__c,Loan_Opportunity__r.Name,Loan_Opportunity__r.Loan_Amount_Requested__c,Loan_Opportunity__r.Effective_Date_of_Loan_Docs__c, Account_Name__c, Account_Name__r.Name, Enter_name_of_Project_Owner__c, Name, Project_Number__c, (Select General_Contractor_Contract_Owner__c from Disbursement_Requests1_del__r ORDER BY CreatedDate DESC Limit 1) FROM Project__c WHERE Id = :projectId Limit 1];

        response.project = projectRecord; 

        if (projectRecord.Account_Name__c != null) {
            List<Contact> contacts = [
                SELECT Name, Id,Title FROM Contact 
                WHERE AccountId = :projectRecord.Account_Name__c
            ];
            
            Contact selectedContact = null;
            
            for (Contact c : contacts) {
                if (currentUser.ContactId != null && c.Id == currentUser.ContactId) {
                    selectedContact = c;
                    break; // Stop looping if we find the current user's contact
                }
            }
            
            // If no match, just use the first contact found
            if (selectedContact == null && !contacts.isEmpty()) {
                selectedContact = contacts[0];
            }
        
            response.contact = selectedContact;
        }

        return response;
    }

    public class ProjectWrapper {
        @AuraEnabled 
        public Project__c project;
        @AuraEnabled 
        public Contact contact;
    }
}