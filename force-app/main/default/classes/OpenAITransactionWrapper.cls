public class OpenAITransactionWrapper {
    
    // Inner class representing a transaction to be classified
    public class TransactionWrapper {
        public Decimal amount;
        public String description;
        public String type; 
        public String salesforce_id;
    }
    
    // Inner class representing a message in the OpenAI request
    public class MessageWrapper {
        public String content;
        public String role;
    }
    
    public class OpenAIRequest {
        public List<MessageWrapper> messages;
        public Decimal temperature;
        public Integer max_tokens;
        public Map<String, Object> response_format; 
        
        public OpenAIRequest(List<TransactionWrapper> transactions, String systemMsgContext, Decimal maxToken, Decimal temp) {
            messages = new List<MessageWrapper>();
            
            // System message providing context and instructions
            MessageWrapper systemMessage = new MessageWrapper();
            systemMessage.content = systemMsgContext; 
            systemMessage.role = 'system';
            messages.add(systemMessage);
            
            // User message containing the transaction data
            MessageWrapper userMessage = new MessageWrapper();
            // Serialize the list of transactions into a JSON string for the content
            userMessage.content = JSON.serialize(transactions);
            userMessage.role = 'user';
            messages.add(userMessage);
            
            this.temperature = temp;
            this.max_tokens = Integer.valueOf(maxToken);
            
            Map<String, Object> schemaDefinition = getResponseJsonSchema();
            
            // Create the wrapper object that goes directly inside 'json_schema'
            Map<String, Object> jsonSchemaPayload = new Map<String, Object>();
            jsonSchemaPayload.put('name', 'classify_transactions'); // Mandatory name for the schema object
            jsonSchemaPayload.put('description', 'Schema definition for an array of classified financial transactions.'); // Optional top-level description
            

            // Put the actual schema definition (the array structure) INSIDE a key named "schema"
            jsonSchemaPayload.put('schema', schemaDefinition);
            
            // Configure the final response_format map for the API request
            this.response_format = new Map<String, Object>{
                'type' => 'json_schema',
                'json_schema' => jsonSchemaPayload
            };
        }
    }
    
    
    // --- IMPORTANT ---
    // getResponseJsonSchema() method itself should return the schema describing the *output data*
    // (i.e., the array structure). It should NOT include the 'name' itself.
    // The 'name' is added *around* this schema in the constructor, as shown above.
    // getResponseJsonSchema() method remains the same - it defines the ACTUAL output schema structure
    public static Map<String, Object> getResponseJsonSchema() {

        // 1. Define the schema for the array elements (individual transaction objects)
        Map<String, Object> transactionItemSchema = new Map<String, Object>{
            'type' => 'object',
            'properties' => new Map<String, Object>{
                'ai_category' => new Map<String, Object>{
                    'type' => 'string',
                    'description' => 'The single most appropriate classified category for the transaction.',
                    'enum' => new List<String>{
                        'CC/Auto', 'Check', 'Debit Xfer', 'MCA’s',
                        'Mobile Xfer', 'NSF / OD Fees', 'Overhead', 'Payroll',
                        'Personal', 'SSV', 'Travel'
                    }
                },
                'transaction_type' => new Map<String, Object>{
                    'type' => 'string',
                    'description' => 'The type of transaction, should be debit.',
                    'enum' => new List<String>{'debit'} // Enforce 'debit'
                },
                'salesforce_id' => new Map<String, Object>{
                    'type' => new List<String>{'string', 'null'}, 
                    'description' => 'The corresponding Salesforce ID if available, otherwise null.'
                },
                'confidence_score' => new Map<String, Object>{
                    'type' => 'integer',
                    'description' => 'Confidence score from 1 to 10.',
                    'minimum' => 1,
                    'maximum' => 10
                },
                'ai_reasoning' => new Map<String, Object>{
                    'type' => new List<String>{'string', 'null'}, 
                    'description' => 'Reason of choosing a specific AI category'
                }
            },
            'required' => new List<String>{ // Properties required for each object in the array
                'ai_category', 'transaction_type', 'salesforce_id', 'confidence_score', 'ai_reasoning'
            }
        };

        // 2. Define the schema for the array itself
        Map<String, Object> arraySchema = new Map<String, Object>{
            'type' => 'array',
            'description' => 'An array of classified transaction objects based on the provided input.',
            'items' => transactionItemSchema // Reference the item schema defined above
        };

        // 3. Define the TOP-LEVEL object schema required by the API
        Map<String, Object> rootObjectSchema = new Map<String, Object>{
            'type' => 'object',
            'description' => 'Wrapper object containing the list of classified transactions.', // Optional description for the wrapper
            'properties' => new Map<String, Object>{
                 // Define a property within the object to hold the array
                 // Use a descriptive name like "transactions" or "results" or "classified_transactions"
                'classified_transactions' => arraySchema
            },
            // Make the property containing the array required
            'required' => new List<String>{'classified_transactions'}
        };

        // Return the root object schema
        return rootObjectSchema;
    }

    
    // Inner class for parsing the OpenAI Response
    // This structure might need adjustment based on the actual API response envelope
    // when using response_format. Usually, the JSON content is inside choices[0].message.content
    public class OpenAIResponse {
        // The structure of 'choices' might vary slightly depending on API version/model.
        // Often contains a 'message' object which then has 'content'.
        public List<ChoiceWrapper> choices;
    }
    
    public class ChoiceWrapper {
        public MessageWrapper message;
        public String finish_reason;
    }
    
    // Note: Removed the following inner classes as they were related to 'data_sources'
    // public class AuthenticationWrapper { ... }
    // public class ParametersWrapper { ... }
    // public class DataSourceWrapper { ... }
}