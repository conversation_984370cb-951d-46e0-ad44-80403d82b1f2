@SuppressWarnings('PMD')
public with sharing class MFChatterEmailHandler implements Messaging.InboundEmailHandler {
    public Messaging.InboundEmailResult handleInboundEmail(
        Messaging.InboundEmail email,
        Messaging.InboundEnvelope envelope
    ) {
        System.debug('yo');
        Messaging.InboundEmailResult result = new Messaging.InboundEmailResult();
        
        // Capture email details
        String fromName       = email.fromName;  
        String emailBody      = email.plainTextBody;
        String subject        = email.Subject;
        String fromAddress    = email.fromAddress;
        List<String> ccAddresses  = email.ccAddresses;
        List<String> toAddresses  = email.toAddresses;
        
        // Extract feedItemID and userID from the email body
        Map<String, String> parsedData = parseEmailBody(emailBody);
        String cleanedBody = extractCleanedBody(emailBody);
        
        if (parsedData.containsKey('feedItemID') && parsedData.containsKey('userID')) {
            String feedItemID = parsedData.get('feedItemID');   
            String userID     = parsedData.get('userID');
            
            try {
                // Query the target FeedItem
                FeedItem feedItem = [
                    SELECT Id, ParentId
                    FROM FeedItem
                    WHERE Id = :feedItemID
                    LIMIT 1
                ];
                
                if (feedItem == null) {
                    System.debug('No feed item found for the given ID.');
                    return result;
                }
                
                // Process attachments: create ContentVersion(s) if any attachments are present
                List<ContentVersion> contentVersionsToInsert = new List<ContentVersion>();
                
                // Process binary attachments
                if (email.binaryAttachments != null && !email.binaryAttachments.isEmpty()) {
                    for (Messaging.InboundEmail.BinaryAttachment bAttach : email.binaryAttachments) {
                        ContentVersion cv = new ContentVersion();
                        cv.Title          = bAttach.fileName;
                        cv.PathOnClient   = bAttach.fileName;
                        cv.VersionData    = bAttach.body;
                        cv.IsMajorVersion = true;
                        contentVersionsToInsert.add(cv);
                    }
                }
                
                // Process text attachments
                if (email.textAttachments != null && !email.textAttachments.isEmpty()) {
                    for (Messaging.InboundEmail.TextAttachment tAttach : email.textAttachments) {
                        ContentVersion cv = new ContentVersion();
                        cv.Title          = tAttach.fileName;
                        cv.PathOnClient   = tAttach.fileName;
                        cv.VersionData    = Blob.valueOf(tAttach.body);
                        cv.IsMajorVersion = true;
                        contentVersionsToInsert.add(cv);
                    }
                }
                
                List<ContentVersion> insertedCVList = new List<ContentVersion>();
                if (!contentVersionsToInsert.isEmpty()) {
                    insert contentVersionsToInsert;
                    // Retrieve the newly inserted ContentVersions to get their IDs
                    insertedCVList = [
                        SELECT Id, ContentDocumentId
                        FROM ContentVersion
                        WHERE Id IN :contentVersionsToInsert
                    ];
                }
                
                // Create a FeedComment linking to the feed item
                List<FeedComment> commentsToInsert = new List<FeedComment>();
                
                if (!insertedCVList.isEmpty()) {
                    // First attachment with the main comment
                    FeedComment newComment = new FeedComment();
                    newComment.FeedItemId  = feedItemID;    
                    newComment.CommentBody = cleanedBody;
                    newComment.CreatedById = userID;        
                    newComment.RelatedRecordId = insertedCVList[0].Id;
                    commentsToInsert.add(newComment);
                    
                    // Additional comments for other attachments
                    for (Integer i = 1; i < insertedCVList.size(); i++) {
                        FeedComment additionalComment = new FeedComment();
                        additionalComment.FeedItemId  = feedItemID;   
                        additionalComment.CommentBody = '';  
                        additionalComment.CreatedById = userID;
                        additionalComment.RelatedRecordId = insertedCVList[i].Id;
                        commentsToInsert.add(additionalComment);
                    }
                } else {
                    // No attachments, just create a single comment
                    FeedComment newComment = new FeedComment();
                    newComment.FeedItemId  = feedItemID;
                    newComment.CommentBody = cleanedBody;
                    newComment.CreatedById = userID;
                    commentsToInsert.add(newComment);
                }
                
                insert commentsToInsert;
                
                // Construct the log message with all details, including attachment IDs
                String logMessage = 'Inbound Email Received - ' +
                    'Sender: ' + fromName + ' (' + fromAddress + '); ' +
                    'Subject: ' + subject + '; ' +
                    'Body: ' + cleanedBody + '; ';
                
                if (!insertedCVList.isEmpty()) {
                    List<String> attachmentIDs = new List<String>();
                    for (ContentVersion cv : insertedCVList) {
                        attachmentIDs.add(cv.Id);
                    }
                    logMessage += 'Attachment IDs: ' + String.join(attachmentIDs, ', ');
                } else {
                    logMessage += 'No attachments';
                }
                
                // Log the email receipt with Nebula Logger
                Nebula.Logger.info(logMessage)
                    .addTag('Chatter Email Service');
                Nebula.Logger.saveLog();
            } catch (Exception e) {
                System.debug('Error while processing inbound email attachments or comments: ' + e.getMessage());
                // Log the exception with Nebula Logger
                Nebula.Logger.error('Error processing inbound email from ' + fromAddress + ': ' + e.getMessage() + '--' + e.getStackTraceString())
                    .addTag('Chatter Email Service');
                Nebula.Logger.saveLog();
            }
        } else {
            System.debug('No valid thread information found in the email body.');
            Nebula.Logger.warn('Inbound email from ' + fromAddress + ' did not contain valid thread identifiers.')
                .addTag('Chatter Email Service');
            Nebula.Logger.saveLog();
        }
        
        return result;
    }
    
    /**
    * Parses the email body to find the feedItemID and userID in the form :::thread::<feedItemID>::<userID>::
    */
    public static Map<String, String> parseEmailBody(String emailBody) {
        Map<String, String> result = new Map<String, String>();
        if (String.isNotBlank(emailBody)) {
            Pattern pattern = Pattern.compile(':::thread::([^:]+)::([^:]+)::');
            Matcher matcher = pattern.matcher(emailBody);
            if (matcher.find()) {
                result.put('feedItemID', matcher.group(1));
                result.put('userID',     matcher.group(2));
            }
        }
        return result;
    }
    
    /**
    * Extracts the main portion of the email body (removing prior email references).
    * Example: everything before "On Wed, <date> wrote..."
    */
    public static String extractCleanedBody(String emailBody) {
        if (String.isNotBlank(emailBody)) {
            Pattern delimiterPattern = Pattern.compile('(?m)^On\\s\\w{3,},.*$');
            Matcher matcher = delimiterPattern.matcher(emailBody);
            if (matcher.find()) {
                return emailBody.substring(0, matcher.start()).trim();
            }
        }
        return emailBody.trim();
    }
}