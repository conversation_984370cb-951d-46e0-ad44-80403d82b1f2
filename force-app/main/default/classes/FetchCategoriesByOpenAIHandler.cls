@SuppressWarnings('PMD')
public class FetchCategoriesByOpenAIHandler {
    
    @AuraEnabled(cacheable=false)
    public static List<Bank_Transaction__c> getBankTransactions(Id bankAccountId, String filterType) {
        if (bankAccountId == null) {
            throw new IllegalArgumentException('Bank Account ID cannot be empty');
        }
        
        // Fetch valid debit and credit categories from MF_Category__mdt
        Set<String> validCreditCategories = new Set<String>();
        Set<String> validDebitCategories = new Set<String>();
        for (MF_Category__mdt category : [SELECT MasterLabel, Analysis_Type__c FROM MF_Category__mdt]) {
            if (category.Analysis_Type__c == 'Credit') {
                validCreditCategories.add('\'' + String.escapeSingleQuotes(category.MasterLabel) + '\'');
            } else if (category.Analysis_Type__c == 'Debit') {
                validDebitCategories.add('\'' + String.escapeSingleQuotes(category.MasterLabel) + '\'');
            }
        }
        
        // Build dynamic query
        String query = 'SELECT Id, Description__c, Debit__c, Credit__c, AI_Category__c, Category__c, Synced_Status__c ' +
            'FROM Bank_Transaction__c WHERE ';
        query += ' Bank_Account__c = :bankAccountId ';

        if (String.isNotBlank(filterType) && filterType != 'All') {
            if (filterType == 'Uncategorized') {
                query += ' AND (AI_Category__c = NULL OR AI_Category__c = \'\') ';
                /*if (!validDebitCategories.isEmpty()) {
                    query += ' OR (Debit__c != NULL AND AI_Category__c NOT IN (' + String.join(new List<String>(validDebitCategories), ',') + ')) ';
                }
                if (!validCreditCategories.isEmpty()) {
                    query += ' OR (Credit__c != NULL AND AI_Category__c NOT IN (' + String.join(new List<String>(validCreditCategories), ',') + ')) ';
                }*/
            } else if (filterType == 'NoFlinksCategory') {
                query += ' AND (Category__c = NULL OR Category__c = \'\') ';
            } else if (filterType == 'NoMFCategory') {
                query += ' AND (MF_Category__c = NULL OR MF_Category__c = \'\') ';
            }
        }
        
        try {
            System.debug('query -> ' + query);
            return (List<Bank_Transaction__c>) Database.query(query);
        } catch (QueryException e) {
            throw new AuraHandledException('Error querying transactions: ' + e.getMessage() +e.getStackTraceString());
        }
    }
    
    @AuraEnabled
    public static void categorizeTransactions(List<Bank_Transaction__c> transactions) {
        if (transactions == null || transactions.isEmpty()) {
            throw new IllegalArgumentException('Transaction list cannot be empty');
        }
        
        List<OpenAITransactionWrapper.TransactionWrapper> transactionWrappers = new List<OpenAITransactionWrapper.TransactionWrapper>();
        Map<Id, Bank_Transaction__c> recordsToUpdateMap = new Map<Id, Bank_Transaction__c>();
        
        for (Bank_Transaction__c txn : transactions) {
            OpenAITransactionWrapper.TransactionWrapper wrapper = new OpenAITransactionWrapper.TransactionWrapper();
            wrapper.amount = txn.Debit__c != null ? txn.Debit__c : txn.Credit__c;
            wrapper.description = txn.Description__c;
            wrapper.salesforce_id = txn.Id;
            wrapper.type = txn.Debit__c != null ? 'debit' : 'credit';
            transactionWrappers.add(wrapper);
            recordsToUpdateMap.put(txn.Id, txn); 
        }
        
        processTransactionBatch(transactionWrappers, recordsToUpdateMap);
        
        if (!recordsToUpdateMap.isEmpty()) {
            try {
                update recordsToUpdateMap.values();
            } catch (DmlException e) {
                throw new AuraHandledException('Error updating transactions: ' + e.getMessage());
            }
        }
    }
    
    private static void processTransactionBatch(List<OpenAITransactionWrapper.TransactionWrapper> transactionList, Map<Id, Bank_Transaction__c> recordsToUpdateMap) {
        String responseContentJson;
        Integer maxRetries = 1;
        Integer retryCount = 0;
        Boolean success = false;
        
        while (retryCount <= maxRetries && !success) {
            try {
                // 1. Call OpenAI - This already extracts the 'content' string correctly
                responseContentJson = callOpenAI(transactionList);
                System.debug('Raw response content from OpenAI: ' + responseContentJson);
                
                // --- PARSING CHANGES START ---
                
                // 2. Deserialize the content string into a Map (representing the outer object)
                Map<String, Object> responseContentMap = (Map<String, Object>) JSON.deserializeUntyped(responseContentJson);
                
                // 3. Check if the expected key 'classified_transactions' exists and holds a list
                if (responseContentMap != null && responseContentMap.containsKey('classified_transactions')) {
                    
                    Object classifiedData = responseContentMap.get('classified_transactions');
                    
                    if (classifiedData instanceOf List<Object>) {
                        List<Object> resultsList = (List<Object>) classifiedData;
                        System.debug('Parsed classified_transactions list size: ' + resultsList.size());
                        
                        // 4. Iterate through the list of result objects
                        for (Object resultObj : resultsList) {
                            if (resultObj instanceOf Map<String, Object>) {
                                Map<String, Object> responseMap = (Map<String, Object>) resultObj;
                                
                                // 5. Extract data for each transaction (Add null checks for safety)
                                String transactionId = responseMap.containsKey('salesforce_id') ? (String) responseMap.get('salesforce_id') : null;
                                String aiCategory = responseMap.containsKey('ai_category') ? (String) responseMap.get('ai_category') : null;
                                Object scoreObj = responseMap.get('confidence_score'); // Get as Object first
                                Decimal aiConfidenceScoreDecimal = null; // Use Decimal for safety
                                String aiReasoning = responseMap.containsKey('ai_reasoning') ? (String) responseMap.get('ai_reasoning') : null;
                                
                                // Safely convert score to Decimal
                                if (scoreObj instanceOf Integer) {
                                    aiConfidenceScoreDecimal = (Decimal)scoreObj;
                                } else if (scoreObj instanceOf Decimal) {
                                    aiConfidenceScoreDecimal = (Decimal)scoreObj;
                                } else if (scoreObj != null) {
                                    try {
                                        aiConfidenceScoreDecimal = Decimal.valueOf(String.valueOf(scoreObj));
                                    } catch (Exception e) {
                                        System.debug(LoggingLevel.WARN, 'Could not parse confidence score for txn ' + transactionId + ': ' + scoreObj);
                                    }
                                }
                                
                                // 6. Update the corresponding Bank_Transaction__c record
                                if (transactionId != null && recordsToUpdateMap.containsKey(transactionId)) {
                                    Bank_Transaction__c txn = recordsToUpdateMap.get(transactionId);
                                    if (String.isNotBlank(aiCategory)) {
                                        txn.AI_Category__c = aiCategory;
                                        txn.Synced_Status__c = 'Success';
                                        txn.confidence_score__c = aiConfidenceScoreDecimal;
                                        txn.AI_Reasoning__c = aiReasoning;
                                    } else {
                                        System.debug(LoggingLevel.WARN, 'AI Category was blank for txn ' + transactionId);
                                        txn.Synced_Status__c = 'Failed';
                                        txn.confidence_score__c = null;
                                        txn.AI_Reasoning__c = '';
                                    }
                                    txn.Last_Fetch_Category__c = System.now();
                                } else {
                                    System.debug(LoggingLevel.WARN, 'Received classification for unknown or null Salesforce ID: ' + transactionId);
                                }
                            } else {
                                System.debug(LoggingLevel.ERROR, 'Item in resultsList was not a Map: ' + resultObj);
                                // Decide if you want to mark *all* transactions in the batch as Error here?
                            }
                        } // End of loop through resultsList
                        success = true; // Mark success if loop finishes
                        
                    } else {
                        // Handle case where 'classified_transactions' is not a List
                        System.debug(LoggingLevel.ERROR, 'Key "classified_transactions" did not contain a List.');
                        throw new JSONException('OpenAI response key "classified_transactions" did not contain a List.');
                    }
                    
                } else {
                    // Handle case where the expected key 'classified_transactions' is missing
                    System.debug(LoggingLevel.ERROR, 'Key "classified_transactions" not found in OpenAI response content.');
                    throw new JSONException('OpenAI response content did not contain the expected "classified_transactions" key.');
                }
                
                
            } catch (System.JSONException e) {
                retryCount++;
                System.debug('JSONException occurred: ' + e.getMessage() + '. Retrying ' + retryCount + '/' + maxRetries);
                if (retryCount > maxRetries) {
                    System.debug('Max retries reached. Processing failed.');
                    for (Bank_Transaction__c txn : recordsToUpdateMap.values()) {
                        txn.Synced_Status__c = 'Error';
                        txn.Last_Fetch_Category__c = System.now();
                    }
                    break;
                }
            } catch (CalloutException e) {
                System.debug('OpenAI Callout Exception: ' + e.getMessage());
                throw new CalloutException(e.getMessage() + ' ' + e.getStackTraceString());
            }
        }
    }
    
    @AuraEnabled
    public static void processTransactionsInBatch(Id bankAccountId, String filterType, Id userId) {
        String jobName = 'BankTxnProc_' + bankAccountId + '_' + System.now().getTime();
        String cronExpression = BankTransactionBatchProcessor.formatCron(System.now().addSeconds(5));
        
        System.schedule(jobName, cronExpression, new BankTransactionBatchProcessor(bankAccountId, filterType, userId));
    }
    
    private static String callOpenAI(List<OpenAITransactionWrapper.TransactionWrapper> transactions) {
        OpenAI_Config__mdt configMetadata = [SELECT SystemMessage__c, SystemMessageCredit__c, max_tokens__c, temperature__c, 
                                             Endpoint__c, APIVersion__c, API_Key__c, Model__c 
                                             FROM OpenAI_Config__mdt 
                                             WHERE MasterLabel = 'SystemMessage' 
                                             LIMIT 1];
        if (configMetadata == null) {
            throw new CalloutException('OpenAI Config custom setting is not configured.');
        }
        
        String transactionType = transactions[0].type;
        String systemMessage = (transactionType == 'debit') ? configMetadata.SystemMessage__c : configMetadata.SystemMessageCredit__c;
        
        String apiUrl = configMetadata.Endpoint__c + '/openai/deployments/' + configMetadata.Model__c + '/chat/completions?api-version=' + configMetadata.APIVersion__c;
        String apiKey = configMetadata.API_Key__c;
        
        OpenAITransactionWrapper.OpenAIRequest requestPayload = new OpenAITransactionWrapper.OpenAIRequest(
            transactions,
            systemMessage,
            configMetadata.max_tokens__c,
            configMetadata.temperature__c
        );
        
        System.debug(JSON.serialize(requestPayload));
        
        HttpRequest req = new HttpRequest();
        req.setEndpoint(apiUrl);
        req.setMethod('POST');
        req.setHeader('Content-Type', 'application/json');
        req.setHeader('Accept', 'application/json');
        req.setHeader('api-key', apiKey);
        req.setBody(JSON.serialize(requestPayload));
        req.setTimeout(120000);
        
        Http http = new Http();
        HTTPResponse response = http.send(req);
        
        if (response.getStatusCode() == 200) {
            Map<String, Object> parsedResponse = (Map<String, Object>) JSON.deserializeUntyped(response.getBody());
            System.debug('parsedResponse');
            System.debug(parsedResponse);
            
            if (parsedResponse.containsKey('choices')) {
                List<Object> choices = (List<Object>) parsedResponse.get('choices');
                if (!choices.isEmpty()) {
                    Map<String, Object> firstChoice = (Map<String, Object>) choices[0];
                    if (firstChoice.containsKey('message') && ((Map<String, Object>) firstChoice.get('message')).containsKey('content')) {
                        return (String) ((Map<String, Object>) firstChoice.get('message')).get('content');
                    }
                }
            }
            throw new CalloutException('No valid content found in OpenAI response.');
        } else {
            Map<String, Object> errorResponse = (Map<String, Object>) JSON.deserializeUntyped(response.getBody());
            System.debug('errorResponse');
            System.debug(errorResponse);
            String errorMessage = 'API Error: ';
            if (errorResponse.containsKey('error')) {
                Map<String, Object> errorDetails = (Map<String, Object>) errorResponse.get('error');
                if (errorDetails.containsKey('code') && String.valueOf(errorDetails.get('code')).contains('429')) {
                    throw new CalloutException('Rate limit exceeded: ' + errorDetails.get('message'));
                }
                errorMessage += errorDetails.containsKey('message') ? (String) errorDetails.get('message') : 'Unknown error';
                errorMessage += errorDetails.containsKey('code') ? ' (Code: ' + errorDetails.get('code') + ')' : '';
            }
            throw new CalloutException(errorMessage + ' - ' + response.getBody());
        }
    }
}