@isTest
public class FlinkNightlyRefreshBatchTest {

    @isTest
    static void testFlinkNightlyRefreshBatchWithSingleAccount() {
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Base_Url__c = 'https://api.test.com/',
            Customer_Id__c = '12345',
            Bearer_Token__c = 'testBearerToken'
        );
        insert config;

		User usrObj = [SELECT id, ContactId FROM User WHERE Id = :UserInfo.getUserId()];

        Bank_Account__c account = new Bank_Account__c(
            Name = 'Test Account',
            Institution_Name__c = 'Test Bank',
            Login_ID__c = 'Login123',
            Is_Active__c = true,
            Contact__c = usrObj.ContactId
        );
        insert account;

        Test.setMock(HttpCalloutMock.class, new FlinkNightlyRefreshBatchMock());

        Test.startTest();
        Test.setMock(Database.BatchableContext.class, new MockBatchableContext());

        Set<Id> bankAccIds = new Set<Id>{account.Id};
        FlinkNightlyRefreshBatch batch = new FlinkNightlyRefreshBatch(bankAccIds);
        Database.executeBatch(batch, 1);
        Test.stopTest();

    }
    
    private class FlinkNightlyRefreshBatchMock implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
			
            if (req.getBody().contains('RequestId')){
                res.setStatusCode(203);
                res.setHeader('Content-Type', 'application/json');
                res.setBody('{"key": "value"}');
            } else {
                res.setStatusCode(202);
                res.setHeader('Content-Type', 'application/json');
                res.setBody('{"RequestId": "mockRequestId123"}');
            }
			
            return res;
        }
    }

    private class MockBatchableContext implements Database.BatchableContext {
        public String getJobId() {
            return 'MockJobId';
        }

        public Id getChildJobId() {
            return null;
        }
    }
}