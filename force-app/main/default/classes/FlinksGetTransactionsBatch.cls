@SuppressWarnings('PMD')
public class FlinksGetTransactionsBatch implements Database.Batchable<sObject>,Database.Stateful, Database.AllowsCallouts{

    private static final String CLASS_NAME = 'FlinksGetTransactionsBatch';
    private static final List<String> LOG_TAGS = new List<String>{CLASS_NAME};

    public FlinksGetTransactionsBatch(){
        DebugLogUtil.entry(CLASS_NAME + '.constructor', null, LOG_TAGS);
        // Any constructor specific logic would go here
        DebugLogUtil.exit(CLASS_NAME + '.constructor', LOG_TAGS);
    }

    public Database.QueryLocator start(Database.BatchableContext info) {
        DebugLogUtil.entry(CLASS_NAME + '.start', new Map<String, Object>{'BatchableContext' => info}, LOG_TAGS);

        String query = 'SELECT Id, Request_Id__c FROM Bank_Account__c WHERE Is_Active__c = TRUE';
        DebugLogUtil.info('Batch start query: ' + query, LOG_TAGS);

        Database.QueryLocator queryLocator = Database.getQueryLocator(query);

        DebugLogUtil.exit(CLASS_NAME + '.start', LOG_TAGS);
        return queryLocator;
    }

    public void execute(Database.BatchableContext info, List<Bank_Account__c> records) {
        DebugLogUtil.entry(CLASS_NAME + '.execute', new Map<String, Object>{
            'BatchableContext' => info,
            'numberOfRecords' => records.size()
        }, LOG_TAGS);

        try{
            DebugLogUtil.info('Processing ' + records.size() + ' Bank_Account__c records.', LOG_TAGS);
            for(Bank_Account__c bankAcc : records){
                DebugLogUtil.info('Attempting to get account details for Bank_Account__c Id: ' + bankAcc.Id + ', Request_Id__c: ' + bankAcc.Request_Id__c, LOG_TAGS);
                FlinksController.getAccountDetails(bankAcc.Request_Id__c);
                DebugLogUtil.info('Successfully called getAccountDetails for Request_Id__c: ' + bankAcc.Request_Id__c, LOG_TAGS);
            }

        } catch (Exception e) {
            DebugLogUtil.error('Exception in execute method.', e, LOG_TAGS);
            // UIT_Utility.LogException(UserInfo.getUserId(),e,'FlinksGetTransactionsBatch.execute'); // Retained original custom logging if still needed
        }
        DebugLogUtil.exit(CLASS_NAME + '.execute', LOG_TAGS);
    }

    public void finish(Database.BatchableContext BC){
        DebugLogUtil.entry(CLASS_NAME + '.finish', new Map<String, Object>{'BatchableContext' => BC}, LOG_TAGS);
        try{
           DebugLogUtil.info('Batch job finished successfully.', LOG_TAGS);
        
        } catch (Exception e) {
            DebugLogUtil.error('Exception in finish method.', e, LOG_TAGS);
            // UIT_Utility.LogException(UserInfo.getUserId(),e,'FlinksGetTransactionsBatch.finish'); // Retained original custom logging if still needed
        }
        DebugLogUtil.exit(CLASS_NAME + '.finish', LOG_TAGS);
        DebugLogUtil.saveLogs();
    }

}