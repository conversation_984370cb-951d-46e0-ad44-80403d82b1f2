@SuppressWarnings('PMD')
public class DropboxController {
    // Define the root namespace ID for the team space
    @TestVisible 
    private static final String ROOT_NAMESPACE_ID = '11709235889'; // Replace with your actual root_namespace_id
    @TestVisible
    private static Boolean isAllowNameSpaceId = !mf123opp.isSandbox();

    @AuraEnabled(cacheable=true)
    public static String fetchFiles(String path){
        System.debug('path '+path);
        String body = '{"path":"' + path + '","recursive":false,"include_media_info":false,"include_deleted":false,"include_has_explicit_shared_members":false,"include_mounted_folders":true,"include_non_downloadable_files":true}';
        HttpRequest req = new HttpRequest();
        req.setEndpoint('callout:Dropbox/2/files/list_folder');
        req.setHeader('Content-Type', 'application/json');
        if (isAllowNameSpaceId) {
            req.setHeader('Dropbox-API-Path-Root', '{".tag": "root", "root": "' + ROOT_NAMESPACE_ID + '"}');
        }
        req.setMethod('POST');
        req.setBody(body);
        Http http = new Http();
        HTTPResponse res = http.send(req);
        System.debug(res.getStatusCode());
        System.debug(res.getBody());
        if(res.getStatusCode() == 200) {
            System.debug('Make a callout to External System '+res.getBody());
            return res.getBody();
        } else if (res.getStatusCode() == 409) {
            String responseBody = res.getBody();
            Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(responseBody);
            if(responseMap.get('error_summary') != null && responseMap.get('error_summary').toString().startsWith('path/not_found')) {
                System.debug('No files found at the specified path.');
                return '{"entries": []}';
            } else {
                throw new AuraHandledException('Failed to fetch files from Dropbox: ' + res.getBody());
            }
        } else {
            throw new AuraHandledException('Failed to fetch files from Dropbox: ' + res.getBody());
        }
    }

    @AuraEnabled(cacheable=true)
    public static String getPreview(String filePath) {
        System.debug('filePath getPreview'+filePath);
        String body = '{"path":"' + filePath + '"}';
        System.debug('body '+body);
        HttpRequest req = new HttpRequest();
        req.setEndpoint('callout:DropBox_File_Preview/2/files/get_preview');
        req.setHeader('Dropbox-API-Arg', body);
        if (isAllowNameSpaceId) {
            req.setHeader('Dropbox-API-Path-Root', '{".tag": "root", "root": "' + ROOT_NAMESPACE_ID + '"}');
        }
        req.setMethod('POST');
        Http http = new Http();
        HTTPResponse res = http.send(req);
        System.debug(res.getStatusCode());
        System.debug(res.getBody());
        if(res.getStatusCode() == 200) {
            System.debug('Preview link fetched successfully: '+res.getBody());
            return res.getBody();
        } else {
            throw new AuraHandledException('Failed to fetch file preview from Dropbox: ' + res.getBody());
        }
    }

    @AuraEnabled
    public static String uploadFileToDropbox(String filePath, String fileContentBase64) {
        try {
            HttpRequest req = new HttpRequest();
            req.setEndpoint('callout:DropBox_File_Preview/2/files/upload');
            req.setMethod('POST');
            req.setHeader('Content-Type', 'application/octet-stream');
            req.setHeader('Dropbox-API-Arg', JSON.serialize(new Map<String, Object>{
                'path' => filePath,
                'mode' => 'add',
                'autorename' => true,
                'mute' => false
            }));
            if (isAllowNameSpaceId) {
                req.setHeader('Dropbox-API-Path-Root', '{".tag": "root", "root": "' + ROOT_NAMESPACE_ID + '"}');
            }
            Blob fileContentBlob = EncodingUtil.base64Decode(fileContentBase64);
            req.setBodyAsBlob(fileContentBlob);
            Http http = new Http();
            HTTPResponse res = http.send(req);
            if (res.getStatusCode() == 200) {
                return res.getBody();
            } else {
                throw new AuraHandledException('Failed to upload file to Dropbox: ' + res.getBody());
            }
        } catch (Exception e) {
            throw new AuraHandledException('Error uploading file: ' + e.getMessage());
        }
    }

    public static Map<String,Object> uploadFileToDropboxByUrl(String folderPath, String fileName, String filePublicUrl, String relatedRecordId) {
        return uploadFileToDropboxByUrl(folderPath, fileName, filePublicUrl, relatedRecordId, null);
    }

    public static Map<String,Object> uploadFileToDropboxByUrl(String folderPath, String fileName, String filePublicUrl, String relatedRecordId, String parentId) {
        Map<String,Object> returnMap = new Map<String, Object>();
        HttpRequest req = new HttpRequest();
        req.setEndpoint('callout:Dropbox_api/files/save_url');        
        req.setMethod('POST');
        req.setHeader('Content-Type', 'application/json');
        if (isAllowNameSpaceId) {
            req.setHeader('Dropbox-API-Path-Root', '{".tag": "root", "root": "' + ROOT_NAMESPACE_ID + '"}');
        }
        req.setBody(JSON.serialize(new Map<String, Object>{
            'path' => folderPath  + fileName,
            'url' => filePublicUrl
        }));
        System.debug('body -> ' + req.getBody());
        Http http = new Http();
        HttpResponse res = http.send(req);
        returnMap.put('res', res);
        returnMap.put('req', req);
        String asyncJobId = null;
        try{
            Map<String, Object> resMap = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
            if (resMap.containsKey('async_job_id')) {
                asyncJobId = (String) resMap.get('async_job_id');
            }
        } catch(Exception e){}
        returnMap.put('log', UIT_Utility.LogRequestAndResponseStub(relatedRecordId, res.getStatusCode(), req.getBody(), res.getBody(), asyncJobId, parentId));
        return returnMap;
    }

    public static Map<String,Object> checkDBJobStatus(String asyncJobId, String relatedRecordId) {
        Map<String,Object> returnMap = new Map<String, Object>();
        HttpRequest req = new HttpRequest();
        req.setEndpoint('callout:Dropbox_api/files/save_url/check_job_status');
        req.setMethod('POST');
        req.setHeader('Content-Type', 'application/json');
        if (isAllowNameSpaceId) {
            req.setHeader('Dropbox-API-Path-Root', '{".tag": "root", "root": "' + ROOT_NAMESPACE_ID + '"}');
        }
        req.setBody(JSON.serialize(new Map<String, Object>{
            'async_job_id' => asyncJobId
        }));
        Http http = new Http();
        HttpResponse res = http.send(req);
        returnMap.put('res', res);
        returnMap.put('req', req);
        returnMap.put('log', UIT_Utility.LogRequestAndResponseStub(relatedRecordId, res.getStatusCode(), req.getBody(), res.getBody()));
        return returnMap;
    }

    @AuraEnabled
    public static String fetchFileContentAsBase64(String documentId) {
        System.debug('fetchFileContentAsBase64 '+documentId);
        ContentVersion contentVersion = [
            SELECT VersionData 
            FROM ContentVersion 
            WHERE Id = :documentId 
            LIMIT 1
        ];
        return EncodingUtil.base64Encode(contentVersion.VersionData);
    }

    @InvocableMethod(label='Dropbox Retry Upload' description='Dropbox Retry Upload')
    public static void retryUpload(List<String> contentVersionIds) {
        List<ContentVersion> cvsToUpdate = new List<ContentVersion>();
        ContentDocumentLink cdObj = DropboxUtility.getContentDocumentLinkByContentVersionId(contentVersionIds[0]);
        String fileUrl = DropboxUtility.getFolderPath(cdObj);
        ContentVersion cv = [SELECT Id, Title, FileExtension FROM ContentVersion WHERE Id = :contentVersionIds[0]];
        String fullFileName = cv.Title + '.' + cv.FileExtension;
        DropboxUploadBatch dub = new DropboxUploadBatch(fileUrl);
        Map<Id,String> downloadLinkMap = dub.getDownloadLinks(contentVersionIds);        
        String downloadUrl = downloadLinkMap.get(contentVersionIds[0]);
        if(fileUrl != null) {
            Map<String,Object> returnMap = dub.saveFileToDropbox(fullFileName, downloadUrl, cv.Id, cdObj.LinkedEntityId);
            if((Boolean)returnMap.get('success') == true) {
                cv.Dropbox_Async_Job_Id__c = (String) returnMap.get('jobId');
                cv.Dropbox_Sync_Status__c = 'Processing - Waiting Dropbox Confirmation';
                cvsToUpdate.add(cv);
            } else {
                cv.Dropbox_Sync_Status__c = 'Failed';
                cvsToUpdate.add(cv);
            }
        }
        if(!cvsToUpdate.isEmpty()) {
            update cvsToUpdate;
        }
        if(!dub.customExceptions.isEmpty()) {
            insert dub.customExceptions;
        } 
    }

    @AuraEnabled(cacheable=true)
    public static Map<String, Object> fetchFileShareLink(String path) {
        System.debug('### openFileInEditMode invoked with path: ' + path);
        Map<String, Object> returnMap = new Map<String, Object>();
        
        HttpRequest req = new HttpRequest();
        req.setEndpoint('callout:Dropbox_api/sharing/list_shared_links');
        req.setMethod('POST');
        req.setHeader('Content-Type', 'application/json');
        if (isAllowNameSpaceId) {
            req.setHeader('Dropbox-API-Path-Root', '{".tag": "root", "root": "' + ROOT_NAMESPACE_ID + '"}');
        }
        req.setBody(JSON.serialize(new Map<String, Object>{
            'path' => path
        }));
        System.debug('### HttpRequest prepared: ' + req);
        
        Http http = new Http();
        HttpResponse res;
        try {
            res = http.send(req);
            System.debug('### HttpResponse received. Status: ' + res.getStatusCode() + ', Body: ' + res.getBody());
            
            if (res.getStatusCode() == 200) {
                Map<String, Object> responseBody = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                System.debug('### Response Body: ' + responseBody);
                
                List<Object> links = (List<Object>) responseBody.get('links');
                System.debug('### Links from response: ' + links);
                
                if (!links.isEmpty()) {
                    Map<String, Object> firstLink = (Map<String, Object>) links[0];
                    System.debug('### First Link: ' + firstLink);
                    
                    String url = (String) firstLink.get('url');
                    System.debug('### URL extracted: ' + url);
                    
                    returnMap.put('url', url);
                } else {
                    System.debug('### No links found in the response.');
                    returnMap.put('error', 'No links found in the response.');
                }
            } else {
                System.debug('### HTTP request failed with status: ' + res.getStatusCode());
                returnMap.put('error', 'Failed to fetch links. HTTP Status: ' + res.getStatusCode());
            }
        } catch (Exception e) {
            System.debug('### Exception occurred: ' + e.getMessage());
            returnMap.put('error', 'An exception occurred: ' + e.getMessage());
        }
        
        System.debug('### Returning map: ' + returnMap);
        return returnMap;
    }
    
    @AuraEnabled(cacheable=true)
    public static String createFileShareLink(String path, String contentDocumentId) {
        System.debug('### openFileInEditMode invoked with path: ' + path);
        String fileUrl = '';
        
        try {
            Map<String, Object> requestBody = new Map<String, Object>{
                'path' => path,
                'settings' => new Map<String, Object>{
                    'access' => 'viewer',
                    'allow_download' => true,
                    'audience' => 'public',
                    'requested_visibility' => 'public'
                }
            };
            
            HttpRequest req = new HttpRequest();
            req.setEndpoint('callout:Dropbox_api/sharing/create_shared_link_with_settings');
            req.setMethod('POST');
            req.setHeader('Content-Type', 'application/json');
            if (isAllowNameSpaceId) {
                req.setHeader('Dropbox-API-Path-Root', '{".tag": "root", "root": "' + ROOT_NAMESPACE_ID + '"}');
            }
            req.setBody(JSON.serialize(requestBody));
    
            Http http = new Http();
            HttpResponse res = http.send(req);
            
            if (res.getStatusCode() == 200) {
                Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                
                if (responseMap.containsKey('url')) {
                    fileUrl = (String) responseMap.get('url');
                    // Call future method to update ContentVersion record
                    updateContentVersion(contentDocumentId, fileUrl);
                } else {
                    System.debug('### URL field not found in the response');
                }
            } else if (res.getStatusCode() == 409) { 
                Map<String, Object> errorResponse = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                if (errorResponse.containsKey('error')) {
                    Map<String, Object> errorMap = (Map<String, Object>) errorResponse.get('error');
                    if (errorMap.containsKey('.tag') && (String) errorMap.get('.tag') == 'shared_link_already_exists') {
                        return 'shared_link_exists';
                    }
                }
            } 
            else {
                System.debug('### Error in API response: ' + res.getStatusCode() + ' ' + res.getBody());
                throw new AuraHandledException('The response did not contain a valid URL.');
            }
        } catch (Exception ex) {
            System.debug('### Exception occurred: ' + ex.getMessage());
            throw new AuraHandledException('An error occurred: ' + ex.getMessage());
        }
        
        return fileUrl;
    }
    
    @AuraEnabled(cacheable=true)
    public static String getDropboxShareLink(String contentDocumentId) {
        try {
            ContentVersion cv = [SELECT Id, Dropbox_Create_Share_Link__c FROM ContentVersion WHERE ContentDocumentId = :contentDocumentId LIMIT 1];
            return cv.Dropbox_Create_Share_Link__c;
        } catch (Exception ex) {
            System.debug('### Exception in getDropboxShareLink: ' + ex.getMessage());
            throw new AuraHandledException('Failed to fetch Dropbox share link: ' + ex.getMessage());
        }
    }

    // Future method to handle the update separately
    @future
    public static void updateContentVersion(String contentDocumentId, String fileUrl) {
        try {
            ContentVersion cv = [SELECT Id, Dropbox_Create_Share_Link__c FROM ContentVersion WHERE ContentDocumentId = :contentDocumentId LIMIT 1];
            cv.Dropbox_Create_Share_Link__c = fileUrl;
            update cv;
        } catch (Exception e) {
            System.debug('### Error updating ContentVersion: ' + e.getMessage());
        }
    }
}