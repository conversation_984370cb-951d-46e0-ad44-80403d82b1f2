@isTest
public class TakeParamFromUrlControllerTest {
    @testSetup
    static void setupTestData() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', AccountId = testAccount.Id);
        insert testContact;

        Opportunity testOpportunity = new Opportunity(Name = 'Test Opportunity', StageName = 'Prospecting', CloseDate = Date.today());
        insert testOpportunity;

        Project__c testProject = new Project__c(
            Name = 'Test Project',
            Loan_Opportunity__c = testOpportunity.Id,
            Account_Name__c = testAccount.Id,
            Enter_name_of_Project_Owner__c = 'Owner Name'
        );
        insert testProject;
    }

    @isTest
    static void testGetProjectObj() {
        Project__c insertedProject = [SELECT Id FROM Project__c LIMIT 1];
        
        Test.startTest();
        TakeParamFromUrlController.ProjectWrapper result = TakeParamFromUrlController.getProjectObj(insertedProject.Id);
        Test.stopTest();
    }
}