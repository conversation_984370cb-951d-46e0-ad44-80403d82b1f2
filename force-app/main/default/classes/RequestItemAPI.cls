@RestResource(urlMapping='/requestItem/*')
global without sharing class RequestItemAPI {

    @HttpGet
    global static void getrequestItem() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        Id disburseReqId = req.requestURI.substring(req.requestURI.lastIndexOf('/') + 1);

        ReqItemResponseWrapper responseWrapper = new ReqItemResponseWrapper();

        try {
            List<Requested_Item__c> reqItems;

            if (disburseReqId != null && disburseReqId.getSObjectType().getDescribe().getName() == 'Disbursement_Request__c') {
                reqItems = [
                    SELECT Id, Name, Disbursement_Request__c, Description_Work__c, 
                    Invoice_Amount__c, Invoice_Date__c, Invoice_Due_Date__c, Invoice__c,
                    CreatedDate, CreatedById, LastActivityDate
                    FROM Requested_Item__c 
                    WHERE Disbursement_Request__c = :disburseReqId
                ];
                
                responseWrapper.totalSize = reqItems.size();
            	responseWrapper.done = true;
            	responseWrapper.records = reqItems;

            	res.responseBody = Blob.valueOf(JSON.serialize(responseWrapper));
            } else {
                reqItems = [
                    SELECT Id, Name, Disbursement_Request__c, Description_Work__c, 
                    Invoice_Amount__c, Invoice_Date__c, Invoice_Due_Date__c, Invoice__c,
                    CreatedDate, CreatedById, LastActivityDate
                    FROM Requested_Item__c Where Id = :disburseReqId
                ];
                
                res.responseBody = Blob.valueOf(JSON.serialize(reqItems));
            }

        } catch (Exception ex) {
            res.statusCode = 500;
            res.responseBody = Blob.valueOf('Error retrieving projects: ' + ex.getMessage());
        }
    }

    public class ReqItemResponseWrapper {
        public Integer totalSize;
        public Boolean done;
        public List<Requested_Item__c> records;

        public ReqItemResponseWrapper() {
            this.records = new List<Requested_Item__c>();
        }
    }
}