// File: RequestItemController.cls
@SuppressWarnings('PMD')
public with sharing class RequestItemController {
    
    // Inner class to define the structure of each item received in the JSON
    public class RequestItemDetail {
        @AuraEnabled public String Name;
        @AuraEnabled public String descriptionWork; 
        @AuraEnabled public Date invoiceDate;
        @AuraEnabled public Decimal invoiceAmount;
        @AuraEnabled public String invoice; 
        @AuraEnabled public Date invoiceDueDate;
        @AuraEnabled public List<String> contentDocumentIds;
        
        public RequestItemDetail() {
            this.contentDocumentIds = new List<String>();
        }
    }
    
    // Inner class to define the structure of the input for the Invocable Method
    public class SaveContext {
        @InvocableVariable(label='Disbursement Request ID' description='The ID of the parent Disbursement_Request__c record.' required=true)
        public Id disbursementRequestId;
        
        @InvocableVariable(label='Request Items JSON' description='JSON string representing an array of Request Item details.' required=true)
        public String requestItemsJSON;
    }
    
    // Inner class to define the structure of the output for the Invocable Method
    public class SaveResultOutput {
        @InvocableVariable(label='Success' description='Indicates if the operation was successful for this context.')
        public Boolean isSuccess;
        
        // MODIFIED: Changed from List<Id> to List<Requested_Item__c>
        @InvocableVariable(label='Created Requested Items' description='List of successfully created Requested_Item__c records.')
        public List<Requested_Item__c> createdItems; 
        
        @InvocableVariable(label='Error Message' description='Error message if the operation failed.')
        public String errorMessage;
        
        public SaveResultOutput() {
            this.isSuccess = false; 
            this.createdItems = new List<Requested_Item__c>(); // MODIFIED
            this.errorMessage = '';
        }
    }
    
    /**
    * @description Invocable method to create Requested_Item__c records and link their files.
    * Called from a Flow.
    * @param contexts List of SaveContext objects, each containing a parent ID and item details.
    * @return List of SaveResultOutput objects, one for each input context.
    */
    @InvocableMethod(label='Save Requested Items and Link Files' description='Creates Requested Item records from JSON, links them to a parent Disbursement Request, and attaches specified files.')
    public static List<SaveResultOutput> saveRequestItemsAndLinkFiles(List<SaveContext> contexts) {
        List<SaveResultOutput> results = new List<SaveResultOutput>();
        Nebula.Logger.info('saveRequestItemsAndLinkFiles invoked with ' + (contexts == null ? 0 : contexts.size()) + ' context(s)').addTag('RequestItemFlow');
        Nebula.Logger.info('contexts -> ' + JSON.serialize(contexts)).addTag('RequestItemFlow');
        
        if (contexts == null || contexts.isEmpty()) {
            Nebula.Logger.warn('No contexts provided, returning empty results.').addTag('RequestItemFlow');
            System.debug('RequestItemController.saveRequestItemsAndLinkFiles: No contexts provided.');
            return results; 
        }
        
        for (SaveContext context : contexts) {
            SaveResultOutput currentResult = new SaveResultOutput();
            List<Requested_Item__c> itemsToInsertForContext = new List<Requested_Item__c>();
            List<PairRequestedItemAndStringList> itemAndFileIdsListForContext = new List<PairRequestedItemAndStringList>();
            
            try {
                if (String.isBlank(context.requestItemsJSON) || context.disbursementRequestId == null) {
                    currentResult.errorMessage = 'Missing requestItemsJSON or disbursementRequestId.';
                    System.debug('RequestItemController.saveRequestItemsAndLinkFiles: ' + currentResult.errorMessage);
                    Nebula.Logger.warn(currentResult.errorMessage).addTag('RequestItemFlow');
                    results.add(currentResult);
                    continue;
                }
                
                List<RequestItemDetail> itemDetails;
               
                try {
                    itemDetails = (List<RequestItemDetail>) JSON.deserialize(context.requestItemsJSON, List<RequestItemDetail>.class);
                    Nebula.Logger.info('Deserialized itemDetails with size: ' + itemDetails.size()).addTag('RequestItemFlow');
                } catch (Exception e) {
                    currentResult.errorMessage = 'Error deserializing requestItemsJSON: ' + e.getMessage();
                    System.debug('RequestItemController.saveRequestItemsAndLinkFiles: ' + currentResult.errorMessage);
                    Nebula.Logger.error(currentResult.errorMessage + '\nStack: ' + e.getStackTraceString()).addTag('RequestItemFlow');
                    results.add(currentResult);
                    continue; 
                }
                
                if (itemDetails != null && !itemDetails.isEmpty()) {
                    for (RequestItemDetail detail : itemDetails) {
                        Requested_Item__c newItem = new Requested_Item__c();
                        newItem.Name = detail.Name;
                        newItem.Description_Work__c = detail.descriptionWork; 
                        newItem.Invoice_Date__c = detail.invoiceDate;
                        newItem.Invoice_Amount__c = detail.invoiceAmount;
                        newItem.Invoice__c = detail.invoice; 
                        newItem.Invoice_Due_Date__c = detail.invoiceDueDate;
                        newItem.Disbursement_Request__c = context.disbursementRequestId;
                        
                        itemsToInsertForContext.add(newItem);
                        itemAndFileIdsListForContext.add(new PairRequestedItemAndStringList(newItem, detail.contentDocumentIds));
                    }
                } else {
                    currentResult.errorMessage = 'No item details found in the JSON payload.';
                    System.debug('RequestItemController.saveRequestItemsAndLinkFiles: ' + currentResult.errorMessage);
                    Nebula.Logger.warn(currentResult.errorMessage).addTag('RequestItemFlow');
                    results.add(currentResult); 
                    continue;
                }
                
                if (!itemsToInsertForContext.isEmpty()) {
                    List<ContentDocumentLink> cdlsToInsert = new List<ContentDocumentLink>();
                    Database.SaveResult[] insertResults = Database.insert(itemsToInsertForContext, false); 
                    Boolean anyOperationFailed = false; // Tracks if any DML within this context fails
                    
                    for (Integer i = 0; i < insertResults.size(); i++) {
                        if (insertResults[i].isSuccess()) {
                            // The SObject in itemsToInsertForContext now has its ID populated after successful insert.
                            // Add the successfully inserted SObject (which now includes its ID) to the result.
                            Requested_Item__c successfullyInsertedItem = itemsToInsertForContext[i];
                            successfullyInsertedItem.Id = insertResults[i].getId(); // Explicitly ensure ID is set from SaveResult
                            currentResult.createdItems.add(successfullyInsertedItem); // MODIFIED: Add the SObject
                            
                            PairRequestedItemAndStringList originalItemPair = itemAndFileIdsListForContext[i];
                            List<String> docIds = originalItemPair.fileIds;
                            
                            if (docIds != null && !docIds.isEmpty()) {
                                for (String docId : docIds) {
                                    ContentDocumentLink cdl = new ContentDocumentLink();
                                    cdl.ContentDocumentId = docId;
                                    cdl.LinkedEntityId = successfullyInsertedItem.Id; // Use the ID from the successfully inserted item
                                    cdl.ShareType = 'V'; 
                                    cdl.Visibility = 'AllUsers'; 
                                    cdlsToInsert.add(cdl);
                                }
                            }
                        } else {
                            anyOperationFailed = true;
                            String itemErrors = '';
                            for(Database.Error err : insertResults[i].getErrors()) {
                                itemErrors += err.getStatusCode() + ': ' + err.getMessage() + '; ';
                            }
                            System.debug('RequestItemController: Failed to insert Requested_Item__c. Errors: ' + itemErrors);
                            Nebula.Logger.error('Failed to insert Requested_Item__c. Errors: ' + itemErrors).addTag('RequestItemFlow');
                            currentResult.errorMessage += 'Failed to insert item: ' + itemErrors;
                        }
                    }
                    
                    if (!cdlsToInsert.isEmpty()) {
                        Database.SaveResult[] cdlInsertResults = Database.insert(cdlsToInsert, false);
                        for(Database.SaveResult sr : cdlInsertResults){
                            if(!sr.isSuccess()){
                                anyOperationFailed = true; 
                                String cdlErrors = '';
                                for(Database.Error err : sr.getErrors()) {
                                    cdlErrors += err.getStatusCode() + ': ' + err.getMessage() + '; ';
                                }
                                System.debug('RequestItemController: Failed to insert ContentDocumentLink. Errors: ' + cdlErrors);
                                Nebula.Logger.error('Failed to insert ContentDocumentLink. Errors: ' + cdlErrors).addTag('RequestItemFlow');
                                currentResult.errorMessage += 'Failed to link file(s): ' + cdlErrors;
                            }
                        }
                    }
                    
                    currentResult.isSuccess = !anyOperationFailed && !currentResult.createdItems.isEmpty();
                    if (currentResult.createdItems.isEmpty() && String.isBlank(currentResult.errorMessage)) {
                        currentResult.errorMessage = 'No items were successfully created or linked.';
                    } else if (anyOperationFailed && String.isBlank(currentResult.errorMessage)){ // If an operation failed but no specific error was appended yet
                        currentResult.errorMessage = 'Some items or files failed to process.';
                    }
                    
                    
                } else if (String.isBlank(currentResult.errorMessage)) { 
                    currentResult.isSuccess = true; 
                    currentResult.errorMessage = 'No items were provided to insert for this context.';
                }
                results.add(currentResult);
                
            } catch (Exception e) { 
                System.debug('RequestItemController.saveRequestItemsAndLinkFiles: Unhandled error during DML operations for a context. Details: ' + e.getMessage() + ' Stacktrace: ' + e.getStackTraceString());
                String exMessage = 'Unhandled error: ' + e.getMessage() + '\nStacktrace: ' + e.getStackTraceString();
                Nebula.Logger.error(exMessage).addTag('RequestItemFlow');
                currentResult.isSuccess = false;
                currentResult.errorMessage = 'An unexpected error occurred: ' + e.getMessage();
                results.add(currentResult);
            }
        }
        Nebula.Logger.info('Completed saveRequestItemsAndLinkFiles execution.').addTag('RequestItemFlow');
        Nebula.Logger.saveLog();
        return results;
    }
    
    @AuraEnabled
    public static void deleteUploadedFile(String contentDocumentId) {
        if (String.isBlank(contentDocumentId)) {
            throw new AuraHandledException('ContentDocumentId cannot be blank.');
        }
        try {
            List<ContentDocument> docsToDelete = [SELECT Id FROM ContentDocument WHERE Id = :contentDocumentId LIMIT 1];
            if (!docsToDelete.isEmpty()) {
                Database.delete(docsToDelete, false); 
                System.debug('RequestItemController.deleteUploadedFile: Successfully deleted ContentDocument with ID: ' + contentDocumentId);
                Nebula.Logger.info('Successfully deleted ContentDocument: ' + contentDocumentId).addTag('RequestItemFlow');
            } else {
                System.debug('RequestItemController.deleteUploadedFile: No ContentDocument found with ID: ' + contentDocumentId);
                Nebula.Logger.warn('No ContentDocument found for deletion: ' + contentDocumentId).addTag('RequestItemFlow');
            }
        } catch (Exception e) {
            System.debug('RequestItemController.deleteUploadedFile: Error deleting ContentDocument. ID: ' + contentDocumentId + '. Details: ' + e.getMessage());
            String err = 'Error deleting ContentDocument. ID: ' + contentDocumentId + '. Details: ' + e.getMessage();
            Nebula.Logger.error(err + '\nStack: ' + e.getStackTraceString()).addTag('RequestItemFlow');
            Nebula.Logger.saveLog();
            throw new AuraHandledException('Error deleting file: ' + e.getMessage());
        }
        Nebula.Logger.saveLog();
    }
    
    private class PairRequestedItemAndStringList {
        public Requested_Item__c requestedItem { get; set; }
        public List<String> fileIds { get; set; }
        
        public PairRequestedItemAndStringList(Requested_Item__c item, List<String> ids) {
            this.requestedItem = item;
            this.fileIds = ids;
        }
    }
}