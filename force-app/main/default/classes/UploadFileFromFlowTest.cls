@isTest
private class UploadFileFromFlowTest {
    
    @isTest static void testEmptyRequests() {
        // empty list should return no responses
        Test.startTest();
        List<UploadFileFromFlow.FileUploadResponse> respEmpty = 
            UploadFileFromFlow.uploadFile(new List<UploadFileFromFlow.FileUploadRequest>());
        Test.stopTest();
        
        System.assertEquals(0, respEmpty.size(),
            'Expected zero responses when input list is empty');
    }
    
    @isTest static void testMissingParameters() {
        // missing base64Data and linkedEntityId
        UploadFileFromFlow.FileUploadRequest req = new UploadFileFromFlow.FileUploadRequest();
        req.base64Data     = '';
        req.linkedEntityId = '';
        
        Test.startTest();
        List<UploadFileFromFlow.FileUploadResponse> respMissing = 
            UploadFileFromFlow.uploadFile(new List<UploadFileFromFlow.FileUploadRequest>{ req });
        Test.stopTest();
        
        System.assertEquals(0, respMissing.size(),
            'Expected zero responses when required parameters are blank');
    }
    
    @isTest static void testValidUpload() {
        // 1) create a parent record
        Account acct = new Account(Name='Test Account');
        insert acct;
        
        // 2) encode some simple text as base64
        String b64 = EncodingUtil.base64Encode(Blob.valueOf('HelloWorld'));
        
        // 3) invoke the flow
        UploadFileFromFlow.FileUploadRequest req = new UploadFileFromFlow.FileUploadRequest();
        req.base64Data     = b64;
        req.linkedEntityId = acct.Id;
        
        Test.startTest();
        List<UploadFileFromFlow.FileUploadResponse> resp = 
            UploadFileFromFlow.uploadFile(new List<UploadFileFromFlow.FileUploadRequest>{ req });
        Test.stopTest();
        
        // 4) assertions on the response
        System.assertEquals(1, resp.size(), 'One response expected');
        String downloadUrl = resp[0].contentDownloadUrl;
        System.assertNotEquals(null, downloadUrl, 'URL should not be null');
        System.assert(downloadUrl.length() > 0, 'URL should not be empty');
        
        // 5) verify ContentDocumentLink was created
        ContentDocumentLink cdl = [
            SELECT Id, LinkedEntityId 
            FROM ContentDocumentLink 
            WHERE LinkedEntityId = :acct.Id 
            LIMIT 1
        ];
        System.assertEquals(acct.Id, cdl.LinkedEntityId,
            'ContentDocumentLink must reference the original Account');
        
        // 6) verify a ContentDistribution exists
        ContentDistribution dist = [
            SELECT Id, ContentDownloadUrl 
            FROM ContentDistribution 
            ORDER BY CreatedDate DESC 
            LIMIT 1
        ];
        System.assertNotEquals(null, dist.Id, 'A ContentDistribution record should have been created');
        System.assert(dist.ContentDownloadUrl.contains('http'),
            'Distribution URL should look like a public link');
    }
}