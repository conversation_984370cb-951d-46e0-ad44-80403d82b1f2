@isTest
public class UserDetailsControllerTest {
    @testSetup
    static void setupTestData() {
        // Create a test Account
        Account testAccount = new Account(
            Name = 'Test Account'
        );
        insert testAccount;

        // Create a test Contact
        Contact testContact = new Contact(
            FirstName = 'Test',
            LastName = 'Contact',
            Email = '<EMAIL>',
            Phone = '**********',
            AccountId = testAccount.Id
        );
        insert testContact;

        // Create a community user for the test Contact
        Profile communityUserProfile = [SELECT Id FROM Profile WHERE Name = 'Customer Community User' LIMIT 1];

        String uniqueUsername = 'testuser-' + DateTime.now().getTime() + '@example.com';

        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Email = '<EMAIL>',
            Username = uniqueUsername,
            Alias = 'tuser',
            ProfileId = communityUserProfile.Id,
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            TimeZoneSidKey = 'America/Chicago',
            ContactId = testContact.Id,
            CommunityNickname = 'testuser-' + DateTime.now().getTime()
        );
        insert testUser;
    }

    @isTest
    static void testGetUserDetails() {
        // Retrieve the test User
        User testUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1];

        // Set the context user to the test User
        System.runAs(testUser) {
            // Call the method and get the result
            UserDetailsController.UserDetails result;
            Test.startTest();
            result = UserDetailsController.getUserDetails();
            Boolean response = UserDetailsController.isSandbox();
            Test.stopTest();

            // Assert the values in the result
            System.assertEquals('Test Account', result.accountName);
            System.assertEquals('Test Contact', result.contactName);
            System.assertEquals('**********', result.contactPhone);
            System.assertEquals('<EMAIL>', result.contactEmail);
            System.assertEquals('<EMAIL>', result.userEmail);
            System.assertEquals(null, result.userPhone); // Assuming user phone is null in this context
        }
    }
}