public class GenerateHTML {
    public static String createPaths(List<String> pickListValues, String currentValue) {
        Boolean isCurrentPassed = false;
        String body = 
            '<div style="font-family: Arial, sans-serif; margin: 20px; display: flex; justify-content: center;">' +
            '<table cellspacing="0" cellpadding="0" >' +
            '<tr>';

        for (Integer i = 0; i < pickListValues.size(); i++) {
            String value = pickListValues[i];
            String bgColor, textColor;
            
            if (value == currentValue) {
                bgColor = '#004085'; // Current stage (blue)
                textColor = 'white';
                isCurrentPassed = true;
            } else if (isCurrentPassed) {
                bgColor = '#e0e0e0'; // Future stages (grey)
                textColor = 'black';
            } else {
                bgColor = '#4CAF50'; // Past stages (green)
                textColor = 'white';
            }

            // Stage text cell
            body += '<td style="background-color: ' + bgColor + '; color: ' + textColor + 
                    '; padding: 10px 20px; font-weight: bold; position: relative;">' + 
                    value + '</td>';
            
            body += '<td style="width: 0; height: 0; border-top: 16px solid transparent; ' + 
                'border-bottom: 16px solid transparent; border-left: 16px solid ' + bgColor + ';"></td>';
            
        }

        body += '</tr></table></div>';
        return body;
    }
}