@isTest
public class sendEmailOfNewProjectFormDetailsTest {

    @isTest
    static void testSendEmailOfNewProjectsForm() {
        // Test data setup
        Account testAccount = new Account(
            Name = 'Test Account',
            of_Owners__c = 2,
            Year_Founded__c = '2020',
            Website = 'https://testaccount.com',
            Phone = '*********0',
            NumberOfEmployees = 50,
            Email__c = '<EMAIL>',
            EIN__c = *********,
            Description = 'A test account',
            BillingStreet = '123 Test St',
            BillingCity = 'Test City',
            BillingState = 'TS',
            BillingPostalCode = '12345'
        );
        insert testAccount;
        
        Contact testContact = new Contact(
            AccountId = testAccount.Id,
            FirstName = 'Test',
            LastName = 'Contact',
            Email = '<EMAIL>',
            MailingStreet = '123 Contact St',
            MailingCity = 'Contact City',
            MailingState = 'CS',
            MailingPostalCode = '54321'
        );
        insert testContact;

        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity',
            AccountId = testAccount.Id,
            StageName = 'Prospecting',
            CloseDate = Date.today().addMonths(1),
            Amount = 50000,
            UCC_Filings__c = 'No',
            Bad_Debt__c = 'No',
            Bankruptcy__c = 'No',
            Current_Lawsuits__c = 'No',
            Supporting_Docs__c = 'None'
            //Signed_App__c = Date.today().format()
        );
        insert testOpportunity;

        // Create input variables for the method
        sendEmailOfNewProjectFormDetails.inputVariables inputVar = new sendEmailOfNewProjectFormDetails.inputVariables();
        inputVar.contactId = testContact.Id;
        inputVar.oppId = testOpportunity.Id;
        inputVar.contentDownloadUrl = 'https://platform-customer-2576-dev-ed.scratch.my.salesforce.com/sfc/dist/version/download/?oid=00DE200000D0a7F&ids=068E2000009lfs9&d=%2Fa%2FE2000000gWgD%2FQNHPwyncpjTkhSbJguox6RY2YBFEumARu0gN7Q23TvQ&asPdf=false';

        List<sendEmailOfNewProjectFormDetails.inputVariables> inputVariablesList = new List<sendEmailOfNewProjectFormDetails.inputVariables>();
        inputVariablesList.add(inputVar);

        // Invoke the method
        Test.startTest();
        List<sendEmailOfNewProjectFormDetails.outputVariables> result = sendEmailOfNewProjectFormDetails.sendEmailOfNewProjectsForm(inputVariablesList);
        Test.stopTest();

        // Assert the results
       
    }
}