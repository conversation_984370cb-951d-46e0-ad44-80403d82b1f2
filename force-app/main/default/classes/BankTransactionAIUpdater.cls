@SuppressWarnings('PMD')
public class BankTransactionAIUpdater {
    public static void updateInvalidAICategories() {
        // Query all MasterLabel values from MF_Category__mdt
        Set<String> validCategories = new Set<String>();
        for (MF_Category__mdt category : [SELECT MasterLabel FROM MF_Category__mdt]) {
            validCategories.add(category.MasterLabel);
        }
        
        // Query Bank_Transaction__c records with AI_Category__c not in valid categories
        List<Bank_Transaction__c> transactionsToUpdate = [
            SELECT Id, AI_Category__c 
            FROM Bank_Transaction__c
            WHERE AI_Category__c != NULL AND AI_Category__c NOT IN :validCategories
        ];
        
        if (!transactionsToUpdate.isEmpty()) {
            for (Bank_Transaction__c tx : transactionsToUpdate) {
                tx.AI_Category__c = null;
            }
            
            // Perform DML update
            try {
                update transactionsToUpdate;
            } catch (DmlException e) {
                System.debug('Error updating transactions: ' + e.getMessage());
            }
        }
    }
}