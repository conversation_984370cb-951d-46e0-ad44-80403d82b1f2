@IsTest
private class CreateCommunityUserTest {
    @testSetup
    static void setupTestData() {
        // Create a test Profile
        Profile communityUserProfile = [SELECT Id FROM Profile WHERE Name = 'Customer Community User' LIMIT 1];
        
        Account ac = new Account(name ='<PERSON><PERSON><PERSON><PERSON>') ;
		insert ac;

        // Create a test Contact
        Contact testContact = new Contact(
            FirstName = 'Test',
            LastName = 'User',
            Email = '<EMAIL>',
            AccountId = ac.Id
        );
        insert testContact;
    }

    @IsTest
    static void testCreateCommunityUserSuccess() {
        // Test data setup
        Profile profile = [SELECT Id FROM Profile WHERE Name Like 'Customer Community User' LIMIT 1];
        Contact contact = [SELECT Id, FirstName, LastName, Email FROM Contact WHERE Email LIKE '<EMAIL>' LIMIT 1];

        // Test Input
        CreateCommunityUser.inputVariables inputVar = new CreateCommunityUser.inputVariables();
        inputVar.profileId = profile.Id;
        inputVar.firstName = contact.FirstName;
        inputVar.lastName = contact.LastName;
        inputVar.email = contact.Email;
        inputVar.contactId = contact.Id;
        inputVar.isUserFromExistingAccount = true;

        List<CreateCommunityUser.inputVariables> inputVars = new List<CreateCommunityUser.inputVariables>{ inputVar };

        // Execute method
        Test.startTest();
        List<CreateCommunityUser.outputVariables> results = CreateCommunityUser.createCommunityUser(inputVars);
        Test.stopTest();

        // Verify results
        System.assertEquals(1, results.size());
        //System.assertNotEquals(null, results[0].userId, 'User should be created successfully.');
    }

    @IsTest
    static void testCreateCommunityUserExceptionHandling() {
        // Test Input with invalid profileId to force exceptions
        CreateCommunityUser.inputVariables inputVar = new CreateCommunityUser.inputVariables();
        inputVar.profileId = null;
        inputVar.firstName = 'Test';
        inputVar.lastName = 'User';
        inputVar.email = '<EMAIL>';
        inputVar.contactId = null;
        inputVar.isUserFromExistingAccount = true;

        List<CreateCommunityUser.inputVariables> inputVars = new List<CreateCommunityUser.inputVariables>{ inputVar };

        // Execute method
        Test.startTest();
        List<CreateCommunityUser.outputVariables> results = CreateCommunityUser.createCommunityUser(inputVars);
        Test.stopTest();

        // Verify results
        System.assertEquals(1, results.size());
        System.assertEquals(null, results[0].userId, 'User should not be created due to invalid profileId and contactId.');
    }

    @IsTest
    static void testSendEmailNotification() {
        // Retrieve test User
        Profile profile = [SELECT Id FROM Profile WHERE Name = 'Customer Community User' LIMIT 1];
        Contact contact = [SELECT Id FROM Contact WHERE Email = '<EMAIL>' LIMIT 1];
        
        User testUser = new User(
            ProfileId = profile.Id,
            EmailEncodingKey = 'ISO-8859-1',
            LanguageLocaleKey = 'en_US',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            FirstName = 'Test',
            LastName = 'User',
            Username = '<EMAIL>',
            CommunityNickname = 'testUser123',
            Alias = 'tusr',
            Email = '<EMAIL>',
            IsActive = true,
            ContactId = contact.Id
        );
        insert testUser;
              
        // Invoke the future method
        Test.startTest();
        CreateCommunityUser.sendEmailNotification(testUser.Id, 'password',contact.Id, true);
        Test.stopTest();
        
        // Verification logic can be added if email sending is mockable
        System.assert(true, 'Email notification should have been sent.');
    }
}