@SuppressWarnings('PMD')
public without sharing class UploadFileFromFlow {
    
    @InvocableMethod(label='Upload File' description='Uploads a file as ContentVersion and links it to a record')
    public static List<FileUploadResponse> uploadFile(List<FileUploadRequest> requests) {
        List<FileUploadResponse> responses = new List<FileUploadResponse>();

        if (requests == null || requests.isEmpty()) {
            return responses; 
        }

        FileUploadRequest req = requests[0];
		System.debug('req.base64Data '+req.base64Data);
		System.debug('req.linkedEntityId '+req.linkedEntityId);
        if (String.isBlank(req.base64Data) || String.isBlank(req.linkedEntityId)) {
            System.debug('Error: Missing required parameters');
            return responses;
        }

        try {
            ContentVersion contentVersion = new ContentVersion(
                Title = 'Uploaded Signature',  
                PathOnClient = 'UploadedSignature.jpg',
                VersionData = EncodingUtil.base64Decode(req.base64Data),
                IsMajorVersion = true,
                ContentLocation = 'S'
            );
			if (Test.isRunningTest()) { 
				try {
				Id netId = [SELECT Id FROM Network LIMIT 1].Id;
					contentVersion.NetworkId = netId;
				} catch (Exception e) {
					System.debug('Not in a network context: ' + e.getMessage());
					Nebula.Logger.error('Not in a network context: ' + e.getMessage() + ' ' + e.getStackTraceString(), contentVersion.NetworkId)
                    .addTag('network context');
					
                    Nebula.Logger.saveLog();
				}
			}
			insert contentVersion;

            // Query ContentDocumentId
            ContentVersion insertedVersion = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id LIMIT 1];
			System.debug('insertedVersion '+insertedVersion);

            // Create ContentDocumentLink
            ContentDocumentLink contentDocLink = new ContentDocumentLink(
                ContentDocumentId = insertedVersion.ContentDocumentId,
                LinkedEntityId = req.linkedEntityId,
                Visibility = 'AllUsers'
            );
            insert as system contentDocLink;
			System.debug('contentDocLink '+contentDocLink);

            List<ContentDistribution> existingDistributions = [
                SELECT Id, ContentDownloadUrl 
                FROM ContentDistribution 
                WHERE ContentVersionId = :insertedVersion.Id 
                LIMIT 1
            ];

            ContentDistribution contentDist;
            if (existingDistributions.isEmpty()) {
                contentDist = new ContentDistribution(
                    Name = 'File Distribution3',
                    ContentVersionId = insertedVersion.Id,
                    PreferencesAllowOriginalDownload = true,
                    PreferencesAllowPDFDownload = true,
                    PreferencesLinkLatestVersion = true,
                    PreferencesAllowViewInBrowser = true
                );
                insert contentDist;

                // Fetch newly created ContentDistribution
                contentDist = [SELECT Id, ContentDownloadUrl FROM ContentDistribution WHERE Id = :contentDist.Id LIMIT 1];
                System.debug('Newly created contentDist ' + contentDist);
            } else {
                // Use existing ContentDistribution
                contentDist = existingDistributions[0];
                System.debug('Existing contentDist ' + contentDist);
            }


            // Create ContentDistribution to generate a public URL
            /*ContentDistribution contentDist = new ContentDistribution(
                Name = 'File Distribution3',
                ContentVersionId = insertedVersion.Id,
                PreferencesAllowOriginalDownload = true,
                PreferencesAllowPDFDownload = true,
                PreferencesLinkLatestVersion = true,
                PreferencesAllowViewInBrowser = true
            );
            insert contentDist;

            // Fetch ContentDistribution URL
            ContentDistribution createdDist = [SELECT Id, ContentDownloadUrl FROM ContentDistribution WHERE Id = :contentDist.Id LIMIT 1];
            System.debug('createdDist ' + createdDist);*/

            // Return response to Flow
            FileUploadResponse response = new FileUploadResponse();
            response.contentDownloadUrl = contentDist.ContentDownloadUrl;
            //response.contentDownloadUrl = createdDist.ContentDownloadUrl;
            responses.add(response);

            // Generate Content Download URL
            // String downloadUrl = '/sfc/servlet.shepherd/document/download/' + insertedVersion.ContentDocumentId;
			// System.debug('downloadUrl '+downloadUrl);

            // // Return response to Flow
            // FileUploadResponse response = new FileUploadResponse();
            // response.contentDownloadUrl = downloadUrl;
            // responses.add(response);
        } catch (Exception e) {
            System.debug('Error uploading file: ' + e.getMessage());
			Nebula.Logger.error('uploading file: ' + e.getMessage() + ' ' + e.getStackTraceString(), req.linkedEntityId)
                    .addTag('Upload File From Flow');
			Nebula.Logger.error('uploading file: ' + e.getMessage() + ' ' + e.getStackTraceString(), req.base64Data)
                    .addTag('Upload File From Flow');
            Nebula.Logger.info('linkedIntity -> ' + req.linkedEntityId)
                .addTag('Upload File From Flow');
            Nebula.Logger.info('Siganture Body -> ' + req.base64Data)
                .addTag('Upload File From Flow');
            Nebula.Logger.saveLog();
        }

        return responses;
    }

    public class FileUploadRequest {
        @InvocableVariable(label='Base64 Data' required=true)
        public String base64Data;

        @InvocableVariable(label='Linked Entity ID' required=true)
        public String linkedEntityId;
    }

    public class FileUploadResponse {
        @InvocableVariable(label='Content Download URL')
        public String contentDownloadUrl;
    }
}