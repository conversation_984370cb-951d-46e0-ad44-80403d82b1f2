@SuppressWarnings('PMD')
public without sharing class ChatterPostTrackerController {
    
    @AuraEnabled
    public static List<Chatter_Post_Tracker_Owner__c> getTrackers() {
        System.debug('Fetching tracker owner records with Notification_Count__c < 3');
        List<Chatter_Post_Tracker_Owner__c> trackerOwners = [
            SELECT Id, Notification_Count__c, Chatter_Post_Tracker__r.Status__c, User__c, Chatter_Post_Tracker__r.Post_Link__c, 
                Chatter_Post_Tracker__r.Related_Opportunity__r.Name, 
                Chatter_Post_Tracker__r.Related_Project__r.Name, 
                Chatter_Post_Tracker__r.Related_Disbursement_Request__r.Name
            FROM Chatter_Post_Tracker_Owner__c 
            WHERE User__c = :UserInfo.getUserId() AND Chatter_Post_Tracker__r.Status__c = 'New' AND isChatterNotifyVisible__c = false
            
        ];
        System.debug('Tracker Owners fetched: ' + trackerOwners);

        return trackerOwners;
    }

    @AuraEnabled
    public static void updateChatterNotifyVisibility(Id trackerOwnerId) {
        Chatter_Post_Tracker_Owner__c tracker = [SELECT Id, isChatterNotifyVisible__c FROM Chatter_Post_Tracker_Owner__c WHERE Id = :trackerOwnerId LIMIT 1];
        tracker.isChatterNotifyVisible__c = true;
        update tracker;
    }

    @AuraEnabled
    public static void hideAllNotifications() {
        List<Chatter_Post_Tracker_Owner__c> trackers = [
            SELECT Id, isChatterNotifyVisible__c 
            FROM Chatter_Post_Tracker_Owner__c 
            WHERE User__c = :UserInfo.getUserId() 
                AND Chatter_Post_Tracker__r.Status__c = 'New'
                AND isChatterNotifyVisible__c = false
        ];
        
        for (Chatter_Post_Tracker_Owner__c tracker : trackers) {
            tracker.isChatterNotifyVisible__c = true;
        }
        
        if (!trackers.isEmpty()) {
            update trackers;
        }
    }

    @AuraEnabled
    public static void updateNotificationCount(List<Id> trackerOwnerIds) {
        System.debug('Updating Notification_Count__c for tracker owner IDs: ' + trackerOwnerIds);
        if(!trackerOwnerIds.isEmpty()) {
            List<Chatter_Post_Tracker_Owner__c> ownersToUpdate = [
            SELECT Id, Notification_Count__c 
            FROM Chatter_Post_Tracker_Owner__c 
            WHERE Id IN :trackerOwnerIds
            ];
            for (Chatter_Post_Tracker_Owner__c owner : ownersToUpdate) {
                if(owner.Notification_Count__c == null) {
                    owner.Notification_Count__c = 1;
                } 
                else if(owner.Notification_Count__c < 3) {
                    owner.Notification_Count__c += 1;
                }
                System.debug('Updated tracker owner: ' + owner);
            }
            update ownersToUpdate;
            System.debug('Tracker owners updated successfully');
        }
    }
    
    public static void createTrackerOwners(List<Chatter_Post_Tracker__c> postTrackers) {    
        if (postTrackers.isEmpty()) {
            return;
        }
    
        // Fetch Group Members once
        Map<String, Set<Id>> groupMemberMap = new Map<String, Set<Id>>();        
    
        for (GroupMember gm : [SELECT UserOrGroupId FROM GroupMember WHERE Group.Name = :System.label.MF_Chatter_Notification_Group_Name]) {
            if (!groupMemberMap.containsKey(System.label.MF_Chatter_Notification_Group_Name)) {
                groupMemberMap.put(System.label.MF_Chatter_Notification_Group_Name, new Set<Id>());
            }
            groupMemberMap.get(System.label.MF_Chatter_Notification_Group_Name).add(gm.UserOrGroupId);
        }
    
        // Extract Related Account and Opportunity IDs
        Set<Id> accountIds = new Set<Id>();
        Set<Id> opportunityIds = new Set<Id>();
    
        for (Chatter_Post_Tracker__c tracker : postTrackers) {
            if (tracker.Related_Account__c != null) {
                accountIds.add(tracker.Related_Account__c);
            }
            if (tracker.Related_Opportunity__c != null) {
                opportunityIds.add(tracker.Related_Opportunity__c);
            }
        }
    
        // Bulk query Accounts
        Map<Id, Account> accountMap = new Map<Id, Account>();
        if (!accountIds.isEmpty()) {
            for (Account acc : [SELECT Id, Assigned_MF_CRM__c, MF_Assigned_UW__c, MF_Assigned_Servicer__c, OwnerId 
                                FROM Account WHERE Id IN :accountIds]) {
                accountMap.put(acc.Id, acc);
            }
        }
    
        // Bulk query Opportunities
        Map<Id, Opportunity> opportunityMap = new Map<Id, Opportunity>();
        if (!opportunityIds.isEmpty()) {
            for (Opportunity opp : [SELECT Id, Assigned_MF_UW__c, Assigned_MF_Servicer__c, Assigned_MF_CRM__c 
                                    FROM Opportunity WHERE Id IN :opportunityIds]) {
                opportunityMap.put(opp.Id, opp);
            }
        }
    
        // Prepare list for insertion
        List<Chatter_Post_Tracker_Owner__c> trackerOwners = new List<Chatter_Post_Tracker_Owner__c>();
    
        for (Chatter_Post_Tracker__c tracker : postTrackers) {
            Set<Id> userIdsForOwner = new Set<Id>();
    
            // Get Users from Related Opportunity
            Opportunity opp = opportunityMap.get(tracker.Related_Opportunity__c);
            if (opp != null) {
                if (opp.Assigned_MF_CRM__c != null) {
                    userIdsForOwner.add(opp.Assigned_MF_CRM__c);
                }
                if (opp.Assigned_MF_UW__c != null) {
                    userIdsForOwner.add(opp.Assigned_MF_UW__c);
                }
                if (opp.Assigned_MF_Servicer__c != null) {
                    userIdsForOwner.add(opp.Assigned_MF_Servicer__c);
                }
            }
    
            // Get Users from Related Account
            Account acc = accountMap.get(tracker.Related_Account__c);
            if (acc != null) {
                if (acc.Assigned_MF_CRM__c != null) {
                    userIdsForOwner.add(acc.Assigned_MF_CRM__c);
                }
                if (acc.MF_Assigned_UW__c != null) {
                    userIdsForOwner.add(acc.MF_Assigned_UW__c);
                }
                if (acc.MF_Assigned_Servicer__c != null) {
                    userIdsForOwner.add(acc.MF_Assigned_Servicer__c);
                }
    
                // If no users found, add Account Owner
                if (userIdsForOwner.isEmpty() && acc.OwnerId != null) {
                    userIdsForOwner.add(acc.OwnerId);
                }
            }
    
            // Add Group Members
            if (groupMemberMap.containsKey(System.label.MF_Chatter_Notification_Group_Name)) {
                userIdsForOwner.addAll(groupMemberMap.get(System.label.MF_Chatter_Notification_Group_Name));
            }
    
            // Remove Assigned_To__c if exists
            if (tracker.Assigned_To__c != null && tracker.Client__c != null && tracker.Assigned_To__c == tracker.Client__c) {
                userIdsForOwner.remove(tracker.Assigned_To__c);
            }
    
            if (userIdsForOwner.isEmpty()) {
                continue;
            }
            
            Pattern pattern = Pattern.compile('@([A-Za-z]+(?: [A-Za-z]+)*)');
            Matcher matcher = pattern.matcher(tracker.Comments__c);
            System.debug('matcher: ' + matcher);
            Set<String> mentionUsers = new Set<String>();
            while (matcher.find()) {
                mentionUsers.add(matcher.group(1));
            }
            if (!mentionUsers.isEmpty()) {
                List<User> mentionedUsersFetch = [SELECT Id, Name, AccountId FROM User WHERE Name IN :mentionUsers];
                for (User usr : mentionedUsersFetch) {
                    userIdsForOwner.add(usr.Id);
                }
                System.debug('mentionUsers: ' + mentionUsers);
            }

            //remove current user
            String currentUserId = UserInfo.getUserId();
            for(Id usr : userIdsForOwner){
                if(usr == currentUserId) {
                    userIdsForOwner.remove(usr);
                }
            }
    
            // Create Tracker Owner records
            for (Id userId : userIdsForOwner) {
                if(userId != tracker.Client__c){
                 	trackerOwners.add(new Chatter_Post_Tracker_Owner__c(
                    	Chatter_Post_Tracker__c = tracker.Id,
                    	User__c = userId,
                    	Notification_Count__c = 0
                	));
                }
            }
        }
    
        // Insert records in bulk
        if (!trackerOwners.isEmpty()) {
            Database.insert(trackerOwners, AccessLevel.SYSTEM_MODE);
        } 
    }

    public static void notifyAccountOwners(List<Chatter_Post_Tracker__c> postTrackers) {
        System.debug('notifyAccountOwners ');
        Id notifyTypeId = getCustomNotificationTypeId();
        List<Messaging.CustomNotification> notifications = new List<Messaging.CustomNotification>();
        
        // Get a list of community users and their names
        Set<Id> communityUserIds = new Set<Id>();
        Set<Id> accntIds = new Set<Id>();
        Set<Id> opptyIds = new Set<Id>();
        
        for (Chatter_Post_Tracker__c tracker : postTrackers) {
            System.debug('in for loop notifyAccountOwners ');
            if (tracker.Client__c != null) {
                communityUserIds.add(tracker.Client__c);
            }
            if (tracker.Related_Account__c != null) {
            	accntIds.add(tracker.Related_Account__c);
            }
            if (tracker.Related_Opportunity__c != null) {
            	opptyIds.add(tracker.Related_Opportunity__c);
            }
        }
        
        Map<Id, Account> acntMap = new Map<Id, Account>([SELECT Id, Assigned_MF_CRM__c, MF_Assigned_Servicer__c, MF_Assigned_UW__c, ownerid FROM Account WHERE Id IN :accntIds]);
        //Map<Id, Opportunity> opptyMap = new Map<Id, Opportunity>([SELECT Id, Assigned_MF_CRM__c, Assigned_MF_Servicer__c, Assigned_MF_UW__c FROM Opportunity WHERE Id IN :opptyIds]);
        
        Map<Id, Opportunity> opptyMap = new Map<Id, Opportunity>();
        if (!opptyIds.isEmpty()) {
            for (Opportunity opp : [SELECT Id, Assigned_MF_UW__c, Assigned_MF_Servicer__c, Assigned_MF_CRM__c 
                                    FROM Opportunity WHERE Id IN :opptyIds]) {
                opptyMap.put(opp.Id, opp);
            }
        }
        
        Map<Id, User> communityUserMap = new Map<Id, User>();
        if (!communityUserIds.isEmpty()) {
            communityUserMap = new Map<Id, User>([SELECT Id, Name FROM User WHERE Id IN :communityUserIds]);
        }
        
        List<GroupMember> groupMembers = [SELECT Id, GroupId, Group.Name , UserOrGroupId FROM GroupMember Where Group.Name = :System.label.MF_Chatter_Notification_Group_Name];
        
        // Extract Group Member IDs into a Set
        Set<String> groupMemberIds = new Set<String>();
        for (GroupMember gm : groupMembers) {
            groupMemberIds.add(gm.UserOrGroupId);
        }
        
        for (Chatter_Post_Tracker__c tracker : postTrackers) {
            if(tracker.Related_Account__c != null) {
                Messaging.CustomNotification notification = new Messaging.CustomNotification();
                notification.setNotificationTypeId(notifyTypeId);
                
                Account acntObj = (Account) acntMap.get(tracker.Related_Account__c);
                Opportunity oppObj = (Opportunity) opptyMap.get(tracker.Related_Opportunity__c);
                
                Set<String> receiverIds = new Set<String>();
                if (oppObj != null) {
                    if(oppObj.Assigned_MF_CRM__c != null) {
                        receiverIds.add(oppObj.Assigned_MF_CRM__c);
                    }
                    if(oppObj.Assigned_MF_Servicer__c != null) {
                        receiverIds.add(oppObj.Assigned_MF_Servicer__c);
                    }
                    if(oppObj.Assigned_MF_UW__c != null) {
                        receiverIds.add(oppObj.Assigned_MF_UW__c);
                    }
                }
                
                if(acntObj.Assigned_MF_CRM__c != null) {
                    receiverIds.add(acntObj.Assigned_MF_CRM__c);
                }
                if(acntObj.MF_Assigned_Servicer__c != null) {
                    receiverIds.add(acntObj.MF_Assigned_Servicer__c);
                }
                if(acntObj.MF_Assigned_UW__c != null) {
                    receiverIds.add(acntObj.MF_Assigned_UW__c);
                }
                
                if(receiverIds.isEmpty() && acntObj.OwnerId != null) {
                    receiverIds.add(acntObj.OwnerId);
                }

                // Add Group Members
                receiverIds.addAll(groupMemberIds);
                // for(GroupMember gm : groupMembers) {
                //     receiverIds.add(gm.UserOrGroupId);
                // }

                if (tracker.Assigned_To__c != null && tracker.Client__c != null && tracker.Assigned_To__c == tracker.Client__c) {
                    receiverIds.remove(tracker.Assigned_To__c);
                }
                System.debug('receiverIds after filter: ' + receiverIds);

                if (receiverIds.isEmpty()) {
                    System.debug('No recipients left after filtering, skipping notification');
                    continue;
                }
        
                // Set the notification title based on whether Client__c is a community user
                if (tracker.Client__c != null && communityUserMap.containsKey(tracker.Client__c)) {
                    String userName = communityUserMap.get(tracker.Client__c).Name;
                    notification.setTitle(userName + ' posted a comment');
                } else {
                    notification.setTitle('Experience User posted a comment');
                }
        
                notification.setBody(tracker.Comments__c);
        
                String feedItemId = extractFeedItemId(tracker.Post_Link__c);
                if (feedItemId != null) {
                    notification.setTargetId(feedItemId);
                }

                notification.setSenderId(tracker.Assigned_To__c);
                
                Pattern pattern = Pattern.compile('@([A-Za-z]+(?: [A-Za-z]+)*)');
                Matcher matcher = pattern.matcher(tracker.Comments__c);
                System.debug('matcher: ' + matcher);
                Set<String> mentionUsers = new Set<String>();
                while (matcher.find()) {
                    mentionUsers.add(matcher.group(1));
                }
                if (!mentionUsers.isEmpty()) {
                    List<User> mentionedUsersFetch = [SELECT Id, Name, AccountId FROM User WHERE Name IN :mentionUsers];
                    for (User usr : mentionedUsersFetch) {
                        receiverIds.add(usr.Id);
                    }
                    System.debug('mentionUsers: ' + mentionUsers);
                }
                    
                for (Id userId : receiverIds) {
                	if(userId == tracker.Client__c){
                 		receiverIds.remove(userId);
                	}
            	}

                //remove current user
                String currentUserId = UserInfo.getUserId();
                Map<Id, User> activeUserMap = new Map<Id, User>([SELECT Id FROM User WHERE Id IN :receiverIds AND IsActive = true]);
                
                for(Id usr : receiverIds){
                    if(usr == currentUserId || !activeUserMap.containsKey(usr)) {
                        receiverIds.remove(usr);
                    }
                }

                sendNotification(receiverIds, notification);
                //notification.send(receiverIds);
            }
        }
    }

    public static void sendNotification(Set<String> receiverIds, Messaging.CustomNotification notification){
        if (receiverIds.isEmpty()){
            return;
        } 

        Set<String> internalReceiverIds = new Set<String>();
        Set<String> externalReceiverIds = new Set<String>();

        String toggleNotifyExt = System.Label.External_Chatter_Notification_Send;
        String toggleNotifyInt = System.Label.Internal_Chatter_Notification_Send;

        Map<Id, User> userMap = new Map<Id, User>([SELECT Id, AccountId FROM User WHERE Id IN :receiverIds]);
        for(String userId : userMap.keySet()){
            if(userMap.get(userId).AccountId != null){
                externalReceiverIds.add(userId);
            }
            else{
                internalReceiverIds.add(userId);
            }
        }
        if(toggleNotifyExt == 'Active' && !externalReceiverIds.isEmpty()){
            notification.send(externalReceiverIds);
        }
        if(toggleNotifyInt == 'Active' && !internalReceiverIds.isEmpty()){
            notification.send(internalReceiverIds);
        }
    }

    public static Id getCustomNotificationTypeId() {
        CustomNotificationType notifyType = [
            SELECT Id FROM CustomNotificationType 
            WHERE DeveloperName = 'NotifyToResponseOnCommunityPost' 
            LIMIT 1
        ];
        if(notifyType != null){
            return notifyType.Id;
        }
        else {
            return null;
        }
    }
    
  	public static String extractFeedItemId(String postLink) {
        if (postLink == null ) {
            return null;
        }
        
        if (postLink.startsWith('/')) {
            return postLink.substring(1);
        }
        
        return null;
    }
    
    public static String removeTags(String input) {
        if (input == null) {
            return null;
        }
        return input.replaceAll('<[^>]*>', '');
    }
}