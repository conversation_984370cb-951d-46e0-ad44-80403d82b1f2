@IsTest
public class ChatterPostTrackerControllerTest {
    @TestSetup
    static void setup() {
        Account testAccount = new Account(
            Name = 'Test Account',
            Assigned_MF_CRM__c = UserInfo.getUserId(),
            MF_Assigned_UW__c = UserInfo.getUserId(),
            MF_Assigned_Servicer__c = UserInfo.getUserId()
        );
        insert testAccount;
        
        Opportunity testOpp = new Opportunity(
            Name = 'Test Account',
            StageName = 'Funded',
            CloseDate = Date.today(),
            AccountId = testAccount.Id,
            Assigned_MF_CRM__c = UserInfo.getUserId(),
            Assigned_MF_Servicer__c = UserInfo.getUserId(),
            Assigned_MF_UW__c = UserInfo.getUserId()
        );
        insert testOpp;

        Chatter_Post_Tracker__c postTracker = new Chatter_Post_Tracker__c(
            Related_Account__c = testAccount.Id,
            Status__c = 'New',
            Post_Link__c = '/feedItemId',
            Comments__c = 'asdf'
        );
        insert postTracker;
        
         Chatter_Post_Tracker__c postTracker2 = new Chatter_Post_Tracker__c(
            Related_Opportunity__c = testOpp.Id,
            Status__c = 'New',
            Post_Link__c = '/feedItemId',
            Comments__c = 'asdf'
        );
        insert postTracker2;

        Chatter_Post_Tracker_Owner__c trackerOwner = new Chatter_Post_Tracker_Owner__c(
            Chatter_Post_Tracker__c = postTracker.Id,
            User__c = UserInfo.getUserId(),
            Notification_Count__c = 0,
            isChatterNotifyVisible__c = false
        );
        insert trackerOwner;

    }
    
     @IsTest
    static void testUpdateChatterNotifyVisibility() {
        Chatter_Post_Tracker_Owner__c tracker = [SELECT Id FROM Chatter_Post_Tracker_Owner__c LIMIT 1];
        
        Test.startTest();
        ChatterPostTrackerController.updateChatterNotifyVisibility(tracker.Id);
        Test.stopTest();
    }
    
     @IsTest
    static void testHideAllNotifications() {
        Test.startTest();
        ChatterPostTrackerController.hideAllNotifications();
        Test.stopTest();
    }

    @IsTest
    static void testGetTrackers() {
        Test.startTest();
        List<Chatter_Post_Tracker_Owner__c> trackers = ChatterPostTrackerController.getTrackers();
        Test.stopTest();
    }

    @IsTest
    static void testUpdateNotificationCount() {
        List<Chatter_Post_Tracker_Owner__c> trackerOwners = [
            SELECT Id, Notification_Count__c FROM Chatter_Post_Tracker_Owner__c
        ];

        List<Id> trackerOwnerIds = new List<Id>();
        for (Chatter_Post_Tracker_Owner__c owner : trackerOwners) {
            trackerOwnerIds.add(owner.Id);
        }

        Test.startTest();

        ChatterPostTrackerController.updateNotificationCount(trackerOwnerIds);

            Test.stopTest();

        List<Chatter_Post_Tracker_Owner__c> updatedOwners = [
            SELECT Id, Notification_Count__c FROM Chatter_Post_Tracker_Owner__c
        ];
    }

    @IsTest
    static void testCreateTrackerOwners() {
        Chatter_Post_Tracker__c postTracker = new Chatter_Post_Tracker__c(
            Related_Account__c = [SELECT Id FROM Account LIMIT 1].Id,
            Status__c = 'New',
            Post_Link__c = '/dummyFeedItem',
            Comments__c= 'asdf'
        );
        insert postTracker;
        
        Chatter_Post_Tracker__c postTracker2 = new Chatter_Post_Tracker__c(
            Related_Opportunity__c = [SELECT Id FROM Opportunity LIMIT 1].Id,
            Status__c = 'New',
            Post_Link__c = '/dummyFeedItem',
            Comments__c= 'asdf'
        );
        insert postTracker2;

        List<Chatter_Post_Tracker__c> postTrackers = new List<Chatter_Post_Tracker__c>{ postTracker, postTracker2 };

        Test.startTest();
        ChatterPostTrackerController.createTrackerOwners(postTrackers);
        Test.stopTest();

        List<Chatter_Post_Tracker_Owner__c> trackerOwners = [
            SELECT Id, User__c, Notification_Count__c FROM Chatter_Post_Tracker_Owner__c
        ];
    }

    @IsTest
    static void testNotifyAccountOwners() {
        Chatter_Post_Tracker__c postTracker = new Chatter_Post_Tracker__c(
            Related_Account__c = [SELECT Id FROM Account LIMIT 1].Id,
            Status__c = 'New',
            Comments__c = 'Test Comment',
            Post_Link__c = '/dummyFeedItem'
        );
        insert postTracker;

        List<Chatter_Post_Tracker__c> postTrackers = new List<Chatter_Post_Tracker__c>{ postTracker };

        Test.startTest();
        ChatterPostTrackerController.notifyAccountOwners(postTrackers);
        Test.stopTest();
    }
    
        @IsTest
    static void testRemoveTags() {
        String input = '<b>Bold Text</b> with <i>italic</i> and <script>alert("XSS")</script>';
        String expectedOutput = 'Bold Text with italic and alert("XSS")';

        Test.startTest();
        String result = ChatterPostTrackerController.removeTags(input);
        Test.stopTest();
    }
}