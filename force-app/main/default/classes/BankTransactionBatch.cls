public class BankTransactionBatch implements Database.Batchable<sObject>, Database.Stateful {
    
    public Map<String, String> categoryMapping = new Map<String, String>();
    
    public Database.QueryLocator start(Database.BatchableContext bc) {
        // Preload all active category mappings from metadata
        for(Flinks_Category__mdt fc : [
            SELECT Category__c, Sub_Category__c, MF_Category__r.Label 
            FROM Flinks_Category__mdt 
            WHERE Active__c = true
        ]) {
            if(String.isNotBlank(fc.Category__c) && String.isNotBlank(fc.Sub_Category__c)) {
                String key = fc.Category__c.toLowerCase() + '|' + fc.Sub_Category__c.toLowerCase();
                categoryMapping.put(key, fc.MF_Category__r.Label);
            }
        }

		System.debug('categoryMapping -> ' + categoryMapping);        
        System.debug('earned income|wages & salary -> ' + categoryMapping.get('earned income|wages & salary'));
        
        // Query all bank transactions that need processing
        return Database.getQueryLocator(
            'SELECT Id, Category__c, Sub_Category__c, MF_Category__c ' +
            'FROM Bank_Transaction__c'
        );
    }
    
    public void execute(Database.BatchableContext bc, List<Bank_Transaction__c> scope) {
        List<Bank_Transaction__c> recordsToUpdate = new List<Bank_Transaction__c>();
        
        for(Bank_Transaction__c bt : scope) {
            String transactionKey = null;
            
            if(String.isNotBlank(bt.Category__c) && String.isNotBlank(bt.Sub_Category__c)) {
                transactionKey = bt.Category__c.toLowerCase() + '|' + bt.Sub_Category__c.toLowerCase();
            }
            System.debug('transactionKey -> ' + transactionKey);
            System.debug('categoryMapping -> ' + categoryMapping);
            String newCategory = categoryMapping.get(transactionKey);
            System.debug('newCategory -> ' + newCategory);
            
            // Only update if the value actually changes
            if(bt.MF_Category__c != newCategory) {
                bt.MF_Category__c = newCategory;
                recordsToUpdate.add(bt);
            }
        }
		System.debug('recordsToUpdate ->' + recordsToUpdate.size());
        if(!recordsToUpdate.isEmpty()) {
            update recordsToUpdate;
        }
    }
    
    public void finish(Database.BatchableContext bc) {
        // Optional: Add notification logic here
        System.debug('Batch processing completed');
    }

}