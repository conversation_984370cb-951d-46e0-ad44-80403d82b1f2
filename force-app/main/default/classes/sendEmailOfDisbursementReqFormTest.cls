@IsTest
public class sendEmailOfDisbursementReqFormTest {

    @IsTest
    static void testSendEmailOfDisbursementRequestForm() {
        // Create test data
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        
        Disbursement_Request__c disbursementRequest = new Disbursement_Request__c(
            Account_Name__c = 'Test Account Name',
            Zip_Code__c = '12345',
            Street_Address__c = '123 Test Street',
              Payee_Name__c = 'Test Payee',
			Loan_Number__c = 'LN12345',
            Disbursement_Type__c = 'Material',
            Payee_Contact_Name__c = 'Test Payee Contact',
            Payee_Contact_Email__c = '<EMAIL>',
            Status__c = 'Processing',
            State__c = 'CA',
            Bank_Account_Number__c = 'Lntmh'
           
        );
        insert disbursementRequest;
        
        Requested_Item__c requestedItem = new Requested_Item__c(
            Name = 'Test Item',
            Description_Work__c = 'Test Description',
            Invoice_Date__c = Date.today(),
            Invoice_Amount__c = 1000,
            Invoice_Due_Date__c = Date.today() + 30,
            Invoice__c = 'INV12345',
            Disbursement_Request__c = disbursementRequest.Id
        );
        insert requestedItem;
        
        Note note = new Note(
            Title = 'Test Note',
            Body = 'Test Note Body',
            ParentId = disbursementRequest.Id
        );
        insert note;

        // Set up input variables
        sendEmailOfDisbursementReqForm.inputVariables inputVars = new sendEmailOfDisbursementReqForm.inputVariables();
        inputVars.disbursementReqId = disbursementRequest.Id;
        inputVars.requestedItemIds = new List<Requested_Item__c>{requestedItem};
        inputVars.noteId = new List<Note>{note};
        inputVars.contentDownloadUrl = 'https://platform-customer-2576-dev-ed.scratch.my.salesforce.com/sfc/dist/version/download/?oid=00DE200000D0a7F&ids=068E2000009lfs9&d=%2Fa%2FE2000000gWgD%2FQNHPwyncpjTkhSbJguox6RY2YBFEumARu0gN7Q23TvQ&asPdf=false';

        List<sendEmailOfDisbursementReqForm.inputVariables> inputList = new List<sendEmailOfDisbursementReqForm.inputVariables>();
        inputList.add(inputVars);

        // Call the method
        Test.startTest();
        List<sendEmailOfDisbursementReqForm.outputVariables> results = sendEmailOfDisbursementReqForm.sendEmailOfDisbursementRequestForm(inputList);
        Test.stopTest();

        // Verify results
        System.assertEquals(1, results.size());
        System.assert(results[0].result, 'The result should be true.');

    }
    
    @IsTest
    static void testSendEmailOfDisbursementRequestForm1() {
        // Create test data
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        
        Disbursement_Request__c disbursementRequest = new Disbursement_Request__c(
            Account_Name__c = 'Test Account Name',
            Zip_Code__c = null,
            Street_Address__c = null,
              Payee_Name__c = null,
			Loan_Number__c = null,
            Disbursement_Type__c = 'Material',
            Payee_Contact_Name__c = 'Test Payee Contact',
            Payee_Contact_Email__c = '<EMAIL>',
            Project_lookup__c = null,
            State__c = 'CA',
            Payment_Method__c = null 
           
        );
        insert disbursementRequest;
        
        Requested_Item__c requestedItem = new Requested_Item__c(
            Name = 'Test Item',
            Description_Work__c = 'Test Description',
            Invoice_Date__c = Date.today(),
            Invoice_Amount__c = 1000,
            Invoice_Due_Date__c = Date.today() + 30,
            Invoice__c = 'INV12345',
            Disbursement_Request__c = disbursementRequest.Id
        );
        insert requestedItem;
        
        Note note = new Note(
            Title = 'Test Note',
            Body = 'Test Note Body',
            ParentId = disbursementRequest.Id
        );
        insert note;

        // Set up input variables
        sendEmailOfDisbursementReqForm.inputVariables inputVars = new sendEmailOfDisbursementReqForm.inputVariables();
        inputVars.disbursementReqId = disbursementRequest.Id;
        inputVars.requestedItemIds = new List<Requested_Item__c>{requestedItem};
        inputVars.noteId = new List<Note>{note};
        inputVars.contentDownloadUrl = 'https://platform-customer-2576-dev-ed.scratch.my.salesforce.com/sfc/dist/version/download/?oid=00DE200000D0a7F&ids=068E2000009lfs9&d=%2Fa%2FE2000000gWgD%2FQNHPwyncpjTkhSbJguox6RY2YBFEumARu0gN7Q23TvQ&asPdf=false';

        List<sendEmailOfDisbursementReqForm.inputVariables> inputList = new List<sendEmailOfDisbursementReqForm.inputVariables>();
        inputList.add(inputVars);

        // Call the method
        Test.startTest();
        List<sendEmailOfDisbursementReqForm.outputVariables> results = sendEmailOfDisbursementReqForm.sendEmailOfDisbursementRequestForm(inputList);
        Test.stopTest();

        // Verify results
        System.assertEquals(1, results.size());
        System.assert(results[0].result, 'The result should be true.');

    }
}