/**
 * @description Controller to fetch Cashflow records related to a Project.
 */
@SuppressWarnings('PMD.AvoidDeeplyNestedIfStmts, PMD.CognitiveComplexity, PMD.ExcessiveMethodLength,PMD')
public with sharing class ProjectCashflowController {

    private static final String CLASS_NAME = 'ProjectCashflowController';
    private static final List<String> LOG_TAGS = new List<String>{CLASS_NAME, 'ProjectCashflowQuery'};

    /**
     * @description Fetches all Cashflow__c records related to a given Project__c ID.
     * @param projectId The ID of the Project__c record.
     * @return List<Cashflow__c> A list of related Cashflow records.
     * @throws AuraHandledException if the projectId is blank or if a query exception occurs.
     */
    @AuraEnabled(cacheable=true)
    public static List<Cashflow__c> getCashflowsForProject(String projectId) {
        final String METHOD_NAME = 'getCashflowsForProject';
        Map<String, Object> params = new Map<String, Object>{'projectId' => projectId};
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, params, LOG_TAGS);

        if (String.isBlank(projectId)) {
            DebugLogUtil.error(METHOD_NAME + ': Project ID is required but was blank.', LOG_TAGS);
            throw new AuraHandledException('Project ID is required to fetch cashflows.');
        }

        List<Cashflow__c> cashflows = new List<Cashflow__c>();
        try {
            cashflows = [
                SELECT 
                    Id, 
                    Name, 
                    Status__c, 
                    Total_Project_Value__c, 
                    Forecast_Start_Date__c, 
                    Forecast_End_Date__c,
                    Version_Number__c,
                    Project_Start_Date__c,
                    CreatedDate
                FROM Cashflow__c 
                WHERE Project__c = :projectId
                ORDER BY CreatedDate DESC
            ];
            DebugLogUtil.info(METHOD_NAME + ': Fetched ' + cashflows.size() + ' Cashflow__c records for Project ID: ' + projectId, LOG_TAGS);
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Error fetching Cashflow__c records for Project ID: ' + projectId, e, LOG_TAGS);
            DebugLogUtil.saveLogs();
            
            throw new AuraHandledException('An error occurred while fetching cashflow records: ' + e.getMessage());
        }
        
        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
        return cashflows;
    }

    /**
     * @description Deletes a Cashflow__c record and its related children.
     * @param cashflowId The ID of the Cashflow__c record to delete.
     * @throws AuraHandledException if the cashflowId is blank or if a DML exception occurs.
     */
    @AuraEnabled
    public static void deleteCashflow(String cashflowId) {
        if (String.isBlank(cashflowId)) {
            throw new AuraHandledException('Cashflow ID is required to perform deletion.');
        }

        // Use a savepoint to roll back the entire transaction if any part fails.
        Savepoint sp = Database.setSavepoint();
        try {
            // Find all related Cashflow Line Items.
            List<Cashflow_Line_Item__c> lineItems = [
                SELECT Id 
                FROM Cashflow_Line_Item__c 
                WHERE Cashflow__c = :cashflowId
            ];

            if (!lineItems.isEmpty()) {
                Set<Id> lineItemIds = new Map<Id, SObject>(lineItems).keySet();

                // Delete child records that are connected via a LOOKUP relationship.
                // Master-Detail children will be cascade-deleted automatically.

                // 1. Delete related Cashflow_Line_Item_Child__c records (assuming lookup).
                List<Cashflow_Line_Item_Child__c> childItems = [
                    SELECT Id FROM Cashflow_Line_Item_Child__c WHERE Cashflow_Line_Item__c IN :lineItemIds
                ];
                if (!childItems.isEmpty()) {
                    delete childItems;
                }

                // 2. Delete related Cashflow_Line_Item_History__c records (assuming lookup).
                List<Cashflow_Line_Item_History__c> historyItems = [
                    SELECT Id FROM Cashflow_Line_Item_History__c WHERE Cashflow_Line_Item__c IN :lineItemIds
                ];
                if (!historyItems.isEmpty()) {
                    delete historyItems;
                }
            }
            
            // 3. Delete the parent Cashflow__c record.
            // This will trigger a cascade-delete for all records in a Master-Detail relationship,
            // including Cashflow_Line_Item__c and its own MD children (Junctions, Disbursements, etc.).
            List<Cashflow__c> cashflowToDelete = [SELECT Id FROM Cashflow__c WHERE Id = :cashflowId];
            if(!cashflowToDelete.isEmpty()){
                 delete cashflowToDelete;
            }

        } catch (Exception e) {
            Database.rollback(sp); // Rollback on error.
            throw new AuraHandledException('An error occurred while deleting the cashflow: ' + e.getMessage());
        }
    }
}