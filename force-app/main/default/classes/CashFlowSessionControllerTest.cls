@isTest
private class CashFlowSessionControllerTest {

    // Test: Fetching records
    @isTest static void testGetRecords() {
        insert new Cash_Flow_Session__c(Name = 'Session 1', SessionId__c = 'SID1', CreatedAt__c = DateTime.now());
        Test.startTest();
        List<Cash_Flow_Session__c> result = cashFlowSessionController.getRecords();
        Test.stopTest();
        System.assertNotEquals(null, result);
        System.assert(result.size() > 0, 'Should return at least one record');
    }

    // Test: HTTP callout
    @isTest static void testShareSessionWithApex() {
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());
        Test.startTest();
        Map<String,Object> res = cashFlowSessionController.shareSessionWithApex('https://example.com/test');
        Test.stopTest();
        System.assertEquals(200, res.get('statusCode'));
        System.assertEquals('Success', res.get('body'));
    }

    private class MockHttpResponseGenerator implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse r = new HttpResponse();
            r.setStatusCode(200);
            r.setBody('Success');
            return r;
        }
    }

    // Test: Invalid JSON
    @isTest static void testCreateRecords_InvalidJSON() {
        Account acct = new Account(Name = 'A'); insert acct;
        Project__c proj = new Project__c(Name = 'P', Account_Name__c = acct.Id); insert proj;

        Map<String,Object> res = cashFlowSessionController.createRecordsFromDynamoData(acct.Id, '{bad', proj.Id);
        System.assertEquals(false, res.get('success'));
        System.assert(((String)res.get('errorMessage')).startsWith('Error parsing input JSON'));
    }

    // Test: Missing required parameters
    @isTest static void testCreateRecords_MissingParams() {
        Map<String,Object> res = cashFlowSessionController.createRecordsFromDynamoData('', '', '');
        System.assertEquals(false, res.get('success'));
        System.assertEquals('JSON data and ProjectId are required.', res.get('errorMessage'));
    }

    // Test: MappingRule class structure
    @isTest static void testMappingRuleInstantiation() {
        cashFlowSessionController.MappingRule rule = new cashFlowSessionController.MappingRule();
        rule.dynamoDbId = 'some/path';
        rule.salesforceObject = 'Cashflow__c';
        rule.salesforceFieldApiName = 'Estimated_Margin_Percent__c';
        rule.fieldName = 'Estimated Margin';
        rule.intakeType = 'Construction';
        rule.intakeFormPageSection = 'Details';

        System.assertEquals('some/path', rule.dynamoDbId);
        System.assertEquals('Estimated Margin', rule.fieldName);
    }

    // Test: Decimal parsing
    @isTest static void testDecimalParsing() {
        Map<String, Object> data = new Map<String, Object>{ 'key' => '1,234.56 USD' };
        Decimal val = cashFlowSessionController.getDecimalValue(data, 'key');
       // System.assertEquals(1234.56, val.setScale(2));
    }

    // Test: Boolean parsing
    @isTest static void testBooleanParsing() {
        Map<String, Object> data = new Map<String, Object>{ 'yesVal' => 'yes', 'noVal' => 'no' };
        System.assertEquals(true, cashFlowSessionController.getBooleanValue(data, 'yesVal'));
        System.assertEquals(false, cashFlowSessionController.getBooleanValue(data, 'noVal'));
    }
    
    @isTest static void testCreateRecords_HappyPath() {
    // Setup minimal data
    Account acct = new Account(Name = 'Test Account');
    insert acct;

    Project__c proj = new Project__c(Name = 'Test Project', Account_Name__c = acct.Id);
    insert proj;

    String json = JSON.serialize(new Map<String, Object>{
        'details' => new Map<String, Object>{
            'projectName' => 'Test Cashflow',
            'totalValue' => 100000,
            'totalEstimatedDirectPayroll' => 10000,
            'totalEstimatedSubcontractLabor' => 20000,
            'totalEstimatedMaterial' => 15000,
            'totalEstimatedEquipmentRental' => 5000,
            'totalEstimatedBondPremium' => 2500,
            'totalEstimatedMiscExpenses' => 1000,
            'retainagePercent' => 5,
            'projectLengthWeeks' => 12,
            'payAppFrequency' => 'Monthly',
            'paymentDelayDays' => 10,
            'contractorName' => 'Big Builder Inc.',
            'marginPercent' => 15,
            'moneyReceivedToDate' => 10000,
            'jointChecks' => 'yes',
            'startDate' => '2025-06-01',
            'computed' => new Map<String, Object>{
                'weekStart' => '2025-06-03',
                'weekdEnd' => '2025-09-03'
            }
        },
        'income' => new Map<String, Object>{
            'lineItems' => new List<Object>{
                new Map<String, Object>{
                    'id' => 'income-1',
                    'amount' => 5000,
                    'week' => '2025-06-07',
                    'weekDue' => '2025-06-14'
                }
            }
        },
        'expenses' => new List<Object>{
            new Map<String, Object>{
                'id' => 'group-1',
                'category' => 'materialOrders',
                'amount' => 2000,
                'schedule' => 'Fixed',
                'paymentFrequency' => 'Weekly',
                'terms' => 'Net 30',
                'name' => 'Vendor 1',
                'lineItems' => new List<Object>{
                    new Map<String, Object>{
                        'id' => 'exp-1',
                        'amount' => 1000,
                        'week' => '2025-06-07',
                        'weekDue' => '2025-06-14'
                    }
                }
            }
        }
    });

    // Call main method
    Test.startTest();
    Map<String, Object> result = cashFlowSessionController.createRecordsFromDynamoData(acct.Id, json, proj.Id);
    Test.stopTest();

   // System.assertEquals(true, result.get('success'));
    //System.assert(result.containsKey('cashflowId'));
   // System.assert(result.containsKey('projectId'));

    // Optional: query and assert specific record types
    List<Cashflow__c> flows = [SELECT Id FROM Cashflow__c WHERE Project__c = :proj.Id];
  //  System.assertEquals(1, flows.size());
}

    @isTest static void testAssignFieldValueVariants() {
    SObject testRecord = new Cashflow__c();
    cashFlowSessionController.assignFieldValue(testRecord, 'Estimated_Margin_Percent__c', '15.2');
    cashFlowSessionController.assignFieldValue(testRecord, 'Joint_Checks_Required__c', 'yes');
    cashFlowSessionController.assignFieldValue(testRecord, 'Project_Start_Date__c', '2025-07-01');
    cashFlowSessionController.assignFieldValue(testRecord, 'Some_Invalid_Field', 'value'); // safe fallback
}

@isTest static void testCreateRecords_CloneFinancingSources() {
    Account acct = new Account(Name = 'Account for Clone Test');
    insert acct;
    
    Project__c proj = new Project__c(Name = 'Project for Clone Test', Account_Name__c = acct.Id);
    insert proj;

    // 1. Create a "previous" cashflow to trigger clone mode
    Cashflow__c prev = new Cashflow__c(
        Name = 'Previous Flow',
        Project__c = proj.Id,
        Version_Number__c = 1
    );
    insert prev;

    // 2. Add Financing Source parent CLI
    Cashflow_Line_Item__c cli = new Cashflow_Line_Item__c(
        Cashflow__c = prev.Id,
        Line_Item_Category__c = 'Disbursement',
        Type__c = 'Financing Source',
        Planned_Amount__c = 2000,
        Week_Start_Date__c = Date.today()
    );
    insert cli;

    Disbursement__c disb = new Disbursement__c(
        Project__c = proj.Id,
        Date_Paid__c = Date.today()
    );
    insert disb;

    // 3. Create the junction record linking the parent and the disbursement
    insert new Cashflow_Weekly_Line_Disbursement__c(
        Cashflow_Line_Item__c = cli.Id,
        Disbursement__c = disb.Id 
    );

    // 4. Now invoke the method that should trigger the cloning logic
    Test.startTest();
    Map<String,Object> result = cashFlowSessionController.createRecordsFromDynamoData(acct.Id, '{"details": {"projectName": "Clone Scenario","totalValue": "500000","retainagePercent": "5","marginPercent": "15","projectLengthWeeks": "12","payAppFrequency": "Weekly","startDate": "2025-06-01"},"income": {"lineItems": []},"expenses": []}', proj.Id);
    Test.stopTest();

    // 5. Assertions to verify the cloning was successful
    System.assertEquals(true, result.get('success'), 'Method should execute successfully.');
    System.assert(result.containsKey('cashflowId'), 'Result should contain the new cashflow ID.');

    Id newCashflowId = (Id)result.get('cashflowId');
    System.assertNotEquals(null, newCashflowId, 'New cashflow ID should not be null.');

    // Verify the new parent was created
    List<Cashflow_Line_Item__c> newParents = [
        SELECT Id, Planned_Amount__c, Line_Item_Category__c, CreatedDate
        FROM Cashflow_Line_Item__c 
        WHERE Cashflow__c = :newCashflowId AND Type__c = 'Financing Source'
        ORDER BY CreatedDate ASC
    ];
    
    System.debug('TEST DEBUG: Found ' + newParents.size() + ' financing source parents');
    for (Cashflow_Line_Item__c parent : newParents) {
        System.debug('TEST DEBUG: Parent: ' + parent);
    }
    System.assertEquals(1, newParents.size(), 'Should have cloned exactly one financing source parent item.');
    System.assertEquals(2000, newParents[0].Planned_Amount__c, 'Cloned parent should have the correct amount.');

    // Verify the child junction was created and correctly linked
    List<Cashflow_Weekly_Line_Disbursement__c> newJunctions = [
        SELECT Id, Disbursement__c 
        FROM Cashflow_Weekly_Line_Disbursement__c 
        WHERE Cashflow_Line_Item__c = :newParents[0].Id
    ];
    System.assertEquals(1, newJunctions.size(), 'Should have cloned exactly one disbursement junction record.');
    System.assertEquals(disb.Id, newJunctions[0].Disbursement__c, 'Cloned junction should point to the original disbursement.');
}

    }