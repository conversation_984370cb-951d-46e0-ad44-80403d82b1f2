@isTest
public class CustomCommentFeederControllerTest {
    
    // Mock class for HTTP callouts (for RTA image processing)
    private class MockHttpResponseGenerator implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'image/png');
            res.setBodyAsBlob(Blob.valueOf('Mock Image Data'));
            res.setStatusCode(200);
            return res;
        }
    }
    
    // Utility method to set up common test data
    private static void setupTestData() {
        Account testAccount = new Account(Name = 'Test Account ' + System.currentTimeMillis());
        insert testAccount;
        
        Contact testContact = new Contact(
            FirstName = 'John', 
            LastName = 'Doe', 
            Email = 'john.doe' + System.currentTimeMillis() + '@example.com', 
            AccountId = testAccount.Id
        );
        insert testContact;
        
        Profile communityProfile = [SELECT Id FROM Profile WHERE Name = 'Customer Community User' LIMIT 1]; 
        User communityUser = new User(
            FirstName = 'Community',
            LastName = 'User',
            Email = 'community.user' + System.currentTimeMillis() + '@example.com',
            Username = 'community.user' + System.currentTimeMillis() + '@example.com.test',
            ProfileId = communityProfile.Id,
            Alias = 'cuser',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            ContactId = testContact.Id,
            CommunityNickname = 'CommunityUserNick' + System.currentTimeMillis(),
            IsActive = true
        );
        insert communityUser;
        
        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity ' + System.currentTimeMillis(),
            StageName = 'Prospecting',
            CloseDate = Date.today().addMonths(1),
            Type = 'New Business',
            AccountId = testAccount.Id
        );
        insert testOpportunity;
    }
    
    @isTest
    static void testGetOpportunityType() {
        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity ' + System.currentTimeMillis(),
            StageName = 'Prospecting',
            CloseDate = Date.today().addMonths(1),
            Type = 'New Business'
        );
        insert testOpportunity;
        
        Test.startTest();
        Opportunity result = CustomCommentFeederController.getOpportunityType(testOpportunity.Id);
        Test.stopTest();
    }
    
    @isTest
    static void testGetOpportunityTypeException() {
        Test.startTest();
        try {
            CustomCommentFeederController.getOpportunityType(null);
        } catch (AuraHandledException e) {
            System.assert(true, 'Exception caught as expected');
        }
        Test.stopTest();
    }
    
    @isTest
    static void testGetFeedItemListWithContentAndTopics() {
        setupTestData();
        Opportunity testOpp = [SELECT Id FROM Opportunity WHERE Name LIKE 'Test Opportunity%' LIMIT 1];
        
        FeedItem feedItem = new FeedItem(
            Body = 'Test feed item with link sfdc://' + testOpp.Id,
            ParentId = testOpp.Id,
            Type = 'TextPost',
            Visibility = 'AllUsers'
        );
        insert feedItem;
        
        FeedComment feedComment = new FeedComment(
            FeedItemId = feedItem.Id,
            CommentBody = 'Test comment with link sfdc://' + feedItem.Id
        );
        insert feedComment;
        
        // Use a unique Topic name
        String uniqueTopicName = 'TestTopic' + System.currentTimeMillis();
        Topic testTopic = new Topic(Name = uniqueTopicName);
        insert testTopic;
        TopicAssignment ta = new TopicAssignment(
            EntityId = feedItem.Id,
            TopicId = testTopic.Id
        );
        insert ta;
        
        ContentVersion cv = new ContentVersion(
            Title = 'Test File ' + System.currentTimeMillis(),
            PathOnClient = 'test.pdf',
            VersionData = Blob.valueOf('Test Content'),
            FirstPublishLocationId = testOpp.Id
        );
        insert cv;
        
        // Use a unique ContentDistribution name
        String uniqueDistName = 'Test Distribution ' + System.currentTimeMillis();
         List<ContentDistribution> existingDistributions = [
                SELECT Id, ContentDownloadUrl 
                FROM ContentDistribution 
                WHERE ContentVersionId = :cv.Id 
                LIMIT 1
            ];
		
        if (existingDistributions.isEmpty()) {
            ContentDistribution cd = new ContentDistribution(
                ContentVersionId = cv.Id,
                Name = uniqueDistName,
                PreferencesAllowViewInBrowser = true
            );
            insert cd;
        }
        
        Test.startTest();
        List<CustomCommentFeederController.ReturnWrapper> result = CustomCommentFeederController.getFeedItemList(testOpp.Id, 'DESC');
        Test.stopTest();
       
    }
    
    @isTest
    static void testGetFeedItemListEmpty() {
        setupTestData();
        Opportunity testOpp = [SELECT Id FROM Opportunity WHERE Name LIKE 'Test Opportunity%' LIMIT 1];
        
        Test.startTest();
        List<CustomCommentFeederController.ReturnWrapper> result = CustomCommentFeederController.getFeedItemList(testOpp.Id, 'ASC');
        Test.stopTest();
        
    }
    
    @isTest
    static void testDeleteFeedItem() {
        setupTestData();
        Opportunity testOpp = [SELECT Id FROM Opportunity WHERE Name LIKE 'Test Opportunity%' LIMIT 1];
        User communityUser = [SELECT Id FROM User WHERE Email LIKE '<EMAIL>' LIMIT 1];
        
        FeedItem feedItem = new FeedItem(
            Body = 'Test feed item body',
            ParentId = testOpp.Id,
            Type = 'TextPost'
        );
        insert feedItem;

        FeedComment feedComment = new FeedComment(
            FeedItemId = feedItem.Id,
            CommentBody = 'Test feed comment body',
            CreatedById = communityUser.Id
        );
        insert feedComment;
        
        Test.startTest();
        CustomCommentFeederController.deleteFeedItem(feedItem.Id);
        Test.stopTest();
        
        Integer feedItemCount = [SELECT COUNT() FROM FeedItem WHERE Id = :feedItem.Id];
        Integer feedCommentCount = [SELECT COUNT() FROM FeedComment WHERE FeedItemId = :feedItem.Id];

    }
    
    @isTest
    static void testDeleteFeedItemException() {
        Test.startTest();
        try {
            CustomCommentFeederController.deleteFeedItem(null);
        } catch (AuraHandledException e) {
            System.assert(true, 'Exception caught as expected');
        }
        Test.stopTest();
    }
    
    @isTest
    static void testDeleteFeedCommentItem() {
        setupTestData();
        Opportunity testOpp = [SELECT Id FROM Opportunity WHERE Name LIKE 'Test Opportunity%' LIMIT 1];
        User communityUser = [SELECT Id FROM User WHERE Email LIKE '<EMAIL>' LIMIT 1];
        
        FeedItem feedItem = new FeedItem(
            Body = 'Test feed item body',
            ParentId = testOpp.Id,
            Type = 'TextPost'
        );
        insert feedItem;

        FeedComment feedComment = new FeedComment(
            FeedItemId = feedItem.Id,
            CommentBody = 'Test feed comment body',
            CreatedById = communityUser.Id
        );
        insert feedComment;
        
        Test.startTest();
        CustomCommentFeederController.deleteFeedCommentItem(feedComment.Id);
        Test.stopTest();
        
        Integer feedCommentCount = [SELECT COUNT() FROM FeedComment WHERE Id = :feedComment.Id];
    }
    
    @isTest
    static void testDeleteFeedCommentItemException() {
        Test.startTest();
        try {
            CustomCommentFeederController.deleteFeedCommentItem(null);
        } catch (AuraHandledException e) {
            System.assert(true, 'Exception caught as expected');
        }
        Test.stopTest();
    }
    
    @isTest
    static void testCreateFeedItemRecWithBase64Image() {
        setupTestData();
        Opportunity testOpp = [SELECT Id FROM Opportunity WHERE Name LIKE 'Test Opportunity%' LIMIT 1];
        
        FeedItem feedItem = new FeedItem(
            Body = 'Test with base64 image <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=">',
            ParentId = testOpp.Id,
            Type = 'TextPost'
        );
        
        Test.startTest();
        FeedItem result = CustomCommentFeederController.createFeedItemRec(feedItem, 'testSid');
        Test.stopTest();
       
    }
    
    @isTest
    static void testCreateFeedItemRecWithRTAImage() {
        setupTestData();
        Opportunity testOpp = [SELECT Id FROM Opportunity WHERE Name LIKE 'Test Opportunity%' LIMIT 1];
        
        FeedItem feedItem = new FeedItem(
            Body = 'Test with RTA image <img src="/mf/servlet/rtaImage?refid=12345">',
            ParentId = testOpp.Id,
            Type = 'TextPost'
        );
        
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());
        
        Test.startTest();
        try {
            FeedItem result = CustomCommentFeederController.createFeedItemRec(feedItem, 'testSid');
        } catch (AuraHandledException e) {
            System.assert(true, 'Exception expected due to mock callout');
        }
        Test.stopTest();
    }
    
    @isTest
    static void testCreateFeedCommentRecWithBase64Image() {
        setupTestData();
        Opportunity testOpp = [SELECT Id FROM Opportunity WHERE Name LIKE 'Test Opportunity%' LIMIT 1];
        User communityUser = [SELECT Id FROM User WHERE Email LIKE '<EMAIL>' LIMIT 1];
        
        FeedItem feedItem = new FeedItem(
            Body = 'Test feed item body',
            ParentId = testOpp.Id,
            Type = 'TextPost'
        );
        insert feedItem;

        FeedComment feedComment = new FeedComment(
            FeedItemId = feedItem.Id,
            CommentBody = 'Test with base64 <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACklEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=">',
            CreatedById = communityUser.Id
        );
        
        Test.startTest();
        FeedComment result = CustomCommentFeederController.createFeedCommentRec(feedComment, 'testSid');
        Test.stopTest();
        
    }
    
    @isTest
    static void testCreateFeedCommentRecException() {
        setupTestData();
        Opportunity testOpp = [SELECT Id FROM Opportunity WHERE Name LIKE 'Test Opportunity%' LIMIT 1];
        
        FeedItem feedItem = new FeedItem(
            Body = 'Test feed item body',
            ParentId = testOpp.Id,
            Type = 'TextPost'
        );
        insert feedItem;

        FeedComment feedComment = new FeedComment(
            FeedItemId = null,
            CommentBody = 'Test comment'
        );
        
        Test.startTest();
        try {
            CustomCommentFeederController.createFeedCommentRec(feedComment, 'testSid');
        } catch (AuraHandledException e) {
            System.assert(true, 'Exception caught as expected');
        }
        Test.stopTest();
    }
    
    @isTest
    static void testAddTopicToFeedItem() {
        setupTestData();
        Opportunity testOpp = [SELECT Id FROM Opportunity WHERE Name LIKE 'Test Opportunity%' LIMIT 1];
        
        FeedItem feedItem = new FeedItem(
            Body = 'Test feed item body',
            ParentId = testOpp.Id,
            Type = 'TextPost'
        );
        insert feedItem;
        
        List<String> topics = new List<String>{
            'TestTopic1' + System.currentTimeMillis(),
            'TestTopic2' + System.currentTimeMillis()
        };
        
        Test.startTest();
        CustomCommentFeederController.addTopicToFeedItem(feedItem.Id, topics);
        Test.stopTest();
        
        List<TopicAssignment> assignments = [SELECT Id, Topic.Name FROM TopicAssignment WHERE EntityId = :feedItem.Id];
    }
    
    @isTest
    static void testExtractContentDocumentIds() {
        setupTestData();
        Opportunity testOpp = [SELECT Id FROM Opportunity WHERE Name LIKE 'Test Opportunity%' LIMIT 1];
        
        String bodyText = 'Text with links sfdc://' + testOpp.Id + ' and sfdc://' + testOpp.Id;
        
        Test.startTest();
        Set<Id> contentIds = CustomCommentFeederController.extractContentDocumentIds(bodyText);
        Test.stopTest();
        
    }
    
    @isTest
    static void testExtractContentDocumentIdsEmpty() {
        String bodyText = 'Text with no links';
        
        Test.startTest();
        Set<Id> contentIds = CustomCommentFeederController.extractContentDocumentIds(bodyText);
        Test.stopTest();
        
    }
    
    @isTest
    static void testExceptionScenarios() {
        Test.startTest();
        
        try {
            CustomCommentFeederController.getOpportunityType(null);
        } catch (AuraHandledException e) {
            System.assert(true, 'Exception caught as expected');
        }
        
        try {
            CustomCommentFeederController.deleteFeedItem(null);
        } catch (AuraHandledException e) {
            System.assert(true, 'Exception caught as expected');
        }
        
        try {
            CustomCommentFeederController.deleteFeedCommentItem(null);
        } catch (AuraHandledException e) {
            System.assert(true, 'Exception caught as expected');
        }
        
        Test.stopTest();
    }
}