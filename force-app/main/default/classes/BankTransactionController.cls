@SuppressWarnings('PMD')
public with sharing class BankTransactionController {

    public class FieldOption implements Comparable { 
        @AuraEnabled public String label { get; set; }
        @AuraEnabled public String value { get; set; }
        @AuraEnabled public String type { get; set; } 

        public FieldOption(String label, String value, String type) {
            this.label = label;
            this.value = value;
            this.type = type;
        }

        public Integer compareTo(Object compareToObject) {
            FieldOption compareToOption = (FieldOption)compareToObject;

            String thisLabel = this.label == null ? '' : this.label;
            String otherLabel = compareToOption.label == null ? '' : compareToOption.label;

            return thisLabel.compareTo(otherLabel);

        }
    }

    @AuraEnabled
    public static List<FieldOption> getTransactionFields() {
        List<FieldOption> options = new List<FieldOption>();
        Map<String, Schema.SObjectField> fieldMap = Schema.SObjectType.Bank_Transaction__c.fields.getMap();

        Set<String> excludedFields = new Set<String>{
            'id', 'createdbyid', 'lastmodifiedbyid', 'bank_account__c', 'btname__c', // Use lowercase for comparison
            'isdeleted', 'createddate', 'lastmodifieddate', 'systemmodstamp'
         };

         Set<String> defaultViewFields = new Set<String>{
            'transaction_date__c', 'description__c', 'debit__c', 'credit__c', 'balance__c', 'category__c', 'name'
         };

        for (String fieldName : fieldMap.keySet()) {
            Schema.DescribeFieldResult fieldDescribe = fieldMap.get(fieldName).getDescribe();
            String fieldNameLower = fieldName.toLowerCase();

            if (fieldDescribe.isAccessible() && !excludedFields.contains(fieldNameLower) &&
                (fieldDescribe.isUpdateable() || defaultViewFields.contains(fieldNameLower)))
             {
                options.add(new FieldOption(
                    fieldDescribe.getLabel(),
                    fieldDescribe.getName(),
                    String.valueOf(fieldDescribe.getType()).toLowerCase()
                ));
            }
        }
        
        options.sort();
        return options;
    }

    @AuraEnabled 
    public static List<Bank_Transaction__c> getTransactions(Id recordId, String objectApiName) {
        if (recordId == null) throw new AuraHandledException('Record ID is missing.');
        if (String.isBlank(objectApiName)) {
            try {
                objectApiName = recordId.getSObjectType().getDescribe().getName();
            } catch (Exception e) {
                throw new AuraHandledException('Object API Name is missing and could not be inferred from Record ID.');
            }
        }

        List<String> queryFields = new List<String>{ 'Id', 'Name', 'Transaction_Date__c', 'Description__c', 'Debit__c', 'Credit__c', 'Balance__c', 'Category__c', 'Sub_Category__c', 'MF_Category__c', 'MF_Category_Manual_Override__c', 'AI_Category__c', 'AI_Reasoning__c', 'Code__c', 'confidence_score__c', 'Last_Fetch_Category__c', 'Synced_Status__c', 'Transaction_Id__c', 'Bank_Account__c', 'Bank_Account__r.Name' };
        queryFields = new List<String>(new Set<String>(queryFields));

        String query = 'SELECT ' + String.join(queryFields, ', ') + ' FROM Bank_Transaction__c';
        String orderBy = ' ORDER BY Transaction_Date__c DESC NULLS LAST, Name DESC LIMIT 50000';

        try {
            if (objectApiName == 'Bank_Account__c') {
                query += ' WHERE Bank_Account__c = :recordId' + orderBy;
                return Database.query(query);
            } else if (objectApiName == 'Account') {
                List<Id> bankAccountIds = new List<Id>();
                for(Bank_Account__c ba : [SELECT Id FROM Bank_Account__c WHERE Account__c = :recordId LIMIT 5000]) {
                    bankAccountIds.add(ba.Id);
                }
                if (bankAccountIds.isEmpty()) return new List<Bank_Transaction__c>();
                query += ' WHERE Bank_Account__c IN :bankAccountIds' + orderBy;
                return Database.query(query);
            } else {
                System.debug('BankTransactionController: Unsupported Object API Name: ' + objectApiName);
                return new List<Bank_Transaction__c>();
            }
        } catch (QueryException qe) {
             System.debug('SOQL Query Error in getTransactions: ' + qe.getMessage() + ' Query: ' + query);
             throw new AuraHandledException('Error querying transactions. Check field access permissions. Details: ' + qe.getMessage());
        } catch (Exception e) {
            System.debug('Error fetching transactions: ' + e.getMessage() + ' Stack: ' + e.getStackTraceString());
            throw new AuraHandledException('An unexpected error occurred while fetching transactions: ' + e.getMessage());
        }
    }

    @AuraEnabled
    public static String saveTransactions(List<Object> transactionsToUpdate) { 
		if (transactionsToUpdate == null || transactionsToUpdate.isEmpty()) {
			return 'No changes received to save.';
		}

		List<Bank_Transaction__c> cleanUpdateList = new List<Bank_Transaction__c>();
		List<String> errors = new List<String>();

		Set<String> nonUpdateableFields = new Set<String>{
			'id', 'name', 'btname__c', 'bank_account__c', 'transaction_id__c',
			'createdbyid', 'lastmodifiedbyid', 'createddate', 'lastmodifieddate', 'systemmodstamp',
			'isdeleted', 'balance__c', 'bank_account__r'
		};

		try {
			for(Object obj : transactionsToUpdate) {
				Map<String, Object> draftData = (Map<String, Object>)JSON.deserializeUntyped(JSON.serialize(obj));

				if (!draftData.containsKey('Id') || draftData.get('Id') == null) {
					errors.add('Cannot update a record missing an ID.');
					continue;
				}

				Bank_Transaction__c cleanTran = new Bank_Transaction__c(Id = (Id)draftData.get('Id'));
				Boolean hasUpdateableField = false;

				for(String fieldName : draftData.keySet()) {
					String fieldNameLower = fieldName.toLowerCase();
					if (nonUpdateableFields.contains(fieldNameLower)) {
						continue;
					}

					cleanTran.put(fieldName, draftData.get(fieldName));
					hasUpdateableField = true;
				}

				if (hasUpdateableField) {
					cleanUpdateList.add(cleanTran);
				}
			}

			if (!errors.isEmpty()) {
				System.debug('Pre-save validation errors: ' + String.join(errors, '; '));
			}

			if (cleanUpdateList.isEmpty()) {
				String msg = 'No valid changes detected to save.';
				if (!errors.isEmpty()) msg += ' Errors found: ' + String.join(errors, '; ');
				return msg;
			}

			Database.SaveResult[] saveResults = Database.update(cleanUpdateList, false);

			Integer successCount = 0;
			Integer errorCount = 0;

			for (Database.SaveResult sr : saveResults) {
				if (sr.isSuccess()) {
					successCount++;
				} else {
					errorCount++;
					for (Database.Error err : sr.getErrors()) {
						String fieldLabels = '';
						if (err.getFields() != null && !err.getFields().isEmpty()) {
							List<String> labels = new List<String>();
							for(String apiName : err.getFields()) {
								try {
									labels.add(Schema.SObjectType.Bank_Transaction__c.fields.getMap().get(apiName).getDescribe().getLabel());
								} catch (Exception e) {
									labels.add(apiName); 
								}
							}
							fieldLabels = ' (Field(s): ' + String.join(labels, ', ') + ')';
						}
						errors.add('Error updating record ID ' + sr.getId() + ': ' + err.getMessage() + fieldLabels);
					}
				}
			}

			String message = successCount + ' record(s) saved successfully.';
			if (errorCount > 0) {
				message += ' ' + errorCount + ' record(s) failed to save.';
			}
			if(!errors.isEmpty()){
				message += ' Details: ' + String.join(errors, '; ');
			}
			return message;

		} catch (Exception e) {
			System.debug('Error saving transactions: ' + e.getMessage() + ' Stack: ' + e.getStackTraceString());
			String errorMsg = 'Error saving transactions: ' + e.getMessage();
			if (!errors.isEmpty()) {
				errorMsg += ' Validation Errors: ' + String.join(errors, '; ');
			}
			throw new AuraHandledException(errorMsg);
		}
    }

}