@RestResource(urlMapping='/feedComment/')
global without sharing class FeedCommentRestApi {
    
	@HttpPost
    global static String createFeedComment(String feedItemId, String commentBody) {
        FeedComment feedComment = new FeedComment();
        feedComment.FeedItemId = feedItemId; 
        feedComment.CommentBody = commentBody;
        Nebula.Logger.info('feedItemId -> ' + feedItemId).addTag('Feed Comment API');
        Nebula.Logger.info('commentBody -> ' + commentBody).addTag('Feed Comment API');

        try {
            insert feedComment;
            return 'FeedComment created successfully with Id: ' + feedComment.Id;
        } catch (Exception ex) {
            Nebula.Logger.error('Error - ' + ex.getMessage() + '--' + ex.getStackTraceString()).addTag('Feed Comment API');
            Nebula.Logger.saveLog();
            return 'Error creating FeedComment: ' + ex.getMessage();
        }
    }
}