@isTest
public class sendEmailOfApplicationFormDetailsTest {

    @isTest
    static void testSendEmailOfApplicationDetailForm() {
        // Create test data
        Account testAccount = new Account(Name = 'Test Account', BillingStreet = '123 Test St', BillingCity = 'Test City', BillingState = 'TS', BillingPostalCode = '12345', Website = 'http://www.test.com', Year_Founded__c = '2000', Description = 'Test Business', Phone = '************', NumberOfEmployees = 10, EIN__c = *********, of_Owners__c = 1);
        insert testAccount;

        Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Phone = '1237890', Email = '<EMAIL>', MailingStreet = '456 Test Ave', MailingCity = 'Test City', MailingState = 'TS', MailingPostalCode = '12345', AccountId = testAccount.Id, Date_of_Birth__c = Date.newInstance(1980, 1, 1), SSN__c = '123-4', Married__c = 'Yes', Ownership__c = 50, Title = 'CEO', Do_you_have_a_life_insurance_policy__c = 'true', Life_Insurance_Policy_Limit__c = '10000');
        insert testContact;

        Contact testContact2 = new Contact(FirstName = 'Jane', LastName = 'Doe', Phone = '987-65', Email = '<EMAIL>', MailingStreet = '789 Test Blvd', MailingCity = 'Test City', MailingState = 'TS', MailingPostalCode = '67890', AccountId = testAccount.Id, Date_of_Birth__c = Date.newInstance(1985, 2, 2), SSN__c = '987-65', Married__c = 'Yes', Ownership__c = 50, Title = 'CFO', Do_you_have_a_life_insurance_policy__c = 'true', Life_Insurance_Policy_Limit__c = '2000');
        insert testContact2;

        Opportunity testOpportunity = new Opportunity(Name = 'Test Opportunity', AccountId = testAccount.Id, StageName = 'Prospecting', CloseDate = Date.today(), Amount = 10000, Loan_Amount_Requested__c = 5000, of_active_contracts_POs__c = '', UCC_Filings__c = 'Yes', Supporting_Docs__c = 'Test Document', Overhead_Debt_Schedule__c = 'Test Debt Schedule', Confirmation_Email__c = '<EMAIL>', Current_Lawsuits__c = 'Yes', Bankruptcy__c = 'Yes', Bad_Debt__c = 'No');
        insert testOpportunity;

        // Create input variables
        sendEmailOfApplicationFormDetails.inputVariables inputVars = new sendEmailOfApplicationFormDetails.inputVariables();
        inputVars.accountId = testAccount.Id;
        inputVars.contactOwnerId = testContact.Id;
        inputVars.contactOwnerId2 = testContact2.Id;
        inputVars.oppId = testOpportunity.Id;
        inputVars.contentDownloadUrl = 'https://platform-customer-2576-dev-ed.scratch.my.salesforce.com/sfc/dist/version/download/?oid=00DE200000D0a7F&ids=068E2000009lfs9&d=%2Fa%2FE2000000gWgD%2FQNHPwyncpjTkhSbJguox6RY2YBFEumARu0gN7Q23TvQ&asPdf=false';

        List<sendEmailOfApplicationFormDetails.inputVariables> inputList = new List<sendEmailOfApplicationFormDetails.inputVariables>();
        inputList.add(inputVars);

        // Test start
        Test.startTest();
        List<sendEmailOfApplicationFormDetails.outputVariables> result = sendEmailOfApplicationFormDetails.sendEmailOfApplicationDetailForm(inputList);
        Test.stopTest();

        // Verify results
        //System.assertEquals(1, result.size());
        //System.assertEquals(true, result[0].success);
        //System.assertEquals('Email sent successfully.', result[0].message);
    }

    @isTest
    static void testSendEmailWithMissingFields() {
        // Create test data
        Account testAccount = new Account(Name = 'Test Account', BillingStreet = '123 Test St', BillingCity = 'Test City', BillingState = 'TS', BillingPostalCode = '12345', Website = 'http://www.test.com', Year_Founded__c = '2000', Description = 'Test Business', Phone = '************', NumberOfEmployees = 10, EIN__c = *********, of_Owners__c = 1);
        insert testAccount;

        Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Phone = '123-456', Email = '<EMAIL>', MailingStreet = '456 Test Ave', MailingCity = 'Test City', MailingState = 'TS', MailingPostalCode = '12345', AccountId = testAccount.Id, Date_of_Birth__c = Date.newInstance(1980, 1, 1), SSN__c = '123-45', Married__c = 'Yes', Ownership__c = 50, Title = 'CEO', Do_you_have_a_life_insurance_policy__c = 'true', Life_Insurance_Policy_Limit__c = '1000');
        insert testContact;

        Opportunity testOpportunity = new Opportunity(Name = 'Test Opportunity', AccountId = testAccount.Id, StageName = 'Prospecting', CloseDate = Date.today(), Amount = 10000, Loan_Amount_Requested__c = 5000, of_active_contracts_POs__c = '', UCC_Filings__c = 'Yes', Supporting_Docs__c = 'Test Document', Overhead_Debt_Schedule__c = 'Test Debt Schedule', Confirmation_Email__c = '<EMAIL>', Current_Lawsuits__c ='Yes', Bankruptcy__c = 'Yes', Bad_Debt__c = 'No');
        insert testOpportunity;

        // Create input variables with missing optional contactOwnerId2
        sendEmailOfApplicationFormDetails.inputVariables inputVars = new sendEmailOfApplicationFormDetails.inputVariables();
        inputVars.accountId = testAccount.Id;
        inputVars.contactOwnerId = UserInfo.getUserId();
        inputVars.oppId = testOpportunity.Id;
        inputVars.contentDownloadUrl = 'https://platform-customer-2576-dev-ed.scratch.my.salesforce.com/sfc/dist/version/download/?oid=00DE200000D0a7F&ids=068E2000009lfs9&d=%2Fa%2FE2000000gWgD%2FQNHPwyncpjTkhSbJguox6RY2YBFEumARu0gN7Q23TvQ&asPdf=false';

        List<sendEmailOfApplicationFormDetails.inputVariables> inputList = new List<sendEmailOfApplicationFormDetails.inputVariables>();
        inputList.add(inputVars);

        // Test start
        Test.startTest();
        List<sendEmailOfApplicationFormDetails.outputVariables> result = sendEmailOfApplicationFormDetails.sendEmailOfApplicationDetailForm(inputList);
        Test.stopTest();

        // Verify results
     //System.assertEquals(1, result.size());
       // System.assertEquals(true, result[0].success);
        //System.assertEquals('Email sent successfully.', result[0].message);
    }
    
    @IsTest
    static void testSendEmailWithExceptions() {
        Account acc = new Account();
        acc.Name = 'Test Account';
        acc.BillingCity = null ; 
        acc.BillingStreet = null;
        acc.BillingState = null;
        acc.BillingPostalCode = null;
        acc.Description = null;
        acc.NumberOfEmployees = null;
        insert acc;

        Opportunity opp = new Opportunity(Name = 'Test Opportunity', AccountId = acc.Id, StageName = 'Prospecting', CloseDate = Date.today());
        insert opp;

        // Test Input with invalid IDs to force exceptions
        sendEmailOfApplicationFormDetails.inputVariables input = new sendEmailOfApplicationFormDetails.inputVariables();
        input.accountId = acc.id;
        //input.contactOwnerId = 'invalidId';
        //input.contactOwnerId2 = 'invalidId';
        input.oppId = opp.id;
        input.contentDownloadUrl = 'https://platform-customer-2576-dev-ed.scratch.my.salesforce.com/sfc/dist/version/download/?oid=00DE200000D0a7F&ids=068E2000009lfs9&d=%2Fa%2FE2000000gWgD%2FQNHPwyncpjTkhSbJguox6RY2YBFEumARu0gN7Q23TvQ&asPdf=false';

        List<sendEmailOfApplicationFormDetails.inputVariables> inputs = new List<sendEmailOfApplicationFormDetails.inputVariables>{ input };

        // Execute method
        Test.startTest();
        List<sendEmailOfApplicationFormDetails.outputVariables> results = sendEmailOfApplicationFormDetails.sendEmailOfApplicationDetailForm(inputs);
        Test.stopTest();

        }

}