@SuppressWarnings('PMD')
public class PDFGeneratorQueueable implements Queueable, Database.AllowsCallouts {
    @TestVisible private static Boolean bypassTest = false;
    
    private String oppId;
    private String emailBody;
    private String contentDownloadUrl;
    private String fileName;
	private String loggerTransactionId;

    public PDFGeneratorQueueable(String oppId, String emailBody, String contentDownloadUrl, String fileName) {
        this.oppId = oppId;
        this.emailBody = emailBody;
        this.contentDownloadUrl = contentDownloadUrl;
        this.fileName = fileName;
    }

	public PDFGeneratorQueueable(String oppId, String emailBody, String contentDownloadUrl, String fileName, String loggerTransactionId) {
        this.oppId = oppId;
        this.emailBody = emailBody;
        this.contentDownloadUrl = contentDownloadUrl;
        this.fileName = fileName;
 		this.loggerTransactionId = loggerTransactionId;
    }

    public void execute(QueueableContext context) {
	if(this.loggerTransactionId!=null){
		Nebula.logger.setParentLogTransactionId(this.loggerTransactionId);
	}

        if (!Test.isRunningTest() || bypassTest) {
            try {
                String encodedUrl = EncodingUtil.urlEncode(emailBody, 'UTF-8');
                String encodedPublicUrl = '';
                if(contentDownloadUrl != null){
                    encodedPublicUrl = EncodingUtil.urlEncode(contentDownloadUrl, 'UTF-8');
                }
                System.debug('page url');
                System.debug('/apex/FormPDFGenerator?Id=' + oppId + '&emailBody=' + encodedUrl + '&contentDownloadUrl=' + encodedPublicUrl);
                
                PageReference formDocument = new PageReference('/apex/FormPDFGenerator?Id=' + oppId + '&emailBody=' + encodedUrl + '&contentDownloadUrl=' + encodedPublicUrl);
                formDocument.setRedirect(false);
                
                Blob documentBlob;
                try {
                    documentBlob = formDocument.getContentAsPDF();
                } catch (Exception e) {
                    System.debug('Error Generating PDF: ' + e.getMessage() + ' ' + e.getStackTraceString());
			Nebula.Logger.error('Error Generating PDF: ' + e.getMessage() + ' ' + e.getStackTraceString(), oppId)
                        .addTag('PDF Generator Queueable');
                    Nebula.Logger.error('Error Generating PDF: ' + e.getMessage() + ' ' + e.getStackTraceString(), oppId)
                        .addTag('PDF Generator Queueable');
                    Nebula.Logger.info('contentDownloadUrl -> ' + contentDownloadUrl)
                        .addTag('PDF Generator Queueable');
                    Nebula.Logger.info('emailBody -> ' + emailBody)
                        .addTag('PDF Generator Queueable');
                    Nebula.Logger.saveLog();
                }
                
                System.debug('Generated PDF Blob: ' + EncodingUtil.base64Encode(documentBlob));
                
                ContentVersion contentVersion = new ContentVersion(
                    Title = fileName,
                    PathOnClient = fileName + '.pdf',
                    VersionData = documentBlob,
                    IsMajorVersion = true,
                    ContentLocation = 'S'
                );
                
                try {
                    Id netId = [SELECT Id FROM Network WHERE Name = 'Mobilization Funding' LIMIT 1].Id;
                    contentVersion.NetworkId = netId;
                } catch (Exception e) {
                    System.debug('Not in a network context: ' + e.getMessage());
                    Nebula.Logger.error('Error Generating PDF: ' + e.getMessage() + ' ' + e.getStackTraceString(), oppId)
                        .addTag('PDF Generator Queueable');
                    Nebula.Logger.error('Error Generating PDF: ' + e.getMessage() + ' ' + e.getStackTraceString(), oppId)
                        .addTag('PDF Generator Queueable');
                    Nebula.Logger.info('contentDownloadUrl -> ' + contentDownloadUrl)
                        .addTag('PDF Generator Queueable');
                    Nebula.Logger.info('emailBody -> ' + emailBody)
                        .addTag('PDF Generator Queueable');
                    Nebula.Logger.saveLog();
                }
                
                insert contentVersion;
                System.debug('Inserted ContentVersion: ' + contentVersion);
                
                contentVersion = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id];
                
                ContentDocumentLink contentDocLink = new ContentDocumentLink(
                    ContentDocumentId = contentVersion.ContentDocumentId,
                    LinkedEntityId = oppId,
                    Visibility = 'AllUsers'
                );
                
                insert contentDocLink;
                System.debug('Inserted ContentDocumentLink: ' + contentDocLink);
            } catch (Exception e) {
                Nebula.Logger.error('Error Generating PDF: ' + e.getMessage() + ' ' + e.getStackTraceString(), oppId)
                        .addTag('PDF Generator Queueable');
                Nebula.Logger.error('Error Generating PDF: ' + e.getMessage() + ' ' + e.getStackTraceString(), oppId)
                    .addTag('PDF Generator Queueable');
                Nebula.Logger.info('contentDownloadUrl -> ' + contentDownloadUrl)
                    .addTag('PDF Generator Queueable');
                Nebula.Logger.info('emailBody -> ' + emailBody)
                    .addTag('PDF Generator Queueable');
                Nebula.Logger.saveLog();
            }
        }
    }
}