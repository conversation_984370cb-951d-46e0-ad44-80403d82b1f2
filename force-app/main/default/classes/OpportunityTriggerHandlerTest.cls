@isTest
public class OpportunityTriggerHandlerTest {
    @testSetup
    static void setupTestData() {
        Account acc = new Account(Name = 'Test Account');
        insert acc;
        
        Contact con = new Contact(FirstName = 'rk', LastName = 'tst32', AccountId = acc.Id);
        insert con;

        Opportunity opp = new Opportunity(
            Name = 'Test Opportunity',
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(30),
            AccountId = acc.Id,
            Status_Update_for_Client__c = 'Loan Committee'
        );
        insert opp;
    }
    
    @isTest
    static void testSendEmailOnStatusChange() {
        Opportunity opp = [SELECT Id, AccountId, Status_Update_for_Client__c FROM Opportunity WHERE Name = 'Test Opportunity' LIMIT 1];
        Contact con = [SELECT Id, Name FROM Contact LIMIT 1];

        User testUser = new User(
            FirstName = 'Test',
            LastName = 'User',
            Email = '<EMAIL>',
            Username = 'testuser' + System.currentTimeMillis() + '@example.com', 
            Alias = 'tuser',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Customer Community User' LIMIT 1].Id,
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            UserRoleId = null,
            IsActive = true,
            ContactId = con.Id
        );
        insert testUser;

        Map<Id, Opportunity> oldOppMap = new Map<Id, Opportunity>{ opp.Id => opp.clone() };

        opp.Status_Update_for_Client__c = 'Approved';
        update opp;

        Opportunity updatedOpp = [SELECT Id, AccountId, Status_Update_for_Client__c FROM Opportunity WHERE Id = :opp.Id LIMIT 1];

        OpportunityTriggerHandler.sendEmailOnStatusChange(new List<Opportunity>{ updatedOpp }, oldOppMap);
    }

    @isTest
    static void testCreateActivityOnInsert() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
        Opportunity opp = new Opportunity(
            Name = 'New Test Opportunity',
            StageName = 'Qualification',
            CloseDate = Date.today().addDays(30),
            AccountId = acc.Id,
            Status_Update_for_Client__c = 'Loan Committee'
        );
        insert opp;

        List<Activity_Logger__c> activities = [SELECT Id FROM Activity_Logger__c WHERE Related_Record__c = :opp.Id];
        System.assertEquals(1, activities.size(), 'Activity should be created for new Opportunity');
    }

    @isTest
    static void testCreateActivityOnUpdate() {
        Opportunity opp = [SELECT Id, Status_Update_for_Client__c FROM Opportunity LIMIT 1];
        opp.Status_Update_for_Client__c = 'Approved';
        update opp;

        List<Activity_Logger__c> activities = [SELECT Id FROM Activity_Logger__c WHERE Related_Record__c = :opp.Id];
        System.assert(activities.size() > 0, 'Activity should be logged for status update');
    }

    @isTest
    static void testGetOpportunityStatusPicklistValues() {
        List<String> picklistValues = OpportunityTriggerHandler.getOpportunityStatusPicklistValues(); 
        System.assert(picklistValues != null && picklistValues.size() > 0, 'Picklist values should be retrieved.');
    }
    
    @isTest
    static void testSendMail() {
        Set<String> testEmails = new Set<String>{ '<EMAIL>' };
    
        String subject = 'Test Email Subject';
        String body = '<p>This is a test email.</p>';
    
        Test.startTest();
        OpportunityTriggerHandler.sendMail(testEmails, subject, body);
        Test.stopTest();
    }

}