@SuppressWarnings('PMD')
@isTest
public class PayeeDeduplicationTest {

    @isTest
    static void testRemoveDuplicatePayees() {
        // Step 1: Insert unique + duplicate Payee Information records
        List<Payee_Information__c> testRecords = new List<Payee_Information__c>{
        new Payee_Information__c(Payee_Name__c = 'Alpha Inc', Payee_Contact_Email__c = '<EMAIL>', Payment_Method__c = 'Check'),
        new Payee_Information__c(Payee_Name__c = 'Beta LLC', Payee_Contact_Email__c = '<EMAIL>', Payment_Method__c = 'Wire'),
        new Payee_Information__c(Payee_Name__c = 'alpha inc', Payee_Contact_Email__c = '<EMAIL>', Payment_Method__c = 'ACH'),
        new Payee_Information__c(Payee_Name__c = 'Beta LLC ', Payee_Contact_Email__c = '<EMAIL>', Payment_Method__c = 'Check'),
        new Payee_Information__c(Payee_Name__c = 'Gamma Ltd', Payee_Contact_Email__c = '<EMAIL>', Payment_Method__c = 'Wire'),
        new Payee_Information__c(Payee_Name__c = 'ALPHA INC', Payee_Contact_Email__c = '<EMAIL>', Payment_Method__c = 'ACH')
    };

        insert testRecords;

        Test.startTest();
        PayeeDeduplication.removeDuplicatePayees();
        Test.stopTest();

        // Step 2: Query remaining records
        List<Payee_Information__c> remaining = [
            SELECT Payee_Name__c FROM Payee_Information__c
        ];

        // Step 3: Assert that only unique payees remain
        Set<String> normalizedNames = new Set<String>();
        for (Payee_Information__c rec : remaining) {
            String normName = rec.Payee_Name__c.trim().toLowerCase();
            System.assert(!normalizedNames.contains(normName), 'Duplicate payee still exists: ' + normName);
            normalizedNames.add(normName);
        }

        System.assertEquals(3, remaining.size(), 'Only 3 unique records should remain');
    }

    @isTest
    static void testNoDuplicatesPresent() {
        // Insert non-duplicate records
        List<Payee_Information__c> records = new List<Payee_Information__c>{
            new Payee_Information__c(Payee_Name__c = 'One Co',   Payee_Contact_Email__c = '<EMAIL>',   Payment_Method__c = 'ACH'),
            new Payee_Information__c(Payee_Name__c = 'Two Co',   Payee_Contact_Email__c = '<EMAIL>',   Payment_Method__c = 'Wire'),
            new Payee_Information__c(Payee_Name__c = 'Three Co', Payee_Contact_Email__c = '<EMAIL>', Payment_Method__c = 'Check')
        };
        insert records;

        Test.startTest();
        PayeeDeduplication.removeDuplicatePayees();
        Test.stopTest();

        List<Payee_Information__c> allRecords = [
            SELECT Id FROM Payee_Information__c
        ];

        System.assertEquals(3, allRecords.size(), 'All original records should remain');
    }

    
}