@isTest
public class SubmitApplicationServiceTest {
    
    @testSetup
    static void testSetup(){
        Account acc = new Account(name = 'kg parry');
        insert acc;
        Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Email = '<EMAIL>', AccountId = acc.Id);
        insert testContact;

        User testUser = new User(
            Username = '<EMAIL>',
            Email = '<EMAIL>',
            LastName = 'User',
            Alias = 'testuser',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Customer Community Login User' LIMIT 1].Id,
            LanguageLocaleKey = 'en_US',
            ContactId = testContact.Id
        );
        insert testUser;
       
        
       
    }
    
    @isTest
    static void testUpdateAccountAndCreateOpportunity_Success() {
        
		
        
        User testUser = [SELECT Id, name, ContactId, Profile.Name FROM User Where Email = '<EMAIL>' Limit 1];
        
		
        String jsonInput = '{ "BusinessInformation": { "BusinessAddress": { "CITY": "New York", "ZIP_CODE": "10001", "STATE": "NY", "STREET_ADDRESS": "123 Main St" }, "Type_of_Work": "Construction", "Federal_Tax_ID_Number": "*********", "Number_of_Employees": "50", "businessPhone": "************", "Website": "www.example.com", "Year_Business_Was_Founded": "2000", "Number_of_Owners_Above_10": "2", "Loan_Amount_Requested": "50000", "Business_Name": "Test Business" }, "VerifyAndSubmit": { "Signature": "John Doe", "Email_for_Confirmation": "<EMAIL>", "Date1": "2023-01-01" }, "AdditionalInformation": { "Are_you_delinquent_or_in_default_of_any_debt_or_other_loans_including_Federal_or": "No", "Have_you_or_any_of_the_majority_owners_ever_filed_for_bankruptcy": "No", "Are_you_or_any_of_the_majority_owners_party_to_any_current_lawsuits": "No", "Are_there_any_UCC_Filings_against_the_company_or_any_of_its_majority_owners": "No" }, "OwnerInformation": { "Name": { "First": "John", "Last": "Doe" }, "Email": "<EMAIL>", "cellPhone": "************", "HomeAddress": { "street": "123 Main St", "state": "NY", "postalCode": "10001", "country": "USA", "city": "New York" }, "Title": "Owner", "Date_Of_Birth": "1980-01-01", "Social_Security_Number": "***********", "Married": "Yes", "Percent_Ownership": "100", "Do_you_have_a_life_insurance_policy": "Yes", "If_yes_what_is_the_policy_limit": "500000" }, "OwnerInformation2": { "Name": { "First2": "John", "Last2": "Doe" }, "Email2": "<EMAIL>", "cellPhone2": "************", "HomeAddress2": { "street2": "123 Main St", "state2": "NY", "postalCode2": "10001", "country2": "USA", "city2": "New York" }, "Title2": "Owner", "Date_Of_Birth2": "1980-01-01", "Social_Security_Number2": "***********", "Married2": "Yes", "Percent_Ownership2": "100", "Do_you_have_a_life_insurance_policy2": "Yes", "If_yes_what_is_the_policy_limit2": "500000" } } }';

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/SubmitApplicationForm/';
        RestResponse res = new RestResponse();
        req.requestBody = Blob.valueOf(jsonInput);
        req.httpMethod = 'POST';
        RestContext.request = req;
        //RestContext.response = res;
        
        
        
        Test.startTest();
        
        PermissionSet mfPermissionSet = [SELECT Id FROM PermissionSet WHERE Name = 'Mobilization_Funding_Experience_Member' LIMIT 1];
        
        PermissionSetAssignment psa = new PermissionSetAssignment(
            AssigneeId = testUser.Id,
            PermissionSetId = mfPermissionSet.Id
        );
        insert psa;   
        
        System.runAs(testUser){
            SubmitApplicationService.ResponseWrapper response = SubmitApplicationService.updateAccountAndCreateOpportunity();
        }
        Test.stopTest();
    
            // Verify the response
            /*System.assertEquals('success', response.status, 'The status should be success.');
            System.assertNotEquals(null, response.accountId, 'Account ID should not be null.');
            System.assertNotEquals(null, response.contact1Id, 'Contact1 ID should not be null.');
            System.assertNotEquals(null, response.opportunityId, 'Opportunity ID should not be null.');*/
        
    }

    
}