@SuppressWarnings('PMD')
public without sharing class DropboxUploadFuture {
    
    @future(callout=true)
    public static void uploadToDropbox(String parentId, String folderPath) {
        List<ContentVersion> cvsToUpdate = new List<ContentVersion>();
        List<ContentVersion> filteredContentVersions = new List<ContentVersion>();
        List<Custom_Exception__c> customExceptions = new List<Custom_Exception__c>();
        Set<Id> contentDocumentIds = new Set<Id>();
        String parentObjectType = parentId.startsWith('006') ? 'Opportunity' : 
                                 parentId.startsWith('a09') ? 'Project__c' : 
                                 'Disbursement_Request__c';
        
        try {
            // Get ContentDocumentLinks
            List<ContentDocumentLink> cdLinks = [
                SELECT ContentDocumentId, LinkedEntityId 
                FROM ContentDocumentLink 
                WHERE LinkedEntityId = :parentId
            ];
            
            // Collect ContentDocumentIds
            for (ContentDocumentLink cdl : cdLinks) {
                contentDocumentIds.add(cdl.ContentDocumentId);
            }
            
            if (!contentDocumentIds.isEmpty()) {
                // Query ContentVersions
                List<ContentVersion> contentVersions = [
                    SELECT Id, Title, ContentDocumentId, FileExtension 
                    FROM ContentVersion 
                    WHERE ContentDocumentId IN :contentDocumentIds 
                    AND IsLatest = true
                    AND ContentDocument.ContentAssetId = null
                    AND Dropbox_Uploaded_Date__c = null
                    AND (Dropbox_Sync_Status__c = null OR Dropbox_Sync_Status__c = 'Not Started') LIMIT 100
                ];
                
                // Filter out excluded records
                for (ContentVersion cv : contentVersions) {
                    if (!shouldExclude(cv)) {
                        filteredContentVersions.add(cv);
                    }
                }
                
                Map<Id,String> cvAndCDLinkMap = getDownloadLinks(filteredContentVersions);
                
                // Process each ContentVersion
                for (ContentVersion cv : filteredContentVersions) {
                    String fileUrl = cvAndCDLinkMap.get(cv.Id);
                    String fullFileName = cv.Title + '.' + cv.FileExtension;
                    fullFileName = fullFileName.replace('/', '-');
                    
                    if (fileUrl != null) {
                        Map<String,Object> returnMap = saveFileToDropbox(fullFileName, fileUrl, cv.Id, parentId, folderPath, customExceptions);
                        if ((Boolean)returnMap.get('success') == true) {
                            cv.Dropbox_Async_Job_Id__c = (String) returnMap.get('jobId');
                            cv.Dropbox_Sync_Status__c = 'Processing - Waiting Dropbox Confirmation';
                            cvsToUpdate.add(cv);
                        } else {
                            cv.Dropbox_Sync_Status__c = 'Failed';
                            cvsToUpdate.add(cv);
                        }
                    }
                }
                
                // Update records
                if (!cvsToUpdate.isEmpty()) {
                    update cvsToUpdate;
                }
                if (!customExceptions.isEmpty()) {
                    insert customExceptions;
                }
            }
        } catch(Exception e) {
            // Handle exceptions and ensure updates are committed
            if (!cvsToUpdate.isEmpty()) {
                update cvsToUpdate;
            }
            if (!customExceptions.isEmpty()) {
                insert customExceptions;
            }
            // Additional error logging if needed
            /*Custom_Exception__c exceptionLog = new Custom_Exception__c(
                Name = 'DropboxUploadFuture Error',
                Exception_Message__c = e.getMessage(),
                Exception_Type__c = e.getTypeName(),
                Line_Number__c = e.getLineNumber(),
                Stack_Trace__c = e.getStackTraceString(),
                Related_Record_ID__c = parentId
            );
            insert exceptionLog;*/
        }
    }
    
    private static Boolean shouldExclude(ContentVersion cv) {
        return cv.FileExtension == 'snote';
    }
    
    private static Map<Id,String> getDownloadLinks(List<ContentVersion> contentVersions) {
        Map<Id,String> cvAndCDLinkMap = new Map<Id,String>();
        
        for (ContentDistribution cd : [
            SELECT ContentVersionId, ContentDownloadUrl 
            FROM ContentDistribution 
            WHERE ContentVersionId IN :contentVersions
        ]) {
            cvAndCDLinkMap.put(cd.ContentVersionId, cd.ContentDownloadUrl);
        }
        
        return cvAndCDLinkMap;
    }
    
    private static Map<String,Object> saveFileToDropbox(
        String fileName, 
        String fileUrl, 
        Id contentVersionId, 
        String parentId, 
        String folderPath,
        List<Custom_Exception__c> customExceptions
    ) {
        Map<String,Object> returnMap = new Map<String,Object>();
        
        returnMap = DropboxController.uploadFileToDropboxByUrl(folderPath, fileName, fileUrl, contentVersionId, parentId);
        
        HttpResponse res = (HttpResponse) returnMap.get('res');
        Custom_Exception__c ceObj = (Custom_Exception__c) returnMap.get('log');
        customExceptions.add(ceObj);
        
        if (res.getStatusCode() == 200) {
            Map<String, Object> resMap = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
            if (resMap.containsKey('async_job_id')) {
                returnMap.put('jobId', (String) resMap.get('async_job_id'));
            }
            returnMap.put('success', true);            
        } else {
            returnMap.put('success', false);
        }
        
        return returnMap;
    }
}