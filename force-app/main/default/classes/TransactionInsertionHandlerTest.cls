@SuppressWarnings('PMD')
@isTest
public class TransactionInsertionHandlerTest {

    @isTest
    static void testSingleProjectMultipleTransactions() {
        // Create one project
        Project__c proj = new Project__c(Name = 'Test Project 1');
        insert proj;

        // Create multiple transactions for this project
        List<Transaction__c> txns = new List<Transaction__c>();
        for (Integer i = 0; i < 5; i++) {
            txns.add(new Transaction__c(
                Project__c = proj.Id,
                Run_Transactions_Flow__c = false
                // No Transaction_Date__c as it is not writable
            ));
        }

        Test.startTest();
        insert txns; // This will fire your before insert logic if wired to trigger
        Test.stopTest();

        // Reload transactions and count flagged ones
        List<Transaction__c> insertedTxns = [
            SELECT Id, Run_Transactions_Flow__c
            FROM Transaction__c
            WHERE Project__c = :proj.Id
        ];

        Integer flaggedCount = 0;
        for (Transaction__c t : insertedTxns) {
            if (t.Run_Transactions_Flow__c == true) flaggedCount++;
        }

        System.assertEquals(1, flaggedCount, 'Exactly one transaction should have Run_Transactions_Flow__c = true');
    }

    @isTest
    static void testMultipleProjectsBulkTransactions() {
        // Create multiple projects
        List<Project__c> projects = new List<Project__c>();
        for (Integer i = 0; i < 3; i++) {
            projects.add(new Project__c(Name = 'Project ' + i));
        }
        insert projects;

        // Create many transactions across projects
        List<Transaction__c> txns = new List<Transaction__c>();
        for (Project__c p : projects) {
            for (Integer j = 0; j < 4; j++) {
                txns.add(new Transaction__c(
                    Project__c = p.Id,
                    Run_Transactions_Flow__c = false
                ));
            }
        }

        Test.startTest();
        insert txns;
        Test.stopTest();

        // For each project, verify only one flagged transaction exists
        for (Project__c p : projects) {
            List<Transaction__c> projectTxns = [
                SELECT Id, Run_Transactions_Flow__c
                FROM Transaction__c
                WHERE Project__c = :p.Id
            ];
            Integer flaggedCount = 0;
            for (Transaction__c t : projectTxns) {
                if (t.Run_Transactions_Flow__c == true) flaggedCount++;
            }
            System.assertEquals(1, flaggedCount, 'Project ' + p.Name + ' should have exactly one flagged transaction');
        }
    }

    @isTest
    static void testTransactionsWithoutProject() {
        // Transactions with no project should be ignored
        List<Transaction__c> txns = new List<Transaction__c>();
        txns.add(new Transaction__c(Run_Transactions_Flow__c = false));
        txns.add(new Transaction__c(Run_Transactions_Flow__c = false));

        Test.startTest();
        insert txns;
        Test.stopTest();

        // Both should have Run_Transactions_Flow__c = false
        List<Transaction__c> insertedTxns = [
            SELECT Id, Run_Transactions_Flow__c
            FROM Transaction__c
            WHERE Id IN :txns
        ];
        for (Transaction__c t : insertedTxns) {
            System.assertEquals(false, t.Run_Transactions_Flow__c, 'Transactions without project should not be flagged');
        }
    }
}