@isTest
public class OpportunityApiTest {

    @testSetup
    static void setupTestData() {
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        
        Opportunity opp1 = new Opportunity(Name = 'Test Opportunity 1', StageName = 'Prospecting', CloseDate = System.today().addDays(10), AccountId = testAccount.Id, Amount = 10000);
        Opportunity opp2 = new Opportunity(Name = 'Test Opportunity 2', StageName = 'Closed Won', CloseDate = System.today().addDays(5), AccountId = testAccount.Id, Amount = 20000);
        insert new List<Opportunity> { opp1, opp2 };
    }

    @isTest
    static void testGetOpportunitiesWithAccountId() {
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/opportunity/' + testAccount.Id;
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = new RestResponse();

        Test.startTest();
        OpportunityAPI.getOpportunities();
        Test.stopTest();
    }

    @isTest
    static void testGetOpportunitiesWithOpportunityId() {
        Opportunity testOpportunity = [SELECT Id FROM Opportunity WHERE Name = 'Test Opportunity 1' LIMIT 1];

        RestRequest req = new RestRequest();
        req.requestURI = '/services/apexrest/opportunity/' + testOpportunity.Id;
        req.httpMethod = 'GET';
        RestContext.request = req;
        RestContext.response = new RestResponse();

        Test.startTest();
        OpportunityAPI.getOpportunities();
        Test.stopTest();

        // Validate the response
        String jsonResponse = RestContext.response.responseBody.toString();
        //System.assertNotEquals(null, jsonResponse, 'Response should not be null.');
        //System.assert(RestContext.response.statusCode == 200, 'Response status should be 200.');

        // Deserialize response to check contents
        //OpportunityAPI.OpportunityResponseWrapper responseWrapper = (OpportunityAPI.OpportunityResponseWrapper) JSON.deserialize(jsonResponse, OpportunityAPI.OpportunityResponseWrapper.class);
        //System.assertEquals(1, responseWrapper.totalSize, 'There should be 1 opportunity.');
        //System.assertEquals(true, responseWrapper.done, 'Response "done" should be true.');
    }
}