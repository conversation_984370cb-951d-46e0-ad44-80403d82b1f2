public without sharing class FormUtility {

     public static void createAndLinkPdfDocument(String emailBody, Id linkedEntityId, String title, String pathOnClient) {
        Blob pdfBlob = Blob.toPdf(emailBody);
        //String netId = Network.getNetworkId();
         
        ContentVersion contentVersion = new ContentVersion(
            Title = title, //'Application Form Details',
            PathOnClient = pathOnClient, //'ApplicationFormDetails.pdf',
            VersionData = pdfBlob,
            IsMajorVersion = true,
            ContentLocation = 'S'
            //NetworkId = netId
        );
         
        if (Test.isRunningTest()) { 
            try {
			Id netId = [SELECT Id FROM Network LIMIT 1].Id;
                contentVersion.NetworkId = netId;
            } catch (Exception e) {
                System.debug('Not in a network context: ' + e.getMessage());
            }
        }
        insert contentVersion;

        contentVersion = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id];
        
        ContentDocumentLink contentDocLink = new ContentDocumentLink(
            ContentDocumentId = contentVersion.ContentDocumentId,
            LinkedEntityId = linkedEntityId, 
            Visibility = 'AllUsers'
        );
        insert as system contentDocLink;
    }

    
}