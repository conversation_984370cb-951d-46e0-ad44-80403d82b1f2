@SuppressWarnings('PMD')
@isTest
public class DisbursementValidationHandlerTest {

    // Method to set up test data
    private static void setupTestData() {
        // Create a Project first (since it's required for the master-detail relationship)
        Project__c project = new Project__c(Name = 'Test Project');
        insert project;

        // Create a Disbursement Request and set the Status__c field to 'New'
        Disbursement_Request__c request1 = new Disbursement_Request__c(
            Project_lookup__c = project.Id,
            Status__c = 'New' // Set Status__c to 'New'
        );
        insert request1;

        Disbursement_Request__c request2 = new Disbursement_Request__c(
            Project_lookup__c = project.Id,
            Status__c = 'New' // Set Status__c to 'New'
        );
        insert request2;

        // Create Disbursements and link them to the Disbursement Requests and Projects
        Disbursement__c disbursement1 = new Disbursement__c(
            
            Disbursement_Request__c = request1.Id,
            Project__c = project.Id // Link to the created Project
        );
        insert disbursement1;

        // Create another Disbursement linked to request1 (to trigger the validation error)
      

        // Create a valid Disbursement linked to request2
        Disbursement__c disbursement3 = new Disbursement__c(
            Disbursement_Request__c = request2.Id,
            Project__c = project.Id // Link to the created Project
        );
        insert disbursement3;
    }

    // Test to ensure duplicate disbursements are prevented
    @isTest
    static void testPreventDuplicateDisbursements() {
        // Setup test data
        setupTestData();

        // Query the existing Disbursements to check how many are present
        List<Disbursement__c> newDisbursements = [
            SELECT Id, Disbursement_Request__c, Project__c FROM Disbursement__c
        ];

        // Collect request Ids of the new disbursements
        Set<Id> requestIds = new Set<Id>();
        for (Disbursement__c disbursement : newDisbursements) {
            requestIds.add(disbursement.Disbursement_Request__c);
        }

        // Call the method to validate the disbursements
        Test.startTest();
        DisbursementValidationHandler.preventDuplicateDisbursements(requestIds, newDisbursements);
        Test.stopTest();

        // Verify that the validation failed for the second Disbursement linked to the same request
        Boolean hasError = false;
        for (Disbursement__c disbursement : newDisbursements) {
            if (disbursement.hasErrors()) {
                hasError = true;
            }
        }

        // Assert that errors were thrown for the duplicate disbursements
        System.assertEquals(true, hasError, 'Validation error should have been triggered for duplicate disbursements.');
    }

    // Test to check that no error occurs when there is only one disbursement for a request
    @isTest
    static void testNoDuplicateDisbursement() {
        // Create Project first
        Project__c project = new Project__c(Name = 'Unique Project');
        insert project;

        // Create one Disbursement Request and set the Status__c field to 'New'
        Disbursement_Request__c request = new Disbursement_Request__c(
            Project_lookup__c = project.Id,
            Status__c = 'New' // Set Status__c to 'New'
        );
        insert request;

        // Create a Disbursement and link it to the Project
        Disbursement__c disbursement = new Disbursement__c(
            Disbursement_Request__c = request.Id,
            Project__c = project.Id // Link to the created Project
        );
        insert disbursement;

        // Validate the disbursement
        Test.startTest();
        DisbursementValidationHandler.preventDuplicateDisbursements(new Set<Id>{request.Id}, new List<Disbursement__c>{disbursement});
        Test.stopTest();

        // Assert that no error occurs as the disbursement is the only one for this request
        //System.assertEquals(0, disbursement.getErrors().size(), 'No error should have been triggered for unique disbursement.');
    }

    // Test logging behavior (ensure Nebula Logger is called correctly)
    @isTest
    static void testLogging() {
        // Mock the Nebula Logger (You can mock the logging behavior here if necessary)
        Test.startTest();

        // Simulate a Disbursement Request with duplicate Disbursements
        

        // Create Project first
        Project__c project = new Project__c(Name = 'Logging Project');
        insert project;
        Disbursement_Request__c request = new Disbursement_Request__c(
           Project_lookup__c = project.Id,
            Status__c = 'New' // Set Status__c to 'New'
        );
        insert request;

        Disbursement__c disbursement1 = new Disbursement__c(
            Disbursement_Request__c = request.Id,
            Project__c = project.Id // Link to the created Project
        );
        insert disbursement1;

       

        // Query the Disbursements to pass into the handler
        List<Disbursement__c> newDisbursements = [
            SELECT Id, Disbursement_Request__c FROM Disbursement__c WHERE Disbursement_Request__c = :request.Id
        ];

        // Call the method
        DisbursementValidationHandler.preventDuplicateDisbursements(new Set<Id>{request.Id}, newDisbursements);

        Test.stopTest();

        // Assertions regarding logging behavior can be added if necessary
        // For example, verifying if logs are saved or checking specific logger methods, etc.
    }
}