@SuppressWarnings('PMD')
@IsTest
private class UserPermissionCheckerTest {

    // Normal scenario: permission check returns true/false
    @IsTest
    static void testCheckCurrentUserPermission_success() {
        Boolean hasPerm = UserPermissionChecker.checkCurrentUserPermission('Enable_Verbose_Debug_Logging');
        System.assertNotEquals(null, hasPerm);
    }

    // Exception scenario: pass null or invalid input to force exception in FeatureManagement.checkPermission
    @IsTest
    static void testCheckCurrentUserPermission_exception() {
        Test.startTest();
        // Passing null should cause FeatureManagement.checkPermission to throw exception
        Boolean result = UserPermissionChecker.checkCurrentUserPermission(null);
        System.assertEquals(false, result, 'Should return false when exception is caught');
        Test.stopTest();
    }
}