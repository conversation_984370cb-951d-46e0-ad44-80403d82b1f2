public class PopulateCounterOnDRTriggerHandler {
    
    public static void populateCounter(List<Disbursement_Request__c> newDisbursements) {        
        Map<Id, Integer> projectDisbursementCountMap = new Map<Id, Integer>();
        
        Set<Id> projectIds = new Set<Id>();
        
        for (Disbursement_Request__c dr : newDisbursements) {
            if (dr.Project_lookup__c != null) {
                projectIds.add(dr.Project_lookup__c);
            }
        }
        
        if (!projectIds.isEmpty()) {
            List<AggregateResult> disbursementCount = [
                SELECT Project_lookup__c, COUNT(Id) total
                FROM Disbursement_Request__c
                WHERE Project_lookup__c IN :projectIds
                GROUP BY Project_lookup__c
            ];
            
            for (AggregateResult result : disbursementCount) {
                projectDisbursementCountMap.put((Id) result.get('Project_lookup__c'), (Integer) result.get('total'));
            }
        }
        
        for (Disbursement_Request__c dr : newDisbursements) {
            if (dr.Project_lookup__c != null) {
                Integer currentCount = projectDisbursementCountMap.containsKey(dr.Project_lookup__c) ? projectDisbursementCountMap.get(dr.Project_lookup__c) : 0;
                dr.Disbursement__c = currentCount + 1;
                
                /*String datePrefix = DateTime.now().format('MMddyy');
                String paddedCounter = String.valueOf(dr.Disbursement__c);
                while (paddedCounter.length() < 4) {
                    paddedCounter = '0' + paddedCounter;
                }
                
                dr.Disbursement_Request_Name__c = datePrefix + '-' + paddedCounter;*/
            }
        }
    }
}