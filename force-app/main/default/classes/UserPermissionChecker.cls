/**
 * @description This utility class provides a method to check if the current running user
 * has a specific Custom Permission. It is designed to be called from Lightning Web Components
 * or other Apex classes to enforce permission-based access to features or data.
 * <AUTHOR>
 * @date 12 May 2025
*/
@SuppressWarnings('PMD')
public with sharing class UserPermissionChecker {
    @AuraEnabled(cacheable=true)
    public static Boolean checkCurrentUserPermission(String permissionName) {

        Boolean hasPerm = false;
        try {
            hasPerm = FeatureManagement.checkPermission(permissionName);
            System.debug('Permission Check for ' + permissionName + ' for user ' + UserInfo.getUserId() + ': ' + hasPerm);
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR,'Error checking permission: ' + permissionName + '. Error: ' + e.getMessage());
            Nebula.Logger.error('Error checking Custom Permission: ' + permissionName, e); 
        }
        return hasPerm;
    }
}