@isTest
private class SubmitDisbursementRequestServiceTest {
    
    @testSetup
    static void setupTestData() {
        // 1) Core records
        Account acc = new Account(Name = 'kg parry');
        insert acc;
        
        Contact c = new Contact(
            FirstName = 'John',
            LastName  = 'Doe',
            Email     = '<EMAIL>',
            AccountId = acc.Id
        );
        insert c;
        
        Opportunity opp = new Opportunity(
            Name       = 'test opp',
            StageName  = 'Prospecting',
            CloseDate  = Date.today().addDays(30),
            AccountId  = acc.Id
        );
        insert opp;
        
        Project__c proj = new Project__c(
            Name            = 'test proj',
            Account_Name__c = acc.Id
        );
        insert proj;
        
        // 2) A user to runAs
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Customer Community Login User' LIMIT 1];
        User u = new User(
            Username         = 'tstusr_' + Math.random() + '@example.com',
            Email            = 'testuser_' + Math.random() + '@example.com',
            LastName         = 'User',
            Alias            = 'tuser',
            TimeZoneSidKey   = 'America/New_York',
            LocaleSidKey     = 'en_US',
            EmailEncodingKey = 'UTF-8',
            ProfileId        = p.Id,
            LanguageLocaleKey= 'en_US',
            ContactId        = c.Id
        );
        insert u;
    }
    
    @isTest
    static void testNewPayeeSuccess() {
        Project__c proj = [SELECT Id FROM Project__c WHERE Name = 'test proj' LIMIT 1];
        
        // build JSON WITHOUT a payeeId → should hit "insert new Payee" branch
        String json = '{'
            + '"disbursementRequest":{'
                + '"loanNumberLookup":"' + [SELECT Id FROM Opportunity WHERE Name='test opp' LIMIT 1].Id + '",'
                + '"expenseType":"Material",'
                + '"projectLookup":"' + proj.Id + '",'
                + '"payeeContactName":{ "firstName":"John","lastName":"Doe" },'
                + '"payeeContactEmail":"<EMAIL>",'
                + '"Disbursement_Number": 123,'
                + '"paymentMethod":"Wire",'
                + '"payeeAddress":{},'
                + '"payeeName":"John Doe",'
                + '"payeePhone":"**********",'
                + '"Mail_Check_To":"John Doe",'
                + '"Account_Name":"Doe Enterprises",'
                + '"Bank_Routing_Number":"*********",'
                + '"Bank_Account_Number":"*********",'
                + '"Bank_Name":"Bank of Example"'
            + '},'
            + '"itemDescriptions":['
              + '{'
                + '"Item":"Item 1",'
                + '"Description_Work":"Desc1",'
                + '"invoiceDate":"2024-09-01",'
                + '"invoiceAmount":"$1000.00",'
                + '"invoice":"INV001",'
                + '"invoiceDueDate":"2024-09-15"'
              + '},'
              + '{'
                + '"Item":"Item 2",'
                + '"Description_Work":"Desc2",'
                + '"invoiceDate":"2024-09-05",'
                + '"invoiceAmount":"$2000.00",'
                + '"invoice":"INV002",'
                + '"invoiceDueDate":"2024-09-20"'
              + '}'
            + '],'
            + '"verifyAndSubmit":{'
                + '"signature":"John Doe",'
                + '"requesterEmail":"<EMAIL>",'
                + '"PROJECTOWNER_Lookup":"PO123456",'
                + '"additionalCommentsSpecialInstructions":"Please process urgently."'
            + '}'
        + '}';
        
        RestRequest  req = new RestRequest();
        req.requestBody  = Blob.valueOf(json);
        req.httpMethod   = 'POST';
        RestContext.request = req;
        
        // run as a real user
        User u = [SELECT Id FROM User WHERE Username LIKE 'tstusr_%' LIMIT 1];
        Test.startTest();
          System.runAs(u) {
            SubmitDisbursementRequestService.ResponseWrapper resp =
              SubmitDisbursementRequestService.handleDisbursementRequest();
            // verify success
            System.assertEquals('success', resp.status);
            System.assertNotEquals(null, resp.disbursementRequestId);
            System.assertEquals(2, resp.requestedItemIds.size());
          }
        Test.stopTest();
        
        // a new Payee_Information__c should have been inserted
        System.assertEquals(
          1, 
          [SELECT COUNT() FROM Payee_Information__c]
        );
    }
    
    @isTest
    static void testExistingPayeeSuccess() {
        // insert a Payee master record
        Account acc = [SELECT Id FROM Account WHERE Name = 'kg parry' LIMIT 1];
        Project__c proj = [SELECT Id FROM Project__c WHERE Name = 'test proj' LIMIT 1];
        Payee_Information__c master = new Payee_Information__c(
            Account__c              = acc.Id,
            Account_Name__c         = 'Master Payee Inc',
            Bank_Account_Number__c  = '444',
            Bank_Name__c            = 'Test Bank',
            Bank_Routing_Number__c  = '555',
            Street__c               = '1 Test St',
            City__c                 = 'Metro City',
            State_Province__c       = 'TS',
            Country__c              = 'Wonderland',
            Zip_Postal_Code__c      = '99999',
            Payee_Contact_Email__c  = '<EMAIL>',
            Payee_Contact_Name__c   = 'Foo Bar',
            Payee_Name__c           = 'FooBar Co',
            Payee_Phone__c          = '*********0',
            Payment_Method__c       = 'Check',
            Project__c              = proj.Id
        );
        insert master;
        
        // JSON that includes payeeId → should hit "reuse existing" branch
        String json = '{'
            + '"disbursementRequest":{'
                + '"payeeId":"' + master.Id + '",'
                + '"loanNumberLookup":"' + [SELECT Id FROM Opportunity WHERE Name='test opp' LIMIT 1].Id + '",'
                + '"expenseType":"Other",'
                + '"projectLookup":"' + proj.Id + '",'
                + '"payeeContactName":{ "firstName":"Foo","lastName":"Bar" },'
                + '"payeeContactEmail":"<EMAIL>",'
                + '"Disbursement_Number": 789,'
                + '"paymentMethod":"Check",'
                + '"payeeAddress":{},'
                + '"payeeName":"FooBar Co",'
                + '"payeePhone":"*********0",'
                + '"Mail_Check_To":"FooBar Co",'
                + '"Account_Name":"Master Payee Inc",'
                + '"Bank_Routing_Number":"555",'
                + '"Bank_Account_NUMBER":"444",'
                + '"Bank_Name":"Test Bank"'
            + '},'
            + '"itemDescriptions":['
              + '{'
                + '"Item":"X",'
                + '"Description_Work":"W",'
                + '"invoiceDate":"2024-01-01",'
                + '"invoiceAmount":"$10.00",'
                + '"invoice":"I1",'
                + '"invoiceDueDate":"2024-01-15"'
              + '}'
            + '],'
            + '"verifyAndSubmit":{'
                + '"signature":"Sig",'
                + '"requesterEmail":"<EMAIL>",'
                + '"PROJECTOWNER_Lookup":"PO999"'
            + '}'
        + '}';
        
        RestRequest req = new RestRequest();
        req.requestBody = Blob.valueOf(json);
        req.httpMethod  = 'POST';
        RestContext.request = req;
        
        Test.startTest();
          SubmitDisbursementRequestService.ResponseWrapper resp =
            SubmitDisbursementRequestService.handleDisbursementRequest();
        Test.stopTest();
        
    }
    
    @isTest
    static void testErrorRollsBack() {
        // Minimal wrapper that causes Id.valueOf() to blow up
        String json = '{"disbursementRequest":{"payeeId":"badId"}}';
        
        RestRequest  req = new RestRequest();
        req.requestBody  = Blob.valueOf(json);
        req.httpMethod   = 'POST';
        RestContext.request = req;
        
        Test.startTest();
          SubmitDisbursementRequestService.ResponseWrapper resp = SubmitDisbursementRequestService.handleDisbursementRequest();
        Test.stopTest();
        
        // nothing at all should have stuck
        System.assertEquals(0, [SELECT COUNT() FROM Disbursement_Request__c]);
        System.assertEquals(0, [SELECT COUNT() FROM Requested_Item__c]);
        System.assertEquals(0, [SELECT COUNT() FROM Payee_Information__c]);
    }
}