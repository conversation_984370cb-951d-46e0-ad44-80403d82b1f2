@SuppressWarnings('PMD')
public class DisbursementValidationHandler {  
    public static void preventDuplicateDisbursements(Set<Id> requestIds, List<Disbursement__c> newDisbursements) {
        
        Map<Id, Integer> requestDisbursementCount = new Map<Id, Integer>();
        for (Disbursement__c existingDisbursement : [
            SELECT Disbursement_Request__c 
            FROM Disbursement__c 
            WHERE Disbursement_Request__c IN :requestIds
        ]) {
            if (requestDisbursementCount.containsKey(existingDisbursement.Disbursement_Request__c)) {
                requestDisbursementCount.put(existingDisbursement.Disbursement_Request__c,
                    requestDisbursementCount.get(existingDisbursement.Disbursement_Request__c) + 1);
            } else {
                requestDisbursementCount.put(existingDisbursement.Disbursement_Request__c, 1);
            }
        }
               
        Map<Id,Boolean> seenDisbursements = new Map<Id,Boolean>();
        
        for (Disbursement__c disbursement : newDisbursements) {
            Id requestId = disbursement.Disbursement_Request__c;
            Integer existingCount = requestDisbursementCount.get(requestId) != null ? requestDisbursementCount.get(requestId) : 0;

            // If there is already a Disbursement linked to the same DR, add an error
            if (seenDisbursements.containsKey(requestId) || existingCount >= 1) {
                String errorMessage = 'Each Disbursement Request may only create one Disbursement. ' + 
                                      'This request already has a Disbursement (ID: ' + requestId + ').';
                disbursement.addError(errorMessage);

                // Log the error to Nebula Logger with tags
                try {
                    String userContext = UserInfo.getUserId() != null ? UserInfo.getUserId() : 'Integration Process';
                    String logMessage = 'Error for DR ID: ' + requestId + ' by User: ' + userContext + 
                                        '. Validation Error: ' + errorMessage;

                    // Log error with tags
                    Nebula.Logger.error('Disbursement Request validation failed for DR ID: ' + requestId + 
                                         '. Error Message: ' + errorMessage + ' User Context: ' + userContext)
                        .addTag('Disbursement and its Disbursement Requests')
                        .addTag('Validation Error');

                    Nebula.Logger.info('Disbursement Request ID: ' + requestId + ' triggered validation error.')
                        .addTag('Disbursement and its Disbursement Requests')
                        .addTag('Info');

                    // Save the logs
                    Nebula.Logger.saveLog();
                } catch (Exception e) {
                    System.debug('Error while logging to Nebula Logger: ' + e.getMessage());
                    // Log any issues with Nebula Logger
                    Nebula.Logger.error('Error logging validation error for DR ID: ' + requestId + ' - ' + e.getMessage())
                        .addTag('Disbursements and its Disbursement Requests')
                        .addTag('Logging Error');
                    Nebula.Logger.saveLog();
                }
            }
            else{
                seenDisbursements.put(requestId,true);
            }
        }
    }
}