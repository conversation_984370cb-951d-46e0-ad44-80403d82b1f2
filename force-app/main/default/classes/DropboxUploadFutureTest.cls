@SuppressWarnings('PMD')
@isTest
private class DropboxUploadFutureTest {
    
    // Helper method to create ContentVersion and link it
    private static ContentVersion createContentVersion(String title, String extension, Id parentId) {
        ContentVersion cv = new ContentVersion(
            Title = title,
            PathOnClient = title + '.' + extension,
            VersionData = Blob.valueOf('Test Content'),
            IsMajorVersion = true,
            // Ensure custom fields are added to the layout/object if not already present
            Dropbox_Uploaded_Date__c = null,
            Dropbox_Sync_Status__c = 'Not Started' // Initial state for testing filter
        );
        insert cv;
        
        // Get the ContentDocumentId
        cv = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id LIMIT 1];
        
        // Create ContentDocumentLink
        ContentDocumentLink cdl = new ContentDocumentLink(
            ContentDocumentId = cv.ContentDocumentId,
            LinkedEntityId = parentId,
            ShareType = 'V' // 'V' - Viewer; 'C' - Collaborator; 'I' - Inferred
        );
        insert cdl;
        
        return cv;
    }
    
    // Helper method to create ContentDistribution
   /* private static ContentDistribution createContentDistribution(Id contentVersionId) {
        ContentDistribution cd = new ContentDistribution(
            Name = 'Test Distribution',
            ContentVersionId = contentVersionId,
            PreferencesAllowViewInBrowser = true,
            PreferencesLinkLatestVersion = true,
            PreferencesNotifyOnVisit = false,
            PreferencesPasswordRequired = false,
            PreferencesAllowOriginalDownload = true
        );
        insert cd;
        // Retrieve the generated URL after insert
        return [SELECT Id, ContentDownloadUrl FROM ContentDistribution WHERE Id = :cd.Id LIMIT 1];
    }
    */
        private static ContentDistribution createContentDistribution(Id contentVersionId) {
        List<ContentDistribution> existing = [
            SELECT Id, ContentDownloadUrl
            FROM ContentDistribution
            WHERE ContentVersionId = :contentVersionId
            LIMIT 1
        ];
        
        if (!existing.isEmpty()) {
            return existing[0];
        }
        
        ContentDistribution cd = new ContentDistribution(
            Name = 'Test Distribution',
            ContentVersionId = contentVersionId,
            PreferencesAllowViewInBrowser = true,
            PreferencesLinkLatestVersion = true,
            PreferencesNotifyOnVisit = false,
            PreferencesPasswordRequired = false,
            PreferencesAllowOriginalDownload = true
        );
        insert cd;
        
        return [SELECT Id, ContentDownloadUrl FROM ContentDistribution WHERE Id = :cd.Id LIMIT 1];
    }
    
    
    @testSetup
    static void setup() {
        // Create a parent record (Opportunity)
        Opportunity opp = new Opportunity(Name = 'Test Opp', StageName = 'Prospecting', CloseDate = Date.today().addMonths(1));
        insert opp;
        
        // Create ContentVersions
        // 1. File to be successfully uploaded
        ContentVersion cvSuccess = createContentVersion('TestFile1', 'txt', opp.Id);
        // 2. File to be excluded
        ContentVersion cvExclude = createContentVersion('TestNoteFile', 'snote', opp.Id);
        // 3. File that will simulate a failure
        ContentVersion cvFail = createContentVersion('TestFile2', 'pdf', opp.Id);
        // 4. File already processed (should be ignored by query)
        ContentVersion cvProcessed = createContentVersion('TestFile3', 'docx', opp.Id);
        cvProcessed.Dropbox_Sync_Status__c = 'Synced';
        cvProcessed.Dropbox_Uploaded_Date__c = System.now();
        update cvProcessed;
        
        // Create ContentDistributions to simulate download URLs
        createContentDistribution(cvSuccess.Id);
        // No distribution needed for excluded file as getDownloadLinks won't be called for it
        createContentDistribution(cvFail.Id);
        // No distribution needed for already processed file
    }
    
    // --- Mock Http Callout Implementations ---
    
    // Mock for successful Dropbox upload response
    private class MockHttpResponseGeneratorSuccess implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            // Simulate Dropbox async job start response
            res.setBody('{"_tag": "async_job_id", "async_job_id": "dbjid:ABCDEFG12345"}');
            res.setStatusCode(200); // Success
            res.setStatus('OK');
            
            // Simulate the log record that DropboxController might create
            Custom_Exception__c log = new Custom_Exception__c(
                Exception_Message__c = 'Callout successful to: ' + req.getEndpoint(),
                Exception_Type__c = 'Dropbox API Call'
            );
            // Simulate the map structure returned by DropboxController
            Map<String, Object> controllerReturnMap = new Map<String, Object>{
                'res' => res,
                    'log' => log,
                    'success' => true, // Assuming controller sets this based on status
                    'jobId' => 'dbjid:ABCDEFG12345' // Added based on how saveFileToDropbox uses the response
                    };
                        
                        // *** IMPORTANT ***: The actual callout is made inside DropboxController.
                        // The mock here simulates the response Dropbox *would* send back to that controller.
                        // The test class interacts with DropboxUploadFuture, which *consumes* the result
                        // prepared by saveFileToDropbox (which in turn got it from DropboxController).
                        // For simplicity in this test, we directly mock the HTTP response.
                        // A more complex test could involve mocking the *controller* method if needed.
                        
                        return res; // Return the raw HttpResponse, as HttpCalloutMock requires
        }
    }
    
    
    // Mock for failed Dropbox upload response
    private class MockHttpResponseGeneratorFailure implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setBody('{"error_summary": "path/not_found/..", "error": {".tag": "path", "path": "not_found"}}');
            res.setStatusCode(409); // Conflict/Error example
            res.setStatus('Conflict');
            
            // Simulate the log record that DropboxController might create for failure
            Custom_Exception__c log = new Custom_Exception__c(
                Exception_Message__c = 'Callout failed: ' + res.getBody(),
                Exception_Type__c = 'Dropbox API Call Error'
            );
            
            // Simulate the map structure returned by DropboxController
            Map<String, Object> controllerReturnMap = new Map<String, Object>{
                'res' => res,
                    'log' => log,
                    'success' => false // Assuming controller sets this based on status
                    };
                        // See note in MockHttpResponseGeneratorSuccess about mocking scope.
                        
                        return res; // Return the raw HttpResponse
        }
    }
    
    // Mock specifically for testing the scenario where the saveFileToDropbox call returns success=false
    private class MockHttpResponseGeneratorSaveFileFailure implements HttpCalloutMock {
        // Re-use the failure response, the difference is how we use it in the test method
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setBody('{"error_summary": "internal_server_error/..", "error": {".tag": "internal_error"}}');
            res.setStatusCode(500); // Internal Server Error example
            res.setStatus('Server Error');
            return res;
        }
    }
    
    
    @isTest
    static void testSuccessfulUpload() {
        Opportunity opp = [SELECT Id FROM Opportunity WHERE Name = 'Test Opp' LIMIT 1];
        List<ContentVersion> cvs = [
            SELECT Id, ContentDocumentId, Dropbox_Sync_Status__c, Dropbox_Async_Job_Id__c, FileExtension
            FROM ContentVersion
            WHERE Title LIKE 'TestFile%' AND FileExtension != 'snote' // Get success and fail candidates
        ];
        
        // Set the mock response for SUCCESS
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGeneratorSuccess());
        
        Test.startTest();
        DropboxUploadFuture.uploadToDropbox(opp.Id, '/Test/Opportunity/Folder');
        Test.stopTest();
        
    }
    
    
    @isTest
    static void testFailedUploadScenario() {
        Opportunity opp = [SELECT Id FROM Opportunity WHERE Name = 'Test Opp' LIMIT 1];
        List<ContentVersion> cvs = [
            SELECT Id, FileExtension
            FROM ContentVersion
            WHERE Title LIKE 'TestFile%' AND FileExtension != 'snote' // Get eligible files
            AND Dropbox_Sync_Status__c = 'Not Started' // Only target unprocessed
        ];
        
        // Set the mock response for FAILURE
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGeneratorSaveFileFailure()); // Use 500 error mock
        
        Test.startTest();
        DropboxUploadFuture.uploadToDropbox(opp.Id, '/Test/Opportunity/Folder/Failure');
        Test.stopTest();
        
    }
    
    @isTest
    static void testFileExclusion() {
        Opportunity opp = [SELECT Id FROM Opportunity WHERE Name = 'Test Opp' LIMIT 1];
        ContentVersion snoteCv = [SELECT Id, Dropbox_Sync_Status__c, Dropbox_Async_Job_Id__c FROM ContentVersion WHERE FileExtension = 'snote' LIMIT 1];
        ContentVersion normalCv = [SELECT Id, Dropbox_Sync_Status__c, Dropbox_Async_Job_Id__c FROM ContentVersion WHERE FileExtension = 'txt' LIMIT 1];
        
        // Initial state check
        System.assertEquals('Not Started', snoteCv.Dropbox_Sync_Status__c);
        System.assertEquals(null, snoteCv.Dropbox_Async_Job_Id__c);
        System.assertEquals('Not Started', normalCv.Dropbox_Sync_Status__c);
        
        
        // Set a success mock, as we want to ensure the normal file proceeds but the snote doesn't
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGeneratorSuccess());
        
        Test.startTest();
        DropboxUploadFuture.uploadToDropbox(opp.Id, '/Test/Opportunity/Folder/Exclusion');
        Test.stopTest();
    }
    
    @isTest
    static void testNoFilesToProcess() {
        Opportunity opp = new Opportunity(Name='Opp With No Files', StageName='Closed Won', CloseDate=Date.today());
        insert opp;
        
        // No files linked to this Opp
        
        Test.startTest();
        // Set a mock just in case, although no callout should happen
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGeneratorSuccess());
        DropboxUploadFuture.uploadToDropbox(opp.Id, '/Test/Opportunity/Folder/Empty');
        Test.stopTest();
        
    }

}