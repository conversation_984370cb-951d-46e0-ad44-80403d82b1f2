@isTest
private class CashflowSessionServiceTest {
    
    static testMethod void testNullProject_createsProject() {
        // Create an Account to satisfy AccountId__c
        Account acc = new Account(Name = 'Test Acc');
        insert acc;
        
        // Insert a session with Project__c = null
        Test.startTest();
            Cash_Flow_Session__c sess = new Cash_Flow_Session__c(
                Name               = 'NullProjSession',
                AccountId__c       = acc.Id,
                CompletedAt__c     = System.now(),
                Processed_JSON__c  = '{"subContractors":{"M":{"hasSubContractors":{"S":"yes"},"subContractorCount":{"S":"1"},"subContractors":{"L":[{"M":{"scopeOfWork":{"S":"Building"},"amount":{"N":"5000"},"amountVariation":{"S":"fixed"},"payments":{"L":[]},"name":{"S":"XConstruction"},"id":{"N":"0.*****************"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]}}},"miscExpenses":{"M":{"hasMiscExpenses":{"S":"yes"},"hasMiscExpenseCount":{"S":"1"},"miscExpenses":{"L":[{"M":{"amount":{"N":"5000"},"amountVariation":{"S":"fixed"},"payments":{"L":[]},"name":{"S":"Other"},"id":{"N":"0.****************"},"type":{"S":"other"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]}}},"computed":{"M":{}},"equipment":{"M":{"equipmentVendors":{"L":[{"M":{"amount":{"N":"10000"},"amountVariation":{"S":"fixed"},"payments":{"L":[]},"name":{"S":"Vendor"},"equipment":{"S":"Crane"},"id":{"N":"0.21581394035995882"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]},"equipmentVendorCount":{"S":"1"},"hasEquipment":{"S":"yes"}}},"bondPremium":{"M":{"totalAmount":{"N":"10000"},"hasBondPremium":{"S":"yes"},"dueDate":{"S":"2025-01-31"}}},"type":{"M":{"type":{"S":"construction"}}},"invoiceSchedule":{"M":{"invoiceSchedule":{"L":[]}}},"mode":{"S":"consumer"},"materials":{"M":{"hasMaterials":{"S":"yes"},"materialOrders":{"L":[{"M":{"amount":{"N":"15000"},"amountVariation":{"S":"fixed"},"materialType":{"S":"Steel"},"payments":{"L":[]},"name":{"S":"Steel Corp"},"id":{"N":"0.5863218977090419"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]},"materialOrderCount":{"S":"1"}}},"review":{"M":{}},"meta":{"M":{"lastPaymentDate":{"S":"2024-12-27"},"firstPaymentDate":{"S":"2024-10-25"},"endDate":{"S":"2024-12-25"},"startDate":{"S":"2024-10-23"},"lastDate":{"S":"2025-02-23"},"schedules":{"M":{"monthly":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]},"bi_weekly":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]},"weekly":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-01"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-08"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-22"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-06"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-20"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]}}}}},"payAppSchedule":{"M":{"payAppWeek":{"S":"4"},"payAppAllocations":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-01"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-08"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-22"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-06"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-20"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"60000"}}}]},"allocationType":{"S":""},"milestonesCount":{"S":"0"},"payAppFrequency":{"S":"weekly"}}},"details":{"M":{"totalEstimatedSubcontractLabor":{"N":"50000"},"payAppFrequency":{"S":"weekly"},"contractorName":{"S":"X10 Construction"},"retainagePercent":{"N":"0"},"userCompanyState":{"S":"Florida"},"totalInvoices":{"S":""},"moneyReceivedToDate":{"N":"40000"},"referralSource":{"S":"Google"},"userEmail":{"S":"<EMAIL>"},"userCompany":{"S":"Steel Construction"},"shipmentDate":{"S":""},"totalEstimatedEquipmentRental":{"N":"100000"},"totalValue":{"N":"600000"},"marginPercent":{"N":"50"},"totalEstimatedDirectPayroll":{"N":"24000"},"userName":{"S":"John Smith"},"invoiceDate":{"S":""},"customerName":{"S":""},"totalEstimatedMiscExpenses":{"N":"50000"},"totalEstimatedMaterial":{"N":"150000"},"totalEstimatedBondPremium":{"N":"10000"},"jointChecks":{"S":"no"},"projectName":{"S":"Project Steel"},"paymentDate":{"S":""},"paymentDelayDays":{"N":"2"},"startDate":{"S":"2024-10-23"},"totalCost":{"S":""},"projectLengthWeeks":{"N":"10"}}},"payroll":{"M":{"amountPerPeriod":{"N":"2400"},"hasPayroll":{"S":"yes"},"payrollFrequency":{"S":"weekly"},"amountVariation":{"S":"fixed"},"payrollAllocations":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-01"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-08"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-22"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-06"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-20"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]}}}}'
                // Project__c left null
            );
            insert sess;
        Test.stopTest();
        
        // The service should have created a Project__c named "Auto-{sessionId}"
        String expectedName = 'Auto-' + sess.Id;
        List<Project__c> projs = [
            SELECT Id, Name 
              FROM Project__c 
             WHERE Name = :expectedName
        ];
        System.assertEquals(
            1, projs.size(),
            'A new Project__c should be created when Project__c is null'
        );
    }
    
    
    static testMethod void testInvalidProject_createsProject() {
        Account acc = new Account(Name = 'Test Acc 2');
        insert acc;
        
        // Build a syntactically valid but non‐existent Project Id
        String prefix    = Project__c.SObjectType.getDescribe().getKeyPrefix();
        String invalidId = prefix + '************'; // 15-char ID
        
        Test.startTest();
            Cash_Flow_Session__c sess = new Cash_Flow_Session__c(
                Name               = 'InvalidProjSession',
                AccountId__c       = acc.Id,
                Project__c         = invalidId,
                CompletedAt__c     = System.now(),
                Processed_JSON__c  = '{"subContractors":{"M":{"hasSubContractors":{"S":"yes"},"subContractorCount":{"S":"1"},"subContractors":{"L":[{"M":{"scopeOfWork":{"S":"Building"},"amount":{"N":"5000"},"amountVariation":{"S":"fixed"},"payments":{"L":[]},"name":{"S":"XConstruction"},"id":{"N":"0.*****************"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]}}},"miscExpenses":{"M":{"hasMiscExpenses":{"S":"yes"},"hasMiscExpenseCount":{"S":"1"},"miscExpenses":{"L":[{"M":{"amount":{"N":"5000"},"amountVariation":{"S":"fixed"},"payments":{"L":[]},"name":{"S":"Other"},"id":{"N":"0.****************"},"type":{"S":"other"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]}}},"computed":{"M":{}},"equipment":{"M":{"equipmentVendors":{"L":[{"M":{"amount":{"N":"10000"},"amountVariation":{"S":"fixed"},"payments":{"L":[]},"name":{"S":"Vendor"},"equipment":{"S":"Crane"},"id":{"N":"0.21581394035995882"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]},"equipmentVendorCount":{"S":"1"},"hasEquipment":{"S":"yes"}}},"bondPremium":{"M":{"totalAmount":{"N":"10000"},"hasBondPremium":{"S":"yes"},"dueDate":{"S":"2025-01-31"}}},"type":{"M":{"type":{"S":"construction"}}},"invoiceSchedule":{"M":{"invoiceSchedule":{"L":[]}}},"mode":{"S":"consumer"},"materials":{"M":{"hasMaterials":{"S":"yes"},"materialOrders":{"L":[{"M":{"amount":{"N":"15000"},"amountVariation":{"S":"fixed"},"materialType":{"S":"Steel"},"payments":{"L":[]},"name":{"S":"Steel Corp"},"id":{"N":"0.5863218977090419"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]},"materialOrderCount":{"S":"1"}}},"review":{"M":{}},"meta":{"M":{"lastPaymentDate":{"S":"2024-12-27"},"firstPaymentDate":{"S":"2024-10-25"},"endDate":{"S":"2024-12-25"},"startDate":{"S":"2024-10-23"},"lastDate":{"S":"2025-02-23"},"schedules":{"M":{"monthly":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]},"bi_weekly":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]},"weekly":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-01"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-08"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-22"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-06"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-20"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]}}}}},"payAppSchedule":{"M":{"payAppWeek":{"S":"4"},"payAppAllocations":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-01"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-08"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-22"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-06"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-20"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"60000"}}}]},"allocationType":{"S":""},"milestonesCount":{"S":"0"},"payAppFrequency":{"S":"weekly"}}},"details":{"M":{"totalEstimatedSubcontractLabor":{"N":"50000"},"payAppFrequency":{"S":"weekly"},"contractorName":{"S":"X10 Construction"},"retainagePercent":{"N":"0"},"userCompanyState":{"S":"Florida"},"totalInvoices":{"S":""},"moneyReceivedToDate":{"N":"40000"},"referralSource":{"S":"Google"},"userEmail":{"S":"<EMAIL>"},"userCompany":{"S":"Steel Construction"},"shipmentDate":{"S":""},"totalEstimatedEquipmentRental":{"N":"100000"},"totalValue":{"N":"600000"},"marginPercent":{"N":"50"},"totalEstimatedDirectPayroll":{"N":"24000"},"userName":{"S":"John Smith"},"invoiceDate":{"S":""},"customerName":{"S":""},"totalEstimatedMiscExpenses":{"N":"50000"},"totalEstimatedMaterial":{"N":"150000"},"totalEstimatedBondPremium":{"N":"10000"},"jointChecks":{"S":"no"},"projectName":{"S":"Project Steel"},"paymentDate":{"S":""},"paymentDelayDays":{"N":"2"},"startDate":{"S":"2024-10-23"},"totalCost":{"S":""},"projectLengthWeeks":{"N":"10"}}},"payroll":{"M":{"amountPerPeriod":{"N":"2400"},"hasPayroll":{"S":"yes"},"payrollFrequency":{"S":"weekly"},"amountVariation":{"S":"fixed"},"payrollAllocations":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-01"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-08"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-22"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-06"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-20"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]}}}}'
            );
            insert sess;
        Test.stopTest();
        
        // The service should have created a Project__c named "Auto-{invalidId}"
        String expectedName = 'Auto-' + invalidId;
        List<Project__c> projs = [
            SELECT Id, Name 
              FROM Project__c 
             WHERE Name = :expectedName
        ];
        System.assertEquals(
            1, projs.size(),
            'A new Project__c should be created when Project__c refers to a non-existent Id'
        );
    }
    
    
    static testMethod void testValidProject_noNewProject() {
        Account acc = new Account(Name = 'Test Acc 3');
        insert acc;
        
        // Pre-create a Project__c
        Project__c existing = new Project__c(Name = 'ExistingProject');
        insert existing;
        
        // Record count before
        Integer beforeCount = [SELECT count() FROM Project__c];
        
        Test.startTest();
            Cash_Flow_Session__c sess = new Cash_Flow_Session__c(
                Name               = 'ValidProjSession',
                AccountId__c       = acc.Id,
                Project__c         = existing.Id,
                CompletedAt__c     = System.now(),
                Processed_JSON__c  = '{"subContractors":{"M":{"hasSubContractors":{"S":"yes"},"subContractorCount":{"S":"1"},"subContractors":{"L":[{"M":{"scopeOfWork":{"S":"Building"},"amount":{"N":"5000"},"amountVariation":{"S":"fixed"},"payments":{"L":[]},"name":{"S":"XConstruction"},"id":{"N":"0.*****************"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]}}},"miscExpenses":{"M":{"hasMiscExpenses":{"S":"yes"},"hasMiscExpenseCount":{"S":"1"},"miscExpenses":{"L":[{"M":{"amount":{"N":"5000"},"amountVariation":{"S":"fixed"},"payments":{"L":[]},"name":{"S":"Other"},"id":{"N":"0.****************"},"type":{"S":"other"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]}}},"computed":{"M":{}},"equipment":{"M":{"equipmentVendors":{"L":[{"M":{"amount":{"N":"10000"},"amountVariation":{"S":"fixed"},"payments":{"L":[]},"name":{"S":"Vendor"},"equipment":{"S":"Crane"},"id":{"N":"0.21581394035995882"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]},"equipmentVendorCount":{"S":"1"},"hasEquipment":{"S":"yes"}}},"bondPremium":{"M":{"totalAmount":{"N":"10000"},"hasBondPremium":{"S":"yes"},"dueDate":{"S":"2025-01-31"}}},"type":{"M":{"type":{"S":"construction"}}},"invoiceSchedule":{"M":{"invoiceSchedule":{"L":[]}}},"mode":{"S":"consumer"},"materials":{"M":{"hasMaterials":{"S":"yes"},"materialOrders":{"L":[{"M":{"amount":{"N":"15000"},"amountVariation":{"S":"fixed"},"materialType":{"S":"Steel"},"payments":{"L":[]},"name":{"S":"Steel Corp"},"id":{"N":"0.5863218977090419"},"paymentFrequency":{"S":"weekly"},"paymentDate":{"S":""},"paymentTerms":{"S":"cod"}}}]},"materialOrderCount":{"S":"1"}}},"review":{"M":{}},"meta":{"M":{"lastPaymentDate":{"S":"2024-12-27"},"firstPaymentDate":{"S":"2024-10-25"},"endDate":{"S":"2024-12-25"},"startDate":{"S":"2024-10-23"},"lastDate":{"S":"2025-02-23"},"schedules":{"M":{"monthly":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]},"bi_weekly":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]},"weekly":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-01"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-08"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-22"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-06"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-20"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]}}}}},"payAppSchedule":{"M":{"payAppWeek":{"S":"4"},"payAppAllocations":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-01"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-08"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-22"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-06"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-20"},"amount":{"N":"60000"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"60000"}}}]},"allocationType":{"S":""},"milestonesCount":{"S":"0"},"payAppFrequency":{"S":"weekly"}}},"details":{"M":{"totalEstimatedSubcontractLabor":{"N":"50000"},"payAppFrequency":{"S":"weekly"},"contractorName":{"S":"X10 Construction"},"retainagePercent":{"N":"0"},"userCompanyState":{"S":"Florida"},"totalInvoices":{"S":""},"moneyReceivedToDate":{"N":"40000"},"referralSource":{"S":"Google"},"userEmail":{"S":"<EMAIL>"},"userCompany":{"S":"Steel Construction"},"shipmentDate":{"S":""},"totalEstimatedEquipmentRental":{"N":"100000"},"totalValue":{"N":"600000"},"marginPercent":{"N":"50"},"totalEstimatedDirectPayroll":{"N":"24000"},"userName":{"S":"John Smith"},"invoiceDate":{"S":""},"customerName":{"S":""},"totalEstimatedMiscExpenses":{"N":"50000"},"totalEstimatedMaterial":{"N":"150000"},"totalEstimatedBondPremium":{"N":"10000"},"jointChecks":{"S":"no"},"projectName":{"S":"Project Steel"},"paymentDate":{"S":""},"paymentDelayDays":{"N":"2"},"startDate":{"S":"2024-10-23"},"totalCost":{"S":""},"projectLengthWeeks":{"N":"10"}}},"payroll":{"M":{"amountPerPeriod":{"N":"2400"},"hasPayroll":{"S":"yes"},"payrollFrequency":{"S":"weekly"},"amountVariation":{"S":"fixed"},"payrollAllocations":{"L":[{"M":{"date":{"S":"2024-10-25"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-01"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-08"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-15"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-22"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-11-29"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-06"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-13"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-20"},"amount":{"N":"0"}}},{"M":{"date":{"S":"2024-12-27"},"amount":{"N":"0"}}}]}}}}'
            );
            insert sess;
        Test.stopTest();
        
        // There should be no additional Project__c beyond the one we created
        Integer afterCount = [SELECT count() FROM Project__c];
        System.assertEquals(
            beforeCount, 
            afterCount,
            'No new Project__c should be created when Project__c is already valid'
        );
    }
}