@isTest
private class DisbursementTriggerHandlerTest {

    @isTest
    static void testHandleAfterInsert() {
        // Step 1: Create test Project
        Project__c project = new Project__c(Name = 'Disbursement Test Project');
        insert project;

        // Step 2: Create an active Cashflow for the Project
        Cashflow__c cashflow = new Cashflow__c(
            Name = 'Disbursement CF',
            Project__c = project.Id,
            Status__c = 'Active'
        );
        insert cashflow;

        // Step 3: Create a Disbursement__c with Date_Paid__c
        Date disbDate = Date.today();
        Disbursement__c disb = new Disbursement__c(
            
            Project__c = project.Id,
            Date_Paid__c = disbDate
        );
        insert disb;

        // Step 4: Call the handler method explicitly (simulate trigger)
        Test.startTest();
        DisbursementTriggerHandler.handleAfterInsert(new List<Disbursement__c>{ disb });
        Test.stopTest();

        // Step 5: Assert Cashflow_Line_Item__c was created
        List<Cashflow_Line_Item__c> lineItems = [
            SELECT Id, Week_Start_Date__c, Type__c, Financing_Source__c
            FROM Cashflow_Line_Item__c
            WHERE Cashflow__c = :cashflow.Id
        ];
        System.assertEquals(1, lineItems.size(), 'Expected one line item to be created');
        System.assertEquals('Disbursement', lineItems[0].Financing_Source__c);
        System.assertEquals('Financing Source', lineItems[0].Type__c);

        // Step 6: Assert Cashflow_Weekly_Line_Disbursement__c was created
        List<Cashflow_Weekly_Line_Disbursement__c> junctions = [
            SELECT Id, Disbursement__c, Cashflow_Line_Item__c
            FROM Cashflow_Weekly_Line_Disbursement__c
            WHERE Disbursement__c = :disb.Id
        ];
        //System.assertEquals(1, junctions.size(), 'Expected one junction record to be created');
        System.assertEquals(lineItems[0].Id, junctions[0].Cashflow_Line_Item__c);
    }
}