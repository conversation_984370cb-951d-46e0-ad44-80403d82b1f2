@RestResource(urlMapping='/FileUpload/')
global without sharing class FileUploadAPI {

    global class FileUploadResponse {
        public String id;
        public Boolean success;
        public List<String> errors = new List<String>();
    }

    @HttpPost
    global static FileUploadResponse uploadFile(String Title, String PathOnClient, String VersionData, String FirstPublishLocationId, Boolean isChatAttachment) {
        FileUploadResponse response = new FileUploadResponse();
        
        try {
            Blob bodyBlob = Blob.valueOf(VersionData); 
            
            ContentVersion contentVersion = new ContentVersion(
                Title = Title,
                PathOnClient = PathOnClient,
                VersionData = bodyBlob,
                IsMajorVersion = true,
                ContentLocation = 'S'
            );

            // if isChatAttachment is true then set FeedItemTriggerHandler.hasInsertedChatterPost is equal to true
            if (isChatAttachment != null && isChatAttachment) {
                FeedItemTriggerHandler.hasInsertedChatterPost = true;
            }
            
            insert contentVersion;

            contentVersion = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id];
        
            ContentDocumentLink contentDocLink = new ContentDocumentLink(
                ContentDocumentId = contentVersion.ContentDocumentId,
                LinkedEntityId = FirstPublishLocationId, 
                Visibility = 'AllUsers'
            );
            insert contentDocLink;

            response.id = contentDocLink.Id;
            response.success = true;

        } catch (Exception e) {
            response.success = false;
            response.errors.add(e.getMessage());
        }
        return response;
    }
}