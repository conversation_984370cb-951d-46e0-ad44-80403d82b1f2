@SuppressWarnings('PMD')
@isTest(SeeAllData=true)
private class PDFGeneratorQueueableTest {
    
    @isTest
    static void testPDFGeneratorQueueableSuccess() {
        // Bypass the Test.isRunningTest check
        PDFGeneratorQueueable.bypassTest = true;
        
        // Create a test Opportunity (to be used as the LinkedEntity)
        Opportunity opp = new Opportunity(Name = 'Test Opportunity', StageName = 'Prospecting', CloseDate = Date.today());
        insert opp;
        
        ContentVersion contentVersion = new ContentVersion(
            Title = 'TestFile',
            PathOnClient = 'TestFile.pdf',
            VersionData = Blob.valueOf('test'),
            IsMajorVersion = true,
            ContentLocation = 'S'
        );
        insert contentVersion;
        
        
        // Instantiate the Queueable job with test parameters
        PDFGeneratorQueueable job = new PDFGeneratorQueueable(
            opp.Id,
            'Test email body',
            'http://example.com',
            'TestFile'
        );
        
        Test.startTest();
            System.enqueueJob(job);
        Test.stopTest();
        
        // Verify that a ContentVersion record was created
        ContentVersion cv = [SELECT Id, Title, ContentDocumentId FROM ContentVersion WHERE Title = 'TestFile' LIMIT 1];
        System.assertNotEquals(null, cv.Id, 'A ContentVersion should have been inserted.');
        

    }
    
    
  }