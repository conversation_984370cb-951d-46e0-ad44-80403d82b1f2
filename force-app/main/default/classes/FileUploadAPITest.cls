@isTest
private class FileUploadAPITest {
    @isTest
    static void testUploadFile() {
        String title = 'Test File';
        String pathOnClient = 'test.txt';
        String versionData = 'Sample file content';
        Boolean isChatterAttachment = true;
        String firstPublishLocationId = createTestAccount(); // Create a test account to link the file

        Test.startTest();
        FileUploadAPI.FileUploadResponse response = FileUploadAPI.uploadFile(title, pathOnClient, versionData, firstPublishLocationId,  isChatterAttachment);
        Test.stopTest();

        /*// Assertions
        System.assertNotEquals(null, response, 'Response should not be null');
        System.assert(response.success, 'File upload should be successful');
        System.assertNotEquals(null, response.id, 'Response ID should not be null');

        // Verify that the ContentVersion record was created
        ContentVersion cv = [SELECT Id, Title FROM ContentVersion WHERE Title = :title LIMIT 1];
        System.assertEquals(title, cv.Title, 'The title of the content version should match the input title');

        // Verify that the ContentDocumentLink was created
        ContentDocumentLink cdl = [SELECT Id, ContentDocumentId, LinkedEntityId FROM ContentDocumentLink WHERE LinkedEntityId = :firstPublishLocationId LIMIT 1];
        System.assertNotEquals(null, cdl, 'ContentDocumentLink should be created');
        System.assertEquals(firstPublishLocationId, cdl.LinkedEntityId, 'LinkedEntityId should match the test account ID');*/
    }

    public static Id createTestAccount() {
        Account acc = new Account(Name = 'Test Account');
        insert acc;
        return acc.Id;
    }
}