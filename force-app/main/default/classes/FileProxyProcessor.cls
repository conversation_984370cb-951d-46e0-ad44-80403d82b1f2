@SuppressWarnings('PMD')
public without sharing class FileProxyProcessor {
    
    public static void processFiles(Set<Id> fProxyIds) {
        // Query File_Proxy__c records to process
        List<File_Proxy__c> filesToProcess = [
            SELECT Id, ContentVersion_Id__c, Parent_Record_Id__c, Create_Activity_Log__c  
            FROM File_Proxy__c 
            WHERE Id IN :fProxyIds AND Status__c = 'Pending'
        ];
        
        // Lists and maps to manage links
        List<ContentDocumentLink> linksToInsert = new List<ContentDocumentLink>();
        Map<Id, Id> contentDocToProxyMap = new Map<Id, Id>();
        
        // Process each proxy record
        for (File_Proxy__c fs : filesToProcess) {
            // Get the ContentVersion and its ContentDocumentId
            ContentVersion cv = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :fs.ContentVersion_Id__c LIMIT 1];
            
            // Handle activity log suppression
            if (fs.Create_Activity_Log__c != null && !fs.Create_Activity_Log__c) {
                FeedItemTriggerHandler.hasInsertedChatterPost = true;
            }
            
            // Create link to parent record
            ContentDocumentLink cdl = new ContentDocumentLink(
                ContentDocumentId = cv.ContentDocumentId,
                LinkedEntityId = fs.Parent_Record_Id__c,
                Visibility = 'AllUsers'
            );
            linksToInsert.add(cdl);
            
            // Map ContentDocumentId to File_Proxy__c Id for later link removal
            contentDocToProxyMap.put(cv.ContentDocumentId, fs.Id);
            
            // Mark as processed
            fs.Status__c = 'Processed';
        }
        
        if (!linksToInsert.isEmpty()) {
            try {
                // Insert links to parent records
                insert linksToInsert;
                
                // Query existing links to proxy objects
                List<ContentDocumentLink> potentialLinks = [
                    SELECT Id, ContentDocumentId, LinkedEntityId 
                    FROM ContentDocumentLink 
                    WHERE ContentDocumentId IN :contentDocToProxyMap.keySet() 
                    AND LinkedEntityId IN :contentDocToProxyMap.values()
                ];
                
                // Identify links to delete (those linking to File_Proxy__c)
                List<ContentDocumentLink> linksToDelete = new List<ContentDocumentLink>();
                for (ContentDocumentLink cdl : potentialLinks) {
                    if (cdl.LinkedEntityId == contentDocToProxyMap.get(cdl.ContentDocumentId)) {
                        linksToDelete.add(cdl);
                    }
                }
                
                // Delete links to proxy objects if they exist
                if (!linksToDelete.isEmpty()) {
                    delete linksToDelete;
                }
                
                // Update File_Proxy__c records
                update filesToProcess;
            } catch (Exception e) {
                System.debug('Error processing files: ' + e.getMessage());
                Nebula.Logger.error('Error processing file Proxy request ' + e.getMessage() + '--' + e.getStackTraceString())
                    .addTag('File Proxy Request');
                Nebula.Logger.saveLog();
            }
        }
    }
}