@SuppressWarnings('PMD')
@RestResource(urlMapping='/clientName')
global without sharing class GetClientNameAPI {

    @HttpGet
    global static void getAccountName() {
        RestResponse res = RestContext.response;
        AccountResponseWrapper responseWrapper = new AccountResponseWrapper();
        
        try {
            User currentUser = [SELECT Id, Name, ContactId, Contact.Name, Contact.AccountId FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
            
            if (currentUser.Contact.AccountId != null) {
                Account userAccount = [SELECT Id, Name FROM Account WHERE Id = :currentUser.Contact.AccountId LIMIT 1];
                
                responseWrapper.accountId = userAccount.Id;
                responseWrapper.accountName = userAccount.Name;
                responseWrapper.contactId = currentUser.ContactId;
                responseWrapper.contactName = currentUser.Contact.Name;
                responseWrapper.userId = currentUser.Id;
                responseWrapper.userFullName = currentUser.Name;
                res.responseBody = Blob.valueOf(JSON.serialize(responseWrapper));
            } else {
                res.statusCode = 404;
                res.responseBody = Blob.valueOf('No associated account found for the user.');
            }
        } catch (Exception ex) {
            res.statusCode = 500;
            res.responseBody = Blob.valueOf('Error retrieving account name: ' + ex.getMessage());
        }
    }
    
    public class AccountResponseWrapper {
        public String accountId;
        public String accountName;
        public String contactId;
        public String contactName;
        public String userId;
        public String userFullName;
    }
}