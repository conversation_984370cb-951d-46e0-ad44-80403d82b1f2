@SuppressWarnings('PMD')
@isTest
private class FetchCategoriesByOpenAIHandlerTest {
    
    @testSetup
    static void setupTestData() {
        // Unchanged setup code
        Bank_Account__c bankAccount = new Bank_Account__c(
            Name = 'Test Bank Account',
            Holder_Name__c = '<PERSON>',
            Current_Balance__c = 123.45
        );
        insert bankAccount;
        
        List<Bank_Transaction__c> transactions = new List<Bank_Transaction__c>{
            new Bank_Transaction__c(
                Transaction_Id__c = '384dc7b2-979e-40e4-a3f4-1aacc2b21y4g',
                Bank_Account__c = bankAccount.Id,
                Description__c = 'Test Transaction 1',
                Debit__c = 100.00,
                MF_Category__c = null
            ),
            new Bank_Transaction__c(
                Transaction_Id__c = '384dc7b2-979e-40e4-a3f4-1aacc2b21g4k',
                Bank_Account__c = bankAccount.Id,
                Description__c = 'Test Transaction 2',
                Credit__c = 50.00,
                MF_Category__c = 'Some Value'
            )
        };
        insert transactions;
    }
    
    // ------------------------------------------------------------------
    // 1. TEST getBankTransactions with various filters
    // ------------------------------------------------------------------
    @isTest
    static void testGetBankTransactions_AllFilter() {
        Bank_Account__c bankAccount = [SELECT Id FROM Bank_Account__c LIMIT 1];
        
        Test.startTest();
        List<Bank_Transaction__c> results = FetchCategoriesByOpenAIHandler.getBankTransactions(bankAccount.Id, 'All');
        Test.stopTest();
        
        System.assertNotEquals(null, results, 'Should return a list, not null');
        System.assertEquals(2, results.size(), 'Should return 2 transactions for filter All');
    }
    
    
    @isTest
    static void testGetBankTransactions_NoFlinksCategory() {
        Bank_Account__c bankAccount = [SELECT Id FROM Bank_Account__c LIMIT 1];
        Test.startTest();
        List<Bank_Transaction__c> results = FetchCategoriesByOpenAIHandler.getBankTransactions(bankAccount.Id, 'NoFlinksCategory');
        Test.stopTest();
        
        // For this filter, both transactions should qualify if Category__c is null or empty.
        System.assertEquals(2, results.size(), 'Should return 2 transactions for NoFlinksCategory filter');
    }
    
    @isTest
    static void testGetBankTransactions_NoMFCategory() {
        Bank_Account__c bankAccount = [SELECT Id FROM Bank_Account__c LIMIT 1];
        Test.startTest();
        // Only the first record in our setup has MF_Category__c as null.
        List<Bank_Transaction__c> results = FetchCategoriesByOpenAIHandler.getBankTransactions(bankAccount.Id, 'NoMFCategory');
        Test.stopTest();
        

    }
    
    @isTest
    static void testGetBankTransactionsWithEmptyId() {
        try {
            Test.startTest();
            FetchCategoriesByOpenAIHandler.getBankTransactions(null, 'All');
            Test.stopTest();
            System.assert(false, 'Expected exception for empty bank account ID');
        } catch (IllegalArgumentException e) {
            System.assertEquals('Bank Account ID cannot be empty', e.getMessage(),
                'Should throw IllegalArgumentException if bankAccountId is null');
        }
    }
    
    // ------------------------------------------------------------------
    // 2. TEST categorizeTransactions
    // ------------------------------------------------------------------
    @isTest
    static void testCategorizeTransactions_Success() {
        Bank_Transaction__c txn = [SELECT Id, Description__c, Debit__c, Credit__c 
                                  FROM Bank_Transaction__c 
                                  LIMIT 1];
        
        Test.setMock(HttpCalloutMock.class, new MockOpenAICalloutSuccess(txn.Id));
        
        Test.startTest();
        FetchCategoriesByOpenAIHandler.categorizeTransactions(new List<Bank_Transaction__c>{ txn });
        Test.stopTest();
        
        Bank_Transaction__c updatedTxn = [SELECT AI_Category__c, Synced_Status__c 
                                          FROM Bank_Transaction__c 
                                          WHERE Id = :txn.Id];
        
        System.assertEquals('Overhead', updatedTxn.AI_Category__c, 'AI Category should be set to Overhead');
        System.assertEquals('Success', updatedTxn.Synced_Status__c, 'Synced Status should be Success');
    }
    
    @isTest
    static void testCategorizeTransactionsWithEmptyList() {
        try {
            Test.startTest();
            FetchCategoriesByOpenAIHandler.categorizeTransactions(new List<Bank_Transaction__c>());
            Test.stopTest();
            System.assert(false, 'Expected exception for empty transaction list');
        } catch (IllegalArgumentException e) {
            System.assertEquals('Transaction list cannot be empty', e.getMessage(),
                'Should throw exception if transaction list is empty');
        }
    }
    
    @isTest
    static void testCategorizeTransactions_Failure() {
        Bank_Transaction__c txn = [SELECT Id, Debit__c, Credit__c, Description__c FROM Bank_Transaction__c LIMIT 1];
        List<Bank_Transaction__c> transactions = new List<Bank_Transaction__c>{ txn };
        
        // Set up mock callout to simulate a failed OpenAI response (status 400)
        Test.setMock(HttpCalloutMock.class, new MockOpenAICalloutFailure());
        
        try {
            Test.startTest();
            FetchCategoriesByOpenAIHandler.categorizeTransactions(transactions);
            Test.stopTest();
            System.assert(false, 'Expected CalloutException to be thrown');
        } catch (CalloutException e) {
            System.assert(e.getMessage().contains('OpenAI API call failed'),
                'Expected error message to contain "OpenAI API call failed"');
        }
    }
    
    @isTest
    static void testCategorizeTransactions_NoValidContent() {
        // This test simulates a 200 response that does not include valid content.
        Bank_Transaction__c txn = [SELECT Id, Debit__c, Credit__c, Description__c FROM Bank_Transaction__c LIMIT 1];
        List<Bank_Transaction__c> transactions = new List<Bank_Transaction__c>{ txn };
        
        Test.setMock(HttpCalloutMock.class, new MockOpenAICalloutNoValidContent());
        
        try {
            Test.startTest();
            FetchCategoriesByOpenAIHandler.categorizeTransactions(transactions);
            Test.stopTest();
        } catch (CalloutException e) {
            System.debug('Max retries reached. Processing failed.');
        }
    }
    
    @isTest
    static void testCategorizeTransactions_RateLimit() {
        // Simulate a callout returning status 400 with error code 429 (rate limit)
        Bank_Transaction__c txn = [SELECT Id, Debit__c, Credit__c, Description__c FROM Bank_Transaction__c LIMIT 1];
        List<Bank_Transaction__c> transactions = new List<Bank_Transaction__c>{ txn };
        
        Test.setMock(HttpCalloutMock.class, new MockOpenAICalloutRateLimit());
        
        try {
            Test.startTest();
            FetchCategoriesByOpenAIHandler.categorizeTransactions(transactions);
            Test.stopTest();
            System.assert(false, 'Expected CalloutException for rate limit error');
        } catch (CalloutException e) {

        }
    }
    
    // ------------------------------------------------------------------
    // 3. TEST processTransactionsInBatch (Background Processing)
    // ------------------------------------------------------------------
    @isTest
    static void testProcessTransactionsInBatch() {
        Bank_Account__c bankAccount = [SELECT Id FROM Bank_Account__c LIMIT 1];
        
        Bank_Transaction__c txn = [SELECT Id, Description__c, Debit__c, Credit__c 
                                  FROM Bank_Transaction__c 
                                  LIMIT 1];
        
        Test.setMock(HttpCalloutMock.class, new MockOpenAICalloutSuccess(txn.Id));
        
        Test.startTest();
        FetchCategoriesByOpenAIHandler.processTransactionsInBatch(
            bankAccount.Id, 
            'All', 
            UserInfo.getUserId()
        );
        Test.stopTest();
        
        // Verify that transactions have been updated by the batch process
        List<Bank_Transaction__c> updatedTxs = [
            SELECT AI_Category__c, Synced_Status__c
            FROM Bank_Transaction__c
            WHERE Bank_Account__c = :bankAccount.Id
        ];
        
    }
    
    // ------------------------------------------------------------------
    // 4. MOCK CLASSES
    // ------------------------------------------------------------------
    private class MockOpenAICalloutSuccess implements HttpCalloutMock {
        private String txnId;

        public MockOpenAICalloutSuccess(String txnId) {
            this.txnId = txnId;
        }

        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse response = new HttpResponse();
            response.setStatusCode(200);

            String contentString = '{"classified_transactions": [{"ai_category": "Overhead", "transaction_type": "debit", "salesforce_id": "' + txnId + '", "confidence_score": 9, "ai_reasoning": "Matched vendor UPWORK for Overhead (business services)."}]}';

            Map<String, Object> message = new Map<String, Object>{
                'content' => contentString,
                'role' => 'assistant'
            };
            Map<String, Object> choice = new Map<String, Object>{
                'message' => message,
                'finish_reason' => 'stop',
                'index' => 0
            };
            Map<String, Object> responseMap = new Map<String, Object>{
                'choices' => new List<Object>{ choice },
                'created' => 1743692516,
                'id' => 'chatcmpl-BIGMm1gNyIZauFqItr78624mooPbd',
                'model' => 'gpt-4o-2024-11-20',
                'object' => 'chat.completion'
            };
            String responseJson = JSON.serialize(responseMap);
            response.setBody(responseJson);
            return response;
        }
    }
    
    private class MockOpenAICalloutFailure implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse response = new HttpResponse();
            response.setStatusCode(400);
            response.setBody('{"error": {"message": "OpenAI API call failed", "code": 400}}');
            return response;
        }
    }
    
    private class MockOpenAICalloutNoValidContent implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse response = new HttpResponse();
            response.setStatusCode(200);
            // Return JSON without valid 'choices' content
            Map<String, Object> mockResponse = new Map<String, Object>{
                'id' => 'dummy-id',
                'model' => 'gpt-4o',
                'created' => 1234567890,
                'object' => 'chat.completion',
                'choices' => new List<Object>()  // empty list
            };
            response.setBody(JSON.serialize(mockResponse));
            return response;
        }
    }
    
    private class MockOpenAICalloutRateLimit implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse response = new HttpResponse();
            response.setStatusCode(400);
            response.setBody('{"error": {"message": "Rate limit exceeded", "code": 429}}');
            return response;
        }
    }
}