public without sharing class mf123opp {
    @AuraEnabled
    public static Opportunity getOpportunityById(Id oppId) {
        return [SELECT Amount,  Account.MF_Assigned_Servicer__r.FirstName,  Account.MF_Assigned_Servicer__r.Email, ExpectedRevenue, CloseDate, Account.Name, Type,
                       LeadSource, Probability, CampaignId, Supporting_Docs__c,
                       Bad_Debt__c, UCC_Filings__c, Bankruptcy__c, Current_Lawsuits__c,
                       Overhead_Debt_Schedule__c, Signed_App__c, 
                       Confirmation_Email__c, of_active_contracts_POs__c,
                       CreatedBy.Name, LastModifiedBy.Name, Description,
                       Valley_Account_Open__c, Loan_Maturity_Date__c, Borrower_Name__c,  
                       Assigned_MF_CRM__c, Assigned_MF_CRM__r.Name,Assigned_MF_CRM__r.FirstName, Assigned_MF_CRM__r.LastName,
                       Assigned_MF_Servicer__c, Assigned_MF_Servicer__r.Name,
                       Assigned_MF_Servicer__r.FirstName,Assigned_MF_Servicer__r.LastName,
                       Assigned_MF_UW__c, Assigned_MF_UW__r.Name, Assigned_MF_UW__r.FirstName,Assigned_MF_UW__r.LastName,
                       Project__c, Project__r.Name, AccountId, Loan_Amount_Requested__c,
                       Name, StageName, Status_Update_for_Client__c
                FROM Opportunity 
                WHERE Id = :oppId];
    }

    @AuraEnabled
    public static Boolean isSandbox() {
        Organization orgInfo = [SELECT Id, Name, OrganizationType, IsSandbox FROM Organization LIMIT 1];
        if (orgInfo.IsSandbox) {
           return true;
        }
        return false;
    }

   @AuraEnabled
   public static String getCurrentUserAccountName() {
        Id userId = UserInfo.getUserId();
        
        User currentUser = [SELECT Account.Name FROM User WHERE Id = :userId LIMIT 1];
        
        String accountName = currentUser.Account != null ? currentUser.Account.Name : 'No associated account';

        return accountName;
    }

    @AuraEnabled
    public static List<sObject> getOpprOfProject(Id projectId, String pgName) {
         String objectName;
          
          if (pgName == 'Project') {
              objectName = 'Project__c';
          } else if (pgName == 'Disbursement request') {
              objectName = 'Disbursement_Request__c';
          }
          else if (pgName == 'Requested Item') {
              objectName = 'Requested_Item__c';
          }
          else if(pgName == 'Contact') {
              objectName = 'Contact';
          }
          else if(pgName == 'Opportunity') {
              objectName = 'Opportunity';
          }
          else {
              objectName = 'Account';
          } 
        
         String query = 'SELECT Id, Name FROM ' + objectName + ' WHERE Id = \'' + projectId + '\'';
         System.debug('SOQL Query: ' + query);
        return Database.query(query);
        //return [Select Id,Name, Loan_Opportunity__c, Account_Name__c, Account_Name__r.Name, owner.Name, Owner.firstName, Owner.LastName, Assigned_MF_Servicer__c, Assigned_MF_Servicer__r.Name,
               // Assigned_MF_Servicer__r.FIrstName, Assigned_MF_Servicer__r.LastName, Project_Number__c,Loan_Opportunity__r.Id, Loan_Status__c, Loan_Opportunity__r.Name FROM project__c WHERE id = 'a09Em0000065Pf4IAE'];
    }

    @AuraEnabled(cacheable=true)
    public static List<Disbursement_Request__c> getDisbursementRequests(Id projectId) {
        return [
            SELECT Id, Name, Amount_Approved__c, Amount_Approved1__c, Disbursement_Type__c, LastModifiedDate, Status__c
            FROM Disbursement_Request__c 
            WHERE Project_lookup__c = :projectId ORDER BY Name DESC
        ];
    }

    @AuraEnabled(cacheable=true)
    public static List<Requested_Item__C> getRequestItems(Id projectId) {
        return [
            SELECT Id, Name, Invoice_Amount__c , Description_Work__c, LastModifiedDate 
            FROM Requested_Item__C 
            WHERE Disbursement_Request__c = :projectId
        ];
    }

    @AuraEnabled(cacheable=true)
    public static List<Contact> getContactItems(Id projectId) {
        return [
            SELECT Id, Name, Email , MobilePhone, Title
            FROM Contact 
            WHERE AccountId = :projectId
        ];
    }

    // @AuraEnabled(cacheable=true)
    // public static List<Project__c> getProjectsForOpportunity(Id opportunityId) {
    //     return [SELECT Id, Name, Project_Number__c,Project_Start_Date__c, Email_for_Servicing_Updates__c FROM Project__c WHERE Loan_Opportunity__c = :opportunityId];
    // }
       @AuraEnabled(cacheable=true)
     public static List<ProjectWrapper> getProjectsForOpportunity(Id opportunityId) {
        List<ProjectWrapper> projectWrappers = new List<ProjectWrapper>();
                    // (SELECT Id FROM FeedItems WHERE ParentId = :opportunityId) 
        List<Project__c> projects = [SELECT Id, Name, MF_Loan_Amount__c, Project_Number__c, Project_Start_Date__c, Loan_Principal__c, Money_Available_to_Draw_on_the_Loan__c, Current_Pay_Off_Balance__c
                                     FROM Project__c 
                                     WHERE Loan_Opportunity__c = :opportunityId];
   
        List<FeedItem> feedItems = [
            SELECT Id, ParentId, Body
            FROM FeedItem
            WHERE ParentId IN :projects
          ];

          // Map to store FeedItem counts per Opportunity
          Map<Id, Integer> feedItemCountMap = new Map<Id, Integer>();
          for (FeedItem item : feedItems) {
            //if (item.Body != null && !item.Body.trim().isEmpty()) {
            if (!String.isBlank(item.Body)) {
              if (feedItemCountMap.containsKey(item.ParentId)) {
                feedItemCountMap.put(item.ParentId, feedItemCountMap.get(item.ParentId) + 1);
              } else {
                feedItemCountMap.put(item.ParentId, 1);
              }
            }
          }

          // Create OpportunityWrapper list
          for (Project__c proj : projects) {
            Integer feedItemCount = feedItemCountMap.get(proj.Id) != null ? feedItemCountMap.get(proj.Id) : 0;
            projectWrappers.add(new ProjectWrapper(proj, feedItemCount));
          }
    
    return projectWrappers;
  }
  
  public class ProjectWrapper {
    @AuraEnabled
    public Project__c project { get; set; }
    @AuraEnabled
    public Integer feedItemCount { get; set; }

    public ProjectWrapper(Project__c proj, Integer feedItemCount) {
      this.project = proj;
      this.feedItemCount = feedItemCount;
    }
  }
}