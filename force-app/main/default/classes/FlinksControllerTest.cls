@isTest
public class FlinksControllerTest {

    @isTest
    static void testGetAccountDetails() {
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Base_Url__c = 'https://api.test.com/',
            Customer_Id__c = '12345',
            Bearer_Token__c = 'testBearerToken'
        );
        insert config;

        Test.setMock(HttpCalloutMock.class, new MockHttpResponseForGetAccountDetails());
        Test.startTest();
        FlinksController.getAccountDetails('mockReqId123');
        Test.stopTest();
    }

    @isTest
    static void testFetchBankAccounts() {
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Base_Url__c = 'https://api.test.com/',
            Customer_Id__c = '12345',
            Bearer_Token__c = 'testBearerToken'
        );
        insert config;

        User loggedInUser = [SELECT Id, ContactId FROM User WHERE Id = :UserInfo.getUserId()];
        Bank_Account__c bankAccount = new Bank_Account__c(
            Name = 'Test Account',
            Institution_Name__c = 'Test Bank',
            Login_ID__c = 'Login123',
            Is_Active__c = true,
            Contact__c = loggedInUser.ContactId
        );
        insert bankAccount;

        Test.setMock(HttpCalloutMock.class, new MockHttpResponseForGetIneligibleCards());
        Test.startTest();
        List<Bank_Account__c> result;
        
        try{
			result = FlinksController.fetchBankAccounts();
        }catch(Exception e){}
        
        Test.stopTest(); 

    }

    @isTest
    static void testGetAccRec() {
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Iframe_Url__c = 'https://iframe.test.com/',
            Base_Url__c = 'https://api.test.com/',
            Customer_Id__c = '12345'
        );
        insert config;

        Test.startTest();
        Map<String, String> result = FlinksController.getAccRec();
        Test.stopTest();

        System.assertEquals('https://iframe.test.com/', result.get('iframeUrl'));
    }

    @isTest
    static void testGetIneligibleCards() {
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Base_Url__c = 'https://api.test.com/',
            Customer_Id__c = '12345',
            Bearer_Token__c = 'testBearerToken'
        );
        insert config;

        Test.setMock(HttpCalloutMock.class, new MockHttpResponseForGetIneligibleCards());
        Test.startTest();
        List<Object> result = FlinksController.getIneligibleCards();
        Test.stopTest();

        System.assertEquals(1, result.size());
    }

    @isTest
    static void testRemoveAccount() {
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Base_Url__c = 'https://api.test.com/',
            Customer_Id__c = '12345',
            Bearer_Token__c = 'testBearerToken'
        );
        insert config;

        Bank_Account__c bankAccount = new Bank_Account__c(
            Name = 'Test Account',
            Institution_Name__c = 'Test Bank',
            Login_ID__c = 'Login123',
            Is_Active__c = true
        );
        insert bankAccount;

        Test.setMock(HttpCalloutMock.class, new MockHttpResponseForDeleteCard());
        Test.startTest();
        FlinksController.removeAccount('Login123');
        Test.stopTest();

        System.assertEquals(0, [SELECT COUNT() FROM Bank_Account__c]);
    }

    private class MockHttpResponseForGetAccountDetails implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setBody('{}');
            return res;
        }
    }

    private class MockHttpResponseForGetIneligibleCards implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setBody('{"IneligibleCards":[{"LoginId":"Login123","LastRefreshErrorCode":"INVALID_PASSWORD"}]}');
            return res;
        }
    }

    private class MockHttpResponseForDeleteCard implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setBody('{}');
            return res;
        }
    }
}