@isTest
private class CashFlowConfigControllerTest {

    @isTest
    static void testGetCashFlowBaseUrl_ReturnsCorrectUrl() {
        Test.startTest();
        String actualReturnedUrl = CashFlowConfigController.getCashFlowBaseUrl();
        Test.stopTest();

        // --- Logic to determine the expected URL within the test ---
        String expectedOrgType;
        List<Organization> orgInfoList = [SELECT IsSandbox FROM Organization LIMIT 1];
        if (!orgInfoList.isEmpty() && orgInfoList[0].IsSandbox) {
            expectedOrgType = 'Sandbox';
        } else {
            expectedOrgType = 'Production';
        }

        String settingNameForQuery = 'CashFlowBaseUrl';
        String defaultUrlFromController = 'https://mobilizationfunding.com/portal/cash-flow-embed/';
        String expectedUrl;

        // Try to query the Custom Metadata like the controller would
        List<MF_Config__mdt> matchingConfigs = [
            SELECT Value__c
            FROM MF_Config__mdt
            WHERE Org_Type__c = :expectedOrgType AND Setting_Name__c = :settingNameForQuery
            LIMIT 1
        ];

        if (!matchingConfigs.isEmpty() && String.isNotBlank(matchingConfigs[0].Value__c)) {
            String cmtdValue = matchingConfigs[0].Value__c;
            // Ensure the expected URL also has the trailing slash logic applied
            expectedUrl = cmtdValue.endsWith('/') ? cmtdValue : cmtdValue + '/';
        } else {
            // If no record found, or Value__c is blank, controller returns defaultUrl
            expectedUrl = defaultUrlFromController;
        }
        // --- End of logic to determine the expected URL ---

        // Assertions
        System.assertNotEquals(null, actualReturnedUrl, 'The returned URL should not be null.');
        System.assert(actualReturnedUrl.endsWith('/'), 'The returned URL must end with a forward slash. Actual: ' + actualReturnedUrl);
        System.assertEquals(expectedUrl, actualReturnedUrl,
            'The CashFlowConfigController.getCashFlowBaseUrl() did not return the expected URL. ' +
            'Expected based on org type "' + expectedOrgType + '" and CMDT records: "' + expectedUrl +
            '". Actual: "' + actualReturnedUrl + '".');

        System.debug('Test: Determined Org Type: ' + expectedOrgType);
        System.debug('Test: Expected URL: ' + expectedUrl);
        System.debug('Test: Actual URL Returned: ' + actualReturnedUrl);
    }

}