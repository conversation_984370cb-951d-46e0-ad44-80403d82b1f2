public class ContentVersionTriggerHandler {
    
    public static void createActivity(List<ContentVersion> versions){
        
        User usr = [SELECT Id,contactId,AccountId FROM User WHERE Id =: userinfo.getUserId()];
        
        List<Activity_Logger__c> activities = new List<Activity_Logger__c>();
        Set<Id> projectIds = new Set<Id>();
        Set<Id> disbursementIds = new Set<Id>();
        Set<Id> oppIds = new Set<Id>();
        
        Schema.SObjectType projectObject = Schema.Project__c.getSObjectType();
        Schema.SObjectType disbursementObject = Schema.Disbursement_Request__c.getSObjectType();
        
        for(ContentVersion version : versions){
            if(version.FirstPublishLocationId != null){
                if(version.FirstPublishLocationId.getsobjecttype() == disbursementObject){
                    disbursementIds.add(version.FirstPublishLocationId);
                }else if(version.FirstPublishLocationId.getsobjecttype() == projectObject){
                    projectIds.add(version.FirstPublishLocationId);
                }else if(String.valueOf(version.FirstPublishLocationId).startsWith('006')){
                    oppIds.add(version.FirstPublishLocationId);
                }
            }
        }
        
        if(!projectIds.isEmpty() || !disbursementIds.isEmpty() || !disbursementIds.isEmpty()){
            
            Map<Id,Project__c> projectMap = new Map<Id,Project__c>();
            Map<Id,Disbursement_Request__c> disbursementMap = new Map<Id,Disbursement_Request__c>();
            Map<Id,Opportunity> oppMap = new Map<Id,Opportunity>();                
            
            if(!projectIds.isEmpty()){
                projectMap = new Map<Id,Project__c>([SELECT Id,Name,Loan_Opportunity__r.AccountId FROM Project__c WHERE Id IN: projectIds]);
            }
            if(!disbursementIds.isEmpty()){
                disbursementMap = new Map<Id,Disbursement_Request__c>([SELECT Id,Name,Project_lookup__r.Loan_Opportunity__r.AccountId FROM Disbursement_Request__c WHERE Id IN: disbursementIds]);
            }
            if(!oppIds.isEmpty()){
                oppMap = new Map<Id,Opportunity>([SELECT Id,Name,AccountId FROM Opportunity WHERE Id IN: oppIds]);
            }
            
            for(ContentVersion version : versions){
                if(version.FirstPublishLocationId != null){
                    Activity_Logger__c al = new Activity_Logger__c(Related_Record__c = version.FirstPublishLocationId,Activity_Time__c = system.now(),Activity_Type__c = 'File Uploaded',Contact__c=usr.contactId,User__c=usr.Id);
                    
                    if(projectMap.containsKey(version.FirstPublishLocationId)){
                        al.Item__c = projectMap.get(version.FirstPublishLocationId).Name;
                        al.Account__c = projectMap.get(version.FirstPublishLocationId).Loan_Opportunity__r.AccountId;
                    }else if(disbursementMap.containsKey(version.FirstPublishLocationId)){
                        al.Item__c = disbursementMap.get(version.FirstPublishLocationId).Name;
                        al.Account__c = disbursementMap.get(version.FirstPublishLocationId).Project_lookup__r.Loan_Opportunity__r.AccountId;
                    }else if(oppMap.containsKey(version.FirstPublishLocationId)){
                        al.Item__c = oppMap.get(version.FirstPublishLocationId).Name;
                        al.Account__c = oppMap.get(version.FirstPublishLocationId).AccountId;
                    }
                    al.Item__c = projectMap.containsKey(version.FirstPublishLocationId) ? projectMap.get(version.FirstPublishLocationId).Name : disbursementMap.containsKey(version.FirstPublishLocationId) ? disbursementMap.get(version.FirstPublishLocationId).Name : null;
                    activities.add(al);
                }
            }
            
            if(!activities.isEmpty()){
                insert activities;
            }
        }
        
        
    }
    
}