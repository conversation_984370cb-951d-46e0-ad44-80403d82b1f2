# This workflow generates a FEATURE TEST PACKAGE from the latest Commit SHA.
# It only serves to validate the deployment and packageability of the latest commit.
# It does NOT affect the version of the package and is not included in the package ancestry.
# To generate a new Beta Package Version, you need to run the `Beta (Unlocked) - Create` workflow.

name: Feature - Test (Unlocked)

on:
  workflow_dispatch:
  pull_request:
    branches:
      - feature/**
      - main

jobs:
  feature-test:
    if: startsWith(github.head_ref, 'feature/')
    uses: nimba-actions/standard-workflows/.github/workflows/feature-unlocked.yml@main
    secrets:
      dev-hub-auth-url: ${{ secrets.DEV_HUB_AUTH_URL }}
    with:
      cumulusci-version: "3.90.0"
      sfdx-version: "7.209.6"

  apex-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Use Node.js 20.x
        uses: actions/setup-node@v3
        with:
          node-version: "20.x"

      - name: Install Salesforce CLI and Scanner
        run: |
          npm install @salesforce/cli -g
          sf plugins install @salesforce/sfdx-scanner@latest

      - name: Run SFDX Scanner - Report findings as comments
        uses: mitchspano/sfdx-scan-pull-request@v0.1.16
        with:
          pmdconfig: pmd-rules.xml
          severity-threshold: 4
          strictly-enforced-rules: '[{ "engine": "pmd", "category": "Performance", "rule": "AvoidDebugStatements" }]'
          report-mode: comments
          delete-resolved-comments: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

permissions:
  actions: write
  attestations: write
  checks: write
  contents: write
  deployments: write
  discussions: write
  issues: write
  packages: write
  pages: write
  pull-requests: write
  repository-projects: write
  security-events: write
  statuses: write
